import {
  AuthService
} from "./chunk-QNBL54OW.js";
import {
  BehaviorSubject,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-THPQGTPB.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-S35DAJRX.js";

// src/app/core/services/user.service.ts
var UserService = class _UserService {
  authService;
  supabase;
  usersSubject = new BehaviorSubject([]);
  users$ = this.usersSubject.asObservable();
  constructor(authService) {
    this.authService = authService;
    this.supabase = authService.supabase;
  }
  getAllUsers() {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this.supabase.from("profiles").select("*").order("created_at", { ascending: false });
        if (error) {
          throw error;
        }
        this.usersSubject.next(data);
        return data;
      } catch (error) {
        console.error("Error fetching users:", error);
        return [];
      }
    });
  }
  getUsersByRole(role) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this.supabase.from("profiles").select("*").eq("role", role).order("created_at", { ascending: false });
        if (error) {
          throw error;
        }
        return data;
      } catch (error) {
        console.error(`Error fetching ${role}s:`, error);
        return [];
      }
    });
  }
  approveDriver(userId) {
    return __async(this, null, function* () {
      try {
        const { error } = yield this.supabase.from("profiles").update({
          is_approved: "TRUE"
          //  is_active: true // Automatically activate when approved
        }).eq("id", userId);
        if (error) {
          throw error;
        }
        const currentUsers = this.usersSubject.value;
        const updatedUsers = currentUsers.map((user) => user.id === userId ? __spreadProps(__spreadValues({}, user), { is_approved: true, is_active: true }) : user);
        this.usersSubject.next(updatedUsers);
        return true;
      } catch (error) {
        console.error("Error approving driver:", error);
        return false;
      }
    });
  }
  updateUserStatus(userId, isActive) {
    return __async(this, null, function* () {
      try {
        const { data: user, error: userError } = yield this.supabase.from("profiles").select("role, is_approved").eq("id", userId).single();
        if (userError)
          throw userError;
        if (isActive && user.role !== "admin" && !user.is_approved) {
          throw new Error("Cannot activate unapproved user");
        }
        const { error } = yield this.supabase.from("profiles").update({
          is_approved: isActive,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }).eq("id", userId);
        if (error)
          throw error;
        const currentUsers = this.usersSubject.value;
        const updatedUsers = currentUsers.map((u) => u.id === userId ? __spreadProps(__spreadValues({}, u), { is_approved: isActive }) : u);
        this.usersSubject.next(updatedUsers);
        return true;
      } catch (error) {
        console.error("Error updating user status:", error);
        return false;
      }
    });
  }
  getUserById(userId) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this.supabase.from("profiles").select("*").eq("id", userId).single();
        if (error) {
          throw error;
        }
        return data;
      } catch (error) {
        console.error("Error fetching user by ID:", error);
        return null;
      }
    });
  }
  filterUsers(users, filter) {
    return users.filter((user) => {
      if (filter.role && user.role !== filter.role) {
        return false;
      }
      if (filter.isApproved !== void 0 && user.role === "driver") {
        if (user.is_approved !== filter.isApproved) {
          return false;
        }
      }
      if (filter.searchTerm) {
        const searchTerm = filter.searchTerm.toLowerCase();
        const fullName = user.full_name?.toLowerCase() || "";
        const email = user.email.toLowerCase();
        const phone = user.phone?.toLowerCase() || "";
        if (!fullName.includes(searchTerm) && !email.includes(searchTerm) && !phone.includes(searchTerm)) {
          return false;
        }
      }
      return true;
    });
  }
  static \u0275fac = function UserService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserService)(\u0275\u0275inject(AuthService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _UserService, factory: _UserService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }], null);
})();

// src/app/core/services/sms.service.ts
var SmsService = class _SmsService {
  userService;
  authService;
  supabase;
  constructor(userService, authService) {
    this.userService = userService;
    this.authService = authService;
    this.supabase = authService.supabase;
  }
  /**
   * Send an SMS notification to a user via Supabase Twilio lambda function
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @returns Promise resolving to the message SID if successful
   */
  sendSms(to, body) {
    return __async(this, null, function* () {
      try {
        const formattedPhone = this.formatPhoneNumber(to);
        const { data, error } = yield this.supabase.functions.invoke("twilio", {
          body: {
            to: formattedPhone,
            message: body,
            from: "+17272025413"
          }
        });
        if (error) {
          console.error("Error calling Twilio lambda function:", error);
          throw error;
        }
        if (!data || !data.sid) {
          throw new Error("No message SID returned from Twilio lambda function");
        }
        console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);
        return data.sid;
      } catch (error) {
        console.error("Error sending SMS:", error);
        throw error;
      }
    });
  }
  /**
   * Send ride assignment notifications to both rider and driver
   * @param ride The ride that was assigned
   * @param driverId The ID of the driver assigned to the ride
   */
  sendRideAssignmentNotifications(ride, driverId) {
    return __async(this, null, function* () {
      try {
        const [rider, driver] = yield Promise.all([
          this.userService.getUserById(ride.rider_id),
          this.userService.getUserById(driverId)
        ]);
        if (!rider || !driver) {
          throw new Error("Could not find rider or driver information");
        }
        if (!rider.phone || !driver.phone) {
          console.warn("Phone number missing for rider or driver. SMS notification skipped.");
          return;
        }
        const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit"
        });
        yield this.sendEnhancedDriverAssignedNotification(ride, driver);
        const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || "your rider"} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;
        const results = yield Promise.allSettled([
          this.sendSmsWithRetry(driver.phone, driverMessage)
        ]);
        const [driverResult] = results;
        if (driverResult.status === "rejected") {
          console.error("Failed to send SMS to driver:", driverResult.reason);
        } else {
          console.log("Ride assignment notification sent successfully to driver");
        }
      } catch (error) {
        console.error("Error sending ride assignment notifications:", error);
      }
    });
  }
  /**
   * Send SMS with retry logic for better reliability
   * @param to Phone number to send the SMS to
   * @param body Message body
   * @param maxRetries Maximum number of retry attempts
   * @returns Promise resolving to the message SID if successful
   */
  sendSmsWithRetry(to, body, maxRetries = 2) {
    return __async(this, null, function* () {
      let lastError;
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          if (attempt > 0) {
            yield new Promise((resolve) => setTimeout(resolve, 1e3 * Math.pow(2, attempt - 1)));
          }
          return yield this.sendSms("+1" + to, body);
        } catch (error) {
          lastError = error;
          console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);
        }
      }
      throw lastError || new Error("Failed to send SMS after multiple attempts");
    });
  }
  /**
   * Send ride status update notifications to both rider and driver
   * @param ride The ride that had its status updated
   * @param newStatus The new status of the ride
   */
  sendRideStatusUpdateNotifications(ride, newStatus) {
    return __async(this, null, function* () {
      try {
        if (!ride.rider_id || !ride.driver_id) {
          console.warn("Ride is missing rider or driver ID. Status update notification skipped.");
          return;
        }
        const [rider, driver] = yield Promise.all([
          this.userService.getUserById(ride.rider_id),
          this.userService.getUserById(ride.driver_id)
        ]);
        if (!rider || !driver) {
          throw new Error("Could not find rider or driver information");
        }
        if (!rider.phone || !driver.phone) {
          console.warn("Phone number missing for rider or driver. Status update notification skipped.");
          return;
        }
        let riderMessage = "";
        let driverMessage = "";
        switch (newStatus) {
          case "in-progress":
            riderMessage = `Your ride has started. Your driver ${driver.full_name || "is"} on the way to ${ride.dropoff_location}.`;
            driverMessage = `You have started the ride with ${rider.full_name || "your rider"}. Destination: ${ride.dropoff_location}.`;
            break;
          case "completed":
            riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;
            driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;
            break;
          case "canceled":
            riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;
            driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;
            break;
          default:
            return;
        }
        const results = yield Promise.allSettled([
          this.sendSmsWithRetry(rider.phone, riderMessage),
          this.sendSmsWithRetry(driver.phone, driverMessage)
        ]);
        const [riderResult, driverResult] = results;
        if (riderResult.status === "rejected") {
          console.error("Failed to send status update SMS to rider:", riderResult.reason);
        }
        if (driverResult.status === "rejected") {
          console.error("Failed to send status update SMS to driver:", driverResult.reason);
        }
        if (riderResult.status === "fulfilled" && driverResult.status === "fulfilled") {
          console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);
        }
      } catch (error) {
        console.error("Error sending ride status update notifications:", error);
      }
    });
  }
  /**
   * Send ride booking confirmation to the rider
   * @param ride The ride that was booked
   */
  sendRideBookingConfirmation(ride) {
    return __async(this, null, function* () {
      try {
        const rider = yield this.userService.getUserById(ride.rider_id);
        if (!rider || !rider.phone) {
          console.warn("Rider not found or phone number missing. Booking confirmation SMS skipped.");
          return;
        }
        const pickupDate = new Date(ride.pickup_time);
        const isASAP = pickupDate.getTime() <= Date.now() + 30 * 60 * 1e3;
        const pickupTimeStr = isASAP ? "ASAP" : pickupDate.toLocaleString();
        const pickupDateStr = pickupDate.toLocaleDateString();
        const fareStr = ride.fare ? `$${ride.fare.toFixed(2)}` : "TBD";
        const message = `Your ride has been booked!
Pickup: ${ride.pickup_location}
Dropoff: ${ride.dropoff_location}
Pick up date: ${pickupDateStr}
Pickup Time: ${pickupTimeStr}
Fare Estimate: ${fareStr}
Driver will be assigned shortly. You'll receive updates as your ride details are confirmed.
Msg & data rates may apply. Message frequency varies.
Reply STOP to unsubscribe. View our policy at: https://bookholyrides.com/?p=1163`;
        yield this.sendSmsWithRetry(rider.phone, message);
        console.log("Ride booking confirmation sent to rider");
      } catch (error) {
        console.error("Error sending ride booking confirmation:", error);
      }
    });
  }
  /**
   * Send general ride booking notification to all drivers
   * @param ride The ride that was booked
   */
  sendRideBookingNotificationToDrivers(ride) {
    return __async(this, null, function* () {
      try {
        const drivers = yield this.userService.getUsersByRole("driver");
        const approvedDrivers = drivers.filter((driver) => driver.is_approved && driver.phone);
        if (approvedDrivers.length === 0) {
          console.warn("No approved drivers with phone numbers found. Driver notification skipped.");
          return;
        }
        const message = `A new trip has been booked! Login now at https://app.bookholyrides.com to view the ride details in the Holy Rides app.`;
        const results = yield Promise.allSettled(approvedDrivers.map((driver) => this.sendSmsWithRetry(driver.phone, message)));
        const successful = results.filter((r) => r.status === "fulfilled").length;
        const failed = results.filter((r) => r.status === "rejected").length;
        console.log(`Ride booking notification sent to ${successful} drivers, ${failed} failed`);
      } catch (error) {
        console.error("Error sending ride booking notifications to drivers:", error);
      }
    });
  }
  /**
   * Send enhanced driver assigned notification to rider
   * @param ride The ride with assigned driver
   * @param driver The assigned driver information
   */
  sendEnhancedDriverAssignedNotification(ride, driver) {
    return __async(this, null, function* () {
      try {
        const rider = yield this.userService.getUserById(ride.rider_id);
        if (!rider || !rider.phone) {
          console.warn("Rider not found or phone number missing. Driver assigned notification skipped.");
          return;
        }
        const vehicleInfo = driver.vehicle_info || "Vehicle details will be provided";
        const licenseNumber = driver.license_number || "License details will be provided";
        const eta = "ETA will be calculated";
        const message = `Great news \u2014 a driver has been assigned to your trip! \u{1F698}
Driver Name: ${driver.full_name || "Driver"}
Vehicle: ${vehicleInfo}
License Number: ${licenseNumber}
ETA: ${eta}
Msg & data rates may apply. Reply STOP to unsubscribe.`;
        yield this.sendSmsWithRetry(rider.phone, message);
        console.log("Enhanced driver assigned notification sent to rider");
      } catch (error) {
        console.error("Error sending enhanced driver assigned notification:", error);
      }
    });
  }
  /**
   * Send ride cancellation notifications
   * @param ride The cancelled ride
   */
  sendRideCancellationNotifications(ride) {
    return __async(this, null, function* () {
      try {
        const notifications = [];
        const rider = yield this.userService.getUserById(ride.rider_id);
        if (rider && rider.phone) {
          const riderMessage = `Your ride from ${ride.pickup_location} to ${ride.dropoff_location} has been cancelled. You can book a new ride at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;
          notifications.push(this.sendSmsWithRetry(rider.phone, riderMessage));
        }
        if (ride.driver_id) {
          const driver = yield this.userService.getUserById(ride.driver_id);
          if (driver && driver.phone) {
            const driverMessage = `The ride to ${ride.dropoff_location} has been cancelled. Please check your dashboard for new ride opportunities at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;
            notifications.push(this.sendSmsWithRetry(driver.phone, driverMessage));
          }
        }
        if (notifications.length > 0) {
          const results = yield Promise.allSettled(notifications);
          const successful = results.filter((r) => r.status === "fulfilled").length;
          const failed = results.filter((r) => r.status === "rejected").length;
          console.log(`Ride cancellation notifications: ${successful} sent, ${failed} failed`);
        }
      } catch (error) {
        console.error("Error sending ride cancellation notifications:", error);
      }
    });
  }
  /**
   * Send ride completion notification to rider with payment instructions
   * @param ride The completed ride
   */
  sendRideCompletionNotification(ride) {
    return __async(this, null, function* () {
      try {
        const rider = yield this.userService.getUserById(ride.rider_id);
        if (!rider || !rider.phone) {
          console.warn("Rider not found or phone number missing. Ride completion notification skipped.");
          return;
        }
        const fareAmount = ride.fare ? `$${ride.fare.toFixed(2)}` : "$0.00";
        const message = `Your ride is complete! \u{1F698}
Thanks for riding with Holy Rides. Please complete payment now in the amount of ${fareAmount} using one of the following methods:
Cash app: https://cash.app/$HolyRides24
Square: https://square.link/u/o9zzuiAv?src=sheet
Msg & data rates may apply. Reply STOP to unsubscribe.`;
        yield this.sendSmsWithRetry(rider.phone, message);
        console.log("Ride completion notification sent to rider");
      } catch (error) {
        console.error("Error sending ride completion notification:", error);
      }
    });
  }
  /**
   * Send payment confirmation notification to rider
   * @param ride The ride that was paid for
   * @param paymentAmount The amount that was paid
   */
  sendPaymentConfirmationNotification(ride, paymentAmount) {
    return __async(this, null, function* () {
      try {
        const rider = yield this.userService.getUserById(ride.rider_id);
        if (!rider || !rider.phone) {
          console.warn("Rider not found or phone number missing. Payment confirmation notification skipped.");
          return;
        }
        const pickupDate = new Date(ride.pickup_time);
        const isASAP = pickupDate.getTime() <= Date.now() + 30 * 60 * 1e3;
        const pickupTimeStr = isASAP ? "ASAP" : pickupDate.toLocaleString();
        const pickupDateStr = pickupDate.toLocaleDateString();
        const amountStr = `$${paymentAmount.toFixed(2)}`;
        const message = `Thank you! \u{1F389}
We've received your payment of ${amountStr} for your recent ride.
Pickup: ${ride.pickup_location}
Dropoff: ${ride.dropoff_location}
Pick up date: ${pickupDateStr}
Pickup Time: ${pickupTimeStr}

Thanks for riding with Holy Rides. See you next time!
Msg & data rates may apply. Reply STOP to unsubscribe.`;
        yield this.sendSmsWithRetry(rider.phone, message);
        console.log("Payment confirmation notification sent to rider");
      } catch (error) {
        console.error("Error sending payment confirmation notification:", error);
      }
    });
  }
  /**
   * Send admin notification when a ride status changes to started or completed
   * @param ride The ride that had its status updated
   * @param newStatus The new status of the ride ('in-progress' or 'completed')
   */
  sendAdminRideStatusNotification(ride, newStatus) {
    return __async(this, null, function* () {
      try {
        const adminPhoneNumber = "7272652963";
        const [driver, rider] = yield Promise.all([
          ride.driver_id ? this.userService.getUserById(ride.driver_id) : null,
          this.userService.getUserById(ride.rider_id)
        ]);
        const pickupTime = new Date(ride.pickup_time).toLocaleString();
        let statusText = "";
        if (newStatus === "in-progress") {
          statusText = "STARTED";
        } else if (newStatus === "completed") {
          statusText = "COMPLETED";
        } else {
          return;
        }
        const driverName = driver?.full_name || "Unknown Driver";
        const riderName = rider?.full_name || "Unknown Rider";
        const message = `\u{1F698} RIDE ${statusText}
Driver: ${driverName}
Rider: ${riderName}
From: ${ride.pickup_location}
To: ${ride.dropoff_location}
Pickup Time: ${pickupTime}
${ride.fare ? `Fare: $${ride.fare.toFixed(2)}` : ""}`;
        yield this.sendSmsWithRetry(adminPhoneNumber, message);
        console.log(`Admin notification sent for ride ${statusText.toLowerCase()}`);
      } catch (error) {
        console.error("Error sending admin ride status notification:", error);
      }
    });
  }
  /**
   * Format phone number to ensure it has the international format with + prefix
   * @param phoneNumber The phone number to format
   * @returns Formatted phone number
   */
  formatPhoneNumber(phoneNumber) {
    if (phoneNumber.startsWith("+")) {
      return phoneNumber;
    }
    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {
      return `+${phoneNumber}`;
    }
    return `+1${phoneNumber.replace(/\D/g, "")}`;
  }
  static \u0275fac = function SmsService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SmsService)(\u0275\u0275inject(UserService), \u0275\u0275inject(AuthService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _SmsService, factory: _SmsService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SmsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: UserService }, { type: AuthService }], null);
})();

export {
  UserService,
  SmsService
};
//# sourceMappingURL=chunk-NVGZCMKL.js.map
