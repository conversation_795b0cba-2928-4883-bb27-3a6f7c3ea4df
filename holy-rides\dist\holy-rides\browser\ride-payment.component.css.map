{"version": 3, "sources": ["src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    .payment-details {\n      margin-bottom: 20px;\n    }\n\n    .detail-row {\n      display: flex;\n      margin-bottom: 8px;\n      align-items: center;\n    }\n\n    .label {\n      font-weight: 500;\n      width: 120px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .value {\n      flex: 1;\n    }\n\n    .amount {\n      font-size: 1.2em;\n      font-weight: 500;\n      margin-top: 16px;\n    }\n\n    .amount .value {\n      color: #3f51b5;\n    }\n\n    .status-badge {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 4px;\n      text-transform: capitalize;\n      font-size: 0.9em;\n    }\n\n    .status-requested {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n      color: white;\n    }\n\n    .status-in-progress {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .payment-paid, .payment-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .payment-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-refunded {\n      background-color: #9e9e9e;\n      color: white;\n    }\n\n    .payment-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      align-items: flex-start;\n      margin-top: 16px;\n    }\n\n    .processing-indicator {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 8px;\n    }\n\n    .section-divider {\n      margin: 24px 0;\n    }\n\n    .stripe-payment-form {\n      margin-top: 24px;\n    }\n\n    .payment-instruction {\n      margin-bottom: 16px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .card-element {\n      border: 1px solid #e0e0e0;\n      border-radius: 4px;\n      padding: 12px;\n      background-color: white;\n      margin-bottom: 16px;\n    }\n\n    .card-errors {\n      color: #f44336;\n      font-size: 0.9em;\n      margin-bottom: 16px;\n    }\n\n    .sdk-status {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 16px;\n      margin: 24px 0;\n    }\n\n    .payment-result {\n      margin-top: 24px;\n      padding: 16px;\n      border-radius: 4px;\n      background-color: #f5f5f5;\n    }\n\n    .success-message {\n      color: #4caf50;\n      font-weight: 500;\n    }\n\n    .error-message {\n      color: #f44336;\n      font-weight: 500;\n    }\n  "], "mappings": ";AACI;AACE,WAAA;AACA,UAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,eAAA;AACA,SAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,QAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,cAAA;;AAGF,CANA,OAMA,CAVA;AAWE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,kBAAA;AACA,aAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AAAA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,eAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,iBAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,iBAAA;AACA,oBAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;", "names": []}