{"version": 3, "sources": ["src/app/core/services/ride.service.ts", "node_modules/@angular/material/fesm2022/divider.mjs", "node_modules/@angular/material/fesm2022/progress-spinner.mjs", "src/app/shared/components/ride-chat/ride-chat.component.ts"], "sourcesContent": ["import { Injectable, OnD<PERSON>roy } from '@angular/core';\nimport { SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { Ride, RideFilter, RideStatus } from '../models/ride.model';\nimport { SmsService } from './sms.service';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RideService implements OnDestroy {\n  private _supabase: SupabaseClient;\n  private ridesSubject = new BehaviorSubject<Ride[]>([]);\n  rides$ = this.ridesSubject.asObservable();\n  private rideSubscription: RealtimeChannel | null = null;\n\n  // Expose supabase client for use in other components\n  get supabase(): SupabaseClient {\n    return this._supabase;\n  }\n\n  constructor(\n    private smsService: SmsService,\n    private authService: AuthService\n  ) {\n    this._supabase = authService.supabase;\n    this.initializeRealTimeSubscription();\n  }\n\n  ngOnDestroy(): void {\n    // Clean up realtime subscription\n    if (this.rideSubscription) {\n      console.log('🧹 Cleaning up rides realtime subscription');\n      this.rideSubscription.unsubscribe();\n      this.rideSubscription = null;\n    }\n  }\n\n  private initializeRealTimeSubscription() {\n    // Clean up existing subscription if any\n    if (this.rideSubscription) {\n      this.rideSubscription.unsubscribe();\n    }\n\n    // Subscribe to changes in the rides table\n    this.rideSubscription = this._supabase\n      .channel('rides-channel')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'rides' },\n        async (payload) => {\n          console.log('🔄 Realtime event received:', payload.eventType, payload.new || payload.old);\n\n          // Refresh rides when there's a change\n          try {\n            await this.refreshRides();\n            console.log('✅ Rides refreshed successfully after realtime event');\n          } catch (error) {\n            console.error('❌ Error refreshing rides after realtime event:', error);\n          }\n        }\n      )\n      .subscribe((status) => {\n        console.log('📡 Realtime subscription status:', status);\n        if (status === 'SUBSCRIBED') {\n          console.log('✅ Successfully subscribed to rides realtime updates');\n        } else if (status === 'CHANNEL_ERROR') {\n          console.error('❌ Realtime subscription error, attempting to reconnect...');\n          // Attempt to reconnect after a delay\n          setTimeout(() => {\n            this.initializeRealTimeSubscription();\n          }, 5000);\n        }\n      });\n  }\n\n  private async refreshRides(): Promise<void> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      this.ridesSubject.next(data || []);\n    } catch (error) {\n      console.error('Error refreshing rides:', error);\n      // Don't update subject on error to preserve last known good state\n    }\n  }\n\n  async getAllRides(): Promise<Ride[]> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      this.ridesSubject.next(data || []);\n      return data || [];\n    } catch (error) {\n      console.error('Error fetching all rides:', error);\n      return this.ridesSubject.value; // Return cached data on error\n    }\n  }\n\n  async getRidesByStatus(status: RideStatus): Promise<Ride[]> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .eq('status', status)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error(`Error fetching rides with status ${status}:`, error);\n      return [];\n    }\n  }\n\n  async assignRideToDriver(rideId: string, driverId: string): Promise<boolean> {\n    try {\n      // First check if the ride is still in requested or assigned status\n      const currentRide = await this.getRide(rideId);\n      if (!currentRide || (currentRide.status !== 'requested' && currentRide.status !== 'assigned')) {\n        throw new Error('Ride is no longer available for assignment');\n      }\n\n      const { error } = await this._supabase\n        .from('rides')\n        .update({\n          driver_id: driverId,\n          status: 'assigned',\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', rideId)\n        .in('status', ['requested', 'assigned']); // Allow both statuses\n\n      if (error) throw error;\n\n      // Refresh rides data\n      //await this.refreshRides();\n\n      // Get the updated ride with the driver assigned\n      const updatedRide = await this.getRide(rideId);\n      if (updatedRide) {\n        // Send SMS notifications to rider and driver\n        // Use setTimeout to ensure ride assignment completes even if SMS sending takes time\n        // This makes the SMS sending non-blocking for the ride assignment process\n        setTimeout(async () => {\n          try {\n            await this.smsService.sendRideAssignmentNotifications(updatedRide, driverId);\n          } catch (smsError) {\n            // Log the error but don't fail the ride assignment\n            console.error('Error sending SMS notifications:', smsError);\n          }\n        }, 0);\n\n        // Log that notifications are being sent\n        console.log(`Sending ride assignment notifications for ride ${rideId} to rider and driver ${driverId}`);\n      }\n\n      return true;\n    } catch (error) {\n      console.error('Error assigning ride to driver:', error);\n      throw error; // Let caller handle the error\n    }\n  }\n\n  async updateRideStatus(rideId: string, status: RideStatus): Promise<boolean> {\n    try {\n      // First check if the ride exists and status transition is valid\n      const currentRide = await this.getRide(rideId);\n      if (!currentRide) {\n        throw new Error('Ride not found');\n      }\n\n      // Validate status transition\n      if (!this.isValidStatusTransition(currentRide.status, status)) {\n        throw new Error(`Invalid status transition from ${currentRide.status} to ${status}`);\n      }\n\n      const { error } = await this._supabase\n        .from('rides')\n        .update({\n          status,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', rideId);\n\n      if (error) throw error;\n\n      // Refresh rides data\n      await this.refreshRides();\n\n      // Get the updated ride to send notifications\n      const updatedRide = await this.getRide(rideId);\n      if (updatedRide) {\n        // Send status update notifications in a non-blocking way\n        setTimeout(async () => {\n          try {\n            if (status === 'canceled') {\n              // Use the new enhanced cancellation notification\n              await this.smsService.sendRideCancellationNotifications(updatedRide);\n            } else if (status === 'completed') {\n              // Send ride completion notification with payment instructions\n              await this.smsService.sendRideCompletionNotification(updatedRide);\n              // Send admin notification for completed ride\n              await this.smsService.sendAdminRideStatusNotification(updatedRide, status);\n            } else if (status === 'in-progress') {\n              // Use existing status update notifications for in-progress\n              await this.smsService.sendRideStatusUpdateNotifications(updatedRide, status);\n              // Send admin notification for started ride\n              await this.smsService.sendAdminRideStatusNotification(updatedRide, status);\n            }\n          } catch (smsError) {\n            // Log the error but don't fail the status update\n            console.error('Error sending status update notifications:', smsError);\n          }\n        }, 0);\n\n        // Log that notifications are being sent\n        console.log(`Sending ride status update (${status}) notifications for ride ${rideId}`);\n      }\n\n      return true;\n    } catch (error) {\n      console.error('Error updating ride status:', error);\n      throw error; // Let caller handle the error\n    }\n  }\n\n  private isValidStatusTransition(from: RideStatus, to: RideStatus): boolean {\n    const transitions: { [key in RideStatus]: RideStatus[] } = {\n      'requested': ['assigned', 'canceled'],\n      'assigned': ['in-progress', 'canceled'],\n      'in-progress': ['completed', 'canceled'],\n      'completed': [],\n      'canceled': []\n    };\n\n    return transitions[from]?.includes(to) || false;\n  }\n\n  async createRide(ride: Omit<Ride, 'id' | 'created_at' | 'updated_at'>): Promise<Ride> {\n    try {\n      // Set default payment status to pending if not provided\n      const rideWithPaymentStatus = {\n        ...ride,\n        payment_status: ride.payment_status || 'pending'\n      };\n\n      const { data, error } = await this._supabase\n        .from('rides')\n        .insert([rideWithPaymentStatus])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Update local rides state\n      const currentRides = this.ridesSubject.value;\n      this.ridesSubject.next([...currentRides, data]);\n\n      // Send booking notifications in a non-blocking way\n      setTimeout(async () => {\n        try {\n          // Send confirmation to rider\n       //   await this.smsService.sendRideBookingConfirmation(data);\n\n          // Send notification to all drivers\n        //  await this.smsService.sendRideBookingNotificationToDrivers(data);\n        } catch (smsError) {\n          // Log the error but don't fail the ride creation\n          console.error('Error sending ride booking notifications:', smsError);\n        }\n      }, 0);\n\n      console.log(`Sending ride booking notifications for ride ${data.id}`);\n\n      return data;\n    } catch (error) {\n      console.error('Error creating ride:', error);\n      throw error;\n    }\n  }\n\n  async getUserRides(userId: string): Promise<Ride[]> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .eq('rider_id', userId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n\n      this.ridesSubject.next(data);\n      return data;\n    } catch (error) {\n      console.error('Error fetching user rides:', error);\n      return [];\n    }\n  }\n\n  async getDriverRides(driverId: string): Promise<Ride[]> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .eq('driver_id', driverId)\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('Error fetching driver rides:', error);\n      return [];\n    }\n  }\n\n  async getAvailableRides(): Promise<Ride[]> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .eq('status', 'requested')\n        .order('created_at', { ascending: false });\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('Error fetching available rides:', error);\n      return [];\n    }\n  }\n\n  async acceptRide(rideId: string, driverId: string): Promise<boolean> {\n    return this.assignRideToDriver(rideId, driverId);\n  }\n\n  async startRide(rideId: string): Promise<boolean> {\n    return this.updateRideStatus(rideId, 'in-progress');\n  }\n\n  async completeRide(rideId: string): Promise<boolean> {\n    return this.updateRideStatus(rideId, 'completed');\n  }\n\n  async getRide(rideId: string): Promise<Ride | null> {\n    try {\n      const { data, error } = await this._supabase\n        .from('rides')\n        .select('*')\n        .eq('id', rideId)\n        .single();\n\n      if (error) throw error;\n      return data;\n    } catch (error) {\n      console.error('Error fetching ride:', error);\n      return null;\n    }\n  }\n\n  async updateRide(rideId: string, updates: Partial<Ride>): Promise<boolean> {\n    try {\n      const { error } = await this._supabase\n        .from('rides')\n        .update({\n          ...updates,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', rideId);\n\n      if (error) throw error;\n\n      // Send payment confirmation notification if payment status is updated to paid/completed\n      if (updates.payment_status && (updates.payment_status === 'paid' || updates.payment_status === 'completed')) {\n        setTimeout(async () => {\n          try {\n            const ride = await this.getRide(rideId);\n            if (ride && updates.amount) {\n              await this.smsService.sendPaymentConfirmationNotification(ride, updates.amount);\n            }\n          } catch (smsError) {\n            console.error('Error sending payment confirmation notification:', smsError);\n          }\n        }, 0);\n      }\n\n      // Refresh rides\n      await this.getAllRides();\n      return true;\n    } catch (error) {\n      console.error('Error updating ride:', error);\n      return false;\n    }\n  }\n\n  async cancelRide(rideId: string): Promise<boolean> {\n    return this.updateRideStatus(rideId, 'canceled');\n  }\n\n  filterRides(rides: Ride[], filter: RideFilter): Ride[] {\n    return rides.filter(ride => {\n      // Filter by status if specified\n      if (filter.status && ride.status !== filter.status) {\n        return false;\n      }\n\n      // Filter by rider ID if specified\n      if (filter.riderId && ride.rider_id !== filter.riderId) {\n        return false;\n      }\n\n      // Filter by driver ID if specified\n      if (filter.driverId && ride.driver_id !== filter.driverId) {\n        return false;\n      }\n\n      // Filter by date range if specified\n      if (filter.dateRange) {\n        const rideDate = new Date(ride.created_at);\n        const startDate = filter.dateRange.start;\n        const endDate = filter.dateRange.end;\n\n        if (rideDate < startDate || rideDate > endDate) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  }\n}\n\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\nclass MatDivider {\n  /** Whether the divider is vertically aligned. */\n  get vertical() {\n    return this._vertical;\n  }\n  set vertical(value) {\n    this._vertical = coerceBooleanProperty(value);\n  }\n  _vertical = false;\n  /** Whether the divider is an inset divider. */\n  get inset() {\n    return this._inset;\n  }\n  set inset(value) {\n    this._inset = coerceBooleanProperty(value);\n  }\n  _inset = false;\n  static ɵfac = function MatDivider_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDivider)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatDivider,\n    selectors: [[\"mat-divider\"]],\n    hostAttrs: [\"role\", \"separator\", 1, \"mat-divider\"],\n    hostVars: 7,\n    hostBindings: function MatDivider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-orientation\", ctx.vertical ? \"vertical\" : \"horizontal\");\n        i0.ɵɵclassProp(\"mat-divider-vertical\", ctx.vertical)(\"mat-divider-horizontal\", !ctx.vertical)(\"mat-divider-inset\", ctx.inset);\n      }\n    },\n    inputs: {\n      vertical: \"vertical\",\n      inset: \"inset\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function MatDivider_Template(rf, ctx) {},\n    styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDivider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-divider',\n      host: {\n        'role': 'separator',\n        '[attr.aria-orientation]': 'vertical ? \"vertical\" : \"horizontal\"',\n        '[class.mat-divider-vertical]': 'vertical',\n        '[class.mat-divider-horizontal]': '!vertical',\n        '[class.mat-divider-inset]': 'inset',\n        'class': 'mat-divider'\n      },\n      template: '',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\\n\"]\n    }]\n  }], null, {\n    vertical: [{\n      type: Input\n    }],\n    inset: [{\n      type: Input\n    }]\n  });\n})();\nclass MatDividerModule {\n  static ɵfac = function MatDividerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatDividerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatDividerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatDivider],\n      exports: [MatDivider, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MatDivider, MatDividerModule };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, NgModule } from '@angular/core';\nimport { NgTemplateOutlet } from '@angular/common';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/bidi';\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nconst _c0 = [\"determinateSpinner\"];\nfunction MatProgressSpinner_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 11);\n    i0.ɵɵelement(1, \"circle\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"viewBox\", ctx_r0._viewBox());\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r0._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx_r0._strokeCircumference() / 2, \"px\")(\"stroke-width\", ctx_r0._circleStrokeWidth(), \"%\");\n    i0.ɵɵattribute(\"r\", ctx_r0._circleRadius());\n  }\n}\nconst MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken('mat-progress-spinner-default-options', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    diameter: BASE_SIZE\n  };\n}\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\nclass MatProgressSpinner {\n  _elementRef = inject(ElementRef);\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.io/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.io/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  _color;\n  _defaultColor = 'primary';\n  /** The element of the determinate spinner. */\n  _determinateCircle;\n  constructor() {\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n    this._noopAnimations = animationMode === 'NoopAnimations' && !!defaults && !defaults._forceAnimations;\n    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner' ? 'indeterminate' : 'determinate';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  mode;\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  _value = 0;\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  get diameter() {\n    return this._diameter;\n  }\n  set diameter(size) {\n    this._diameter = size || 0;\n  }\n  _diameter = BASE_SIZE;\n  /** Stroke width of the progress spinner. */\n  get strokeWidth() {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value) {\n    this._strokeWidth = value || 0;\n  }\n  _strokeWidth;\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius() {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference() {\n    return 2 * Math.PI * this._circleRadius();\n  }\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return this._strokeCircumference() * (100 - this._value) / 100;\n    }\n    return null;\n  }\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return this.strokeWidth / this.diameter * 100;\n  }\n  static ɵfac = function MatProgressSpinner_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinner)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatProgressSpinner,\n    selectors: [[\"mat-progress-spinner\"], [\"mat-spinner\"]],\n    viewQuery: function MatProgressSpinner_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._determinateCircle = _t.first);\n      }\n    },\n    hostAttrs: [\"role\", \"progressbar\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-spinner\", \"mdc-circular-progress\"],\n    hostVars: 18,\n    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuemin\", 0)(\"aria-valuemax\", 100)(\"aria-valuenow\", ctx.mode === \"determinate\" ? ctx.value : null)(\"mode\", ctx.mode);\n        i0.ɵɵclassMap(\"mat-\" + ctx.color);\n        i0.ɵɵstyleProp(\"width\", ctx.diameter, \"px\")(\"height\", ctx.diameter, \"px\")(\"--mdc-circular-progress-size\", ctx.diameter + \"px\")(\"--mdc-circular-progress-active-indicator-width\", ctx.diameter + \"px\");\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._noopAnimations)(\"mdc-circular-progress--indeterminate\", ctx.mode === \"indeterminate\");\n      }\n    },\n    inputs: {\n      color: \"color\",\n      mode: \"mode\",\n      value: [2, \"value\", \"value\", numberAttribute],\n      diameter: [2, \"diameter\", \"diameter\", numberAttribute],\n      strokeWidth: [2, \"strokeWidth\", \"strokeWidth\", numberAttribute]\n    },\n    exportAs: [\"matProgressSpinner\"],\n    decls: 14,\n    vars: 11,\n    consts: [[\"circle\", \"\"], [\"determinateSpinner\", \"\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__determinate-container\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__determinate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\", 1, \"mdc-circular-progress__determinate-circle\"], [\"aria-hidden\", \"true\", 1, \"mdc-circular-progress__indeterminate-container\"], [1, \"mdc-circular-progress__spinner-layer\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-left\"], [3, \"ngTemplateOutlet\"], [1, \"mdc-circular-progress__gap-patch\"], [1, \"mdc-circular-progress__circle-clipper\", \"mdc-circular-progress__circle-right\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"focusable\", \"false\", 1, \"mdc-circular-progress__indeterminate-circle-graphic\"], [\"cx\", \"50%\", \"cy\", \"50%\"]],\n    template: function MatProgressSpinner_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatProgressSpinner_ng_template_0_Template, 2, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(2, \"div\", 2, 1);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(4, \"svg\", 3);\n        i0.ɵɵelement(5, \"circle\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7);\n        i0.ɵɵelementContainer(9, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵelementContainer(11, 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelementContainer(13, 8);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        const circle_r2 = i0.ɵɵreference(1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"viewBox\", ctx._viewBox());\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"stroke-dasharray\", ctx._strokeCircumference(), \"px\")(\"stroke-dashoffset\", ctx._strokeDashOffset(), \"px\")(\"stroke-width\", ctx._circleStrokeWidth(), \"%\");\n        i0.ɵɵattribute(\"r\", ctx._circleRadius());\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngTemplateOutlet\", circle_r2);\n      }\n    },\n    dependencies: [NgTemplateOutlet],\n    styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-spinner, mat-spinner',\n      exportAs: 'matProgressSpinner',\n      host: {\n        'role': 'progressbar',\n        'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': `_noopAnimations`,\n        '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n        '[style.width.px]': 'diameter',\n        '[style.height.px]': 'diameter',\n        '[style.--mdc-circular-progress-size]': 'diameter + \"px\"',\n        '[style.--mdc-circular-progress-active-indicator-width]': 'diameter + \"px\"',\n        '[attr.aria-valuemin]': '0',\n        '[attr.aria-valuemax]': '100',\n        '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n        '[attr.mode]': 'mode'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      imports: [NgTemplateOutlet],\n      template: \"<ng-template #circle>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__indeterminate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeCircumference() / 2\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</ng-template>\\n\\n<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-circular-progress__determinate-container\\\" aria-hidden=\\\"true\\\" #determinateSpinner>\\n  <svg [attr.viewBox]=\\\"_viewBox()\\\" class=\\\"mdc-circular-progress__determinate-circle-graphic\\\"\\n       xmlns=\\\"http://www.w3.org/2000/svg\\\" focusable=\\\"false\\\">\\n    <circle [attr.r]=\\\"_circleRadius()\\\"\\n            [style.stroke-dasharray.px]=\\\"_strokeCircumference()\\\"\\n            [style.stroke-dashoffset.px]=\\\"_strokeDashOffset()\\\"\\n            [style.stroke-width.%]=\\\"_circleStrokeWidth()\\\"\\n            class=\\\"mdc-circular-progress__determinate-circle\\\"\\n            cx=\\\"50%\\\" cy=\\\"50%\\\"/>\\n  </svg>\\n</div>\\n<!--TODO: figure out why there are 3 separate svgs-->\\n<div class=\\\"mdc-circular-progress__indeterminate-container\\\" aria-hidden=\\\"true\\\">\\n  <div class=\\\"mdc-circular-progress__spinner-layer\\\">\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__gap-patch\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n    <div class=\\\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\\\">\\n      <ng-container [ngTemplateOutlet]=\\\"circle\\\"></ng-container>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\\n\"]\n    }]\n  }], () => [], {\n    color: [{\n      type: Input\n    }],\n    _determinateCircle: [{\n      type: ViewChild,\n      args: ['determinateSpinner']\n    }],\n    mode: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    diameter: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    strokeWidth: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }]\n  });\n})();\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nconst MatSpinner = MatProgressSpinner;\nclass MatProgressSpinnerModule {\n  static ɵfac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatProgressSpinnerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatProgressSpinnerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressSpinner, MatSpinner],\n      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS, MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY, MatProgressSpinner, MatProgressSpinnerModule, MatSpinner };\n", "import { Component, Input, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MessageService } from '../../../core/services/message.service';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { RideService } from '../../../core/services/ride.service';\n\n@Component({\n  selector: 'app-ride-chat',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule\n  ],\n  template: `\n    <mat-card class=\"chat-card\">\n      <mat-card-header>\n        <mat-card-title>Chat</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"messages-container\">\n          <div *ngIf=\"loading\" class=\"loading-container\">\n            <mat-spinner diameter=\"40\"></mat-spinner>\n            <p>Loading messages...</p>\n          </div>\n          \n          <div *ngIf=\"!loading && messages.length === 0\" class=\"no-messages\">\n            <p>No messages yet. Start the conversation!</p>\n          </div>\n          \n          <div *ngIf=\"!loading && messages.length > 0\" class=\"messages-list\">\n            <div *ngFor=\"let message of messages\" \n                 class=\"message-bubble\" \n                 [ngClass]=\"{'sent': message.sender_id === currentUserId, 'received': message.sender_id !== currentUserId}\">\n              <div class=\"message-content\">{{ message.content }}</div>\n              <div class=\"message-time\">{{ formatTime(message.created_at) }}</div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n      <mat-card-actions>\n        <form (ngSubmit)=\"sendMessage()\" class=\"message-form\">\n          <mat-form-field appearance=\"outline\" class=\"message-input\">\n            <input matInput [(ngModel)]=\"newMessage\" name=\"newMessage\" placeholder=\"Type a message...\" autocomplete=\"off\">\n          </mat-form-field>\n          <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"!newMessage || sending\">\n            <mat-icon>send</mat-icon>\n          </button>\n        </form>\n      </mat-card-actions>\n    </mat-card>\n  `,\n  styles: [`\n    .chat-card {\n      margin: 16px;\n      max-width: 800px;\n    }\n    \n    .messages-container {\n      height: 300px;\n      overflow-y: auto;\n      padding: 16px;\n      background-color: #f5f5f5;\n      border-radius: 4px;\n    }\n    \n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n    }\n    \n    .no-messages {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .messages-list {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n    \n    .message-bubble {\n      max-width: 80%;\n      padding: 8px 12px;\n      border-radius: 16px;\n      position: relative;\n    }\n    \n    .sent {\n      align-self: flex-end;\n      background-color: #2196f3;\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n    \n    .received {\n      align-self: flex-start;\n      background-color: white;\n      border-bottom-left-radius: 4px;\n    }\n    \n    .message-content {\n      word-break: break-word;\n    }\n    \n    .message-time {\n      font-size: 0.7em;\n      opacity: 0.7;\n      text-align: right;\n      margin-top: 4px;\n    }\n    \n    .message-form {\n      display: flex;\n      gap: 8px;\n      width: 100%;\n      padding: 0 16px 16px;\n    }\n    \n    .message-input {\n      flex: 1;\n    }\n  `]\n})\nexport class RideChatComponent implements OnInit {\n  @Input() threadId!: string;\n  @Input() rideId!: string;\n  \n  messages: any[] = [];\n  newMessage = '';\n  loading = false;\n  sending = false;\n  currentUserId = '';\n  receiverId = '';\n  \n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private rideService: RideService\n  ) {}\n  \n  async ngOnInit() {\n    this.loading = true;\n    \n    try {\n      // Get current user\n      const user = await this.authService.getCurrentUser();\n      if (user) {\n        this.currentUserId = user.id;\n      }\n      \n      // If we have a rideId but no threadId, get or create the thread\n      if (this.rideId && !this.threadId) {\n        const thread = await this.messageService.getOrCreateThreadForRide(this.rideId);\n        this.threadId = thread.id;\n      }\n      \n      // Get the ride to determine the receiver\n      if (this.rideId) {\n        const ride = await this.rideService.getRide(this.rideId);\n        if (ride) {\n          this.receiverId = ride.rider_id === this.currentUserId ? ride.driver_id! : ride.rider_id;\n        }\n      }\n      \n      // Load messages\n      if (this.threadId) {\n        this.messages = await this.messageService.getThreadMessages(this.threadId);\n        \n        // Mark messages as read\n        await this.messageService.markMessagesAsRead(this.threadId);\n      }\n    } catch (error) {\n      console.error('Error initializing chat:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n  \n  async sendMessage() {\n    if (!this.newMessage || !this.threadId || !this.receiverId) return;\n    \n    this.sending = true;\n    \n    try {\n      await this.messageService.sendMessage(this.threadId, this.receiverId, this.newMessage);\n      this.newMessage = '';\n      \n      // Refresh messages\n      this.messages = await this.messageService.getThreadMessages(this.threadId);\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      this.sending = false;\n    }\n  }\n  \n  formatTime(timestamp: string): string {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUM,IAAO,cAAP,MAAO,aAAW;EAYZ;EACA;EAZF;EACA,eAAe,IAAI,gBAAwB,CAAA,CAAE;EACrD,SAAS,KAAK,aAAa,aAAY;EAC/B,mBAA2C;;EAGnD,IAAI,WAAQ;AACV,WAAO,KAAK;EACd;EAEA,YACU,YACA,aAAwB;AADxB,SAAA,aAAA;AACA,SAAA,cAAA;AAER,SAAK,YAAY,YAAY;AAC7B,SAAK,+BAA8B;EACrC;EAEA,cAAW;AAET,QAAI,KAAK,kBAAkB;AACzB,cAAQ,IAAI,mDAA4C;AACxD,WAAK,iBAAiB,YAAW;AACjC,WAAK,mBAAmB;IAC1B;EACF;EAEQ,iCAA8B;AAEpC,QAAI,KAAK,kBAAkB;AACzB,WAAK,iBAAiB,YAAW;IACnC;AAGA,SAAK,mBAAmB,KAAK,UAC1B,QAAQ,eAAe,EACvB,GAAG,oBACF,EAAE,OAAO,KAAK,QAAQ,UAAU,OAAO,QAAO,GAC9C,CAAO,YAAW;AAChB,cAAQ,IAAI,sCAA+B,QAAQ,WAAW,QAAQ,OAAO,QAAQ,GAAG;AAGxF,UAAI;AACF,cAAM,KAAK,aAAY;AACvB,gBAAQ,IAAI,0DAAqD;MACnE,SAAS,OAAO;AACd,gBAAQ,MAAM,uDAAkD,KAAK;MACvE;IACF,EAAC,EAEF,UAAU,CAAC,WAAU;AACpB,cAAQ,IAAI,2CAAoC,MAAM;AACtD,UAAI,WAAW,cAAc;AAC3B,gBAAQ,IAAI,0DAAqD;MACnE,WAAW,WAAW,iBAAiB;AACrC,gBAAQ,MAAM,gEAA2D;AAEzE,mBAAW,MAAK;AACd,eAAK,+BAA8B;QACrC,GAAG,GAAI;MACT;IACF,CAAC;EACL;EAEc,eAAY;;AACxB,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AACjB,aAAK,aAAa,KAAK,QAAQ,CAAA,CAAE;MACnC,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;MAEhD;IACF;;EAEM,cAAW;;AACf,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AACjB,aAAK,aAAa,KAAK,QAAQ,CAAA,CAAE;AACjC,eAAO,QAAQ,CAAA;MACjB,SAAS,OAAO;AACd,gBAAQ,MAAM,6BAA6B,KAAK;AAChD,eAAO,KAAK,aAAa;MAC3B;IACF;;EAEM,iBAAiB,QAAkB;;AACvC,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,GAAG,UAAU,MAAM,EACnB,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AACjB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,oCAAoC,MAAM,KAAK,KAAK;AAClE,eAAO,CAAA;MACT;IACF;;EAEM,mBAAmB,QAAgB,UAAgB;;AACvD,UAAI;AAEF,cAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AAC7C,YAAI,CAAC,eAAgB,YAAY,WAAW,eAAe,YAAY,WAAW,YAAa;AAC7F,gBAAM,IAAI,MAAM,4CAA4C;QAC9D;AAEA,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,UAC1B,KAAK,OAAO,EACZ,OAAO;UACN,WAAW;UACX,QAAQ;UACR,aAAY,oBAAI,KAAI,GAAG,YAAW;SACnC,EACA,GAAG,MAAM,MAAM,EACf,GAAG,UAAU,CAAC,aAAa,UAAU,CAAC;AAEzC,YAAI;AAAO,gBAAM;AAMjB,cAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AAC7C,YAAI,aAAa;AAIf,qBAAW,MAAW;AACpB,gBAAI;AACF,oBAAM,KAAK,WAAW,gCAAgC,aAAa,QAAQ;YAC7E,SAAS,UAAU;AAEjB,sBAAQ,MAAM,oCAAoC,QAAQ;YAC5D;UACF,IAAG,CAAC;AAGJ,kBAAQ,IAAI,kDAAkD,MAAM,wBAAwB,QAAQ,EAAE;QACxG;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,cAAM;MACR;IACF;;EAEM,iBAAiB,QAAgB,QAAkB;;AACvD,UAAI;AAEF,cAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AAC7C,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,gBAAgB;QAClC;AAGA,YAAI,CAAC,KAAK,wBAAwB,YAAY,QAAQ,MAAM,GAAG;AAC7D,gBAAM,IAAI,MAAM,kCAAkC,YAAY,MAAM,OAAO,MAAM,EAAE;QACrF;AAEA,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,UAC1B,KAAK,OAAO,EACZ,OAAO;UACN;UACA,aAAY,oBAAI,KAAI,GAAG,YAAW;SACnC,EACA,GAAG,MAAM,MAAM;AAElB,YAAI;AAAO,gBAAM;AAGjB,cAAM,KAAK,aAAY;AAGvB,cAAM,cAAc,MAAM,KAAK,QAAQ,MAAM;AAC7C,YAAI,aAAa;AAEf,qBAAW,MAAW;AACpB,gBAAI;AACF,kBAAI,WAAW,YAAY;AAEzB,sBAAM,KAAK,WAAW,kCAAkC,WAAW;cACrE,WAAW,WAAW,aAAa;AAEjC,sBAAM,KAAK,WAAW,+BAA+B,WAAW;AAEhE,sBAAM,KAAK,WAAW,gCAAgC,aAAa,MAAM;cAC3E,WAAW,WAAW,eAAe;AAEnC,sBAAM,KAAK,WAAW,kCAAkC,aAAa,MAAM;AAE3E,sBAAM,KAAK,WAAW,gCAAgC,aAAa,MAAM;cAC3E;YACF,SAAS,UAAU;AAEjB,sBAAQ,MAAM,8CAA8C,QAAQ;YACtE;UACF,IAAG,CAAC;AAGJ,kBAAQ,IAAI,+BAA+B,MAAM,4BAA4B,MAAM,EAAE;QACvF;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,+BAA+B,KAAK;AAClD,cAAM;MACR;IACF;;EAEQ,wBAAwB,MAAkB,IAAc;AAC9D,UAAM,cAAqD;MACzD,aAAa,CAAC,YAAY,UAAU;MACpC,YAAY,CAAC,eAAe,UAAU;MACtC,eAAe,CAAC,aAAa,UAAU;MACvC,aAAa,CAAA;MACb,YAAY,CAAA;;AAGd,WAAO,YAAY,IAAI,GAAG,SAAS,EAAE,KAAK;EAC5C;EAEM,WAAW,MAAoD;;AACnE,UAAI;AAEF,cAAM,wBAAwB,iCACzB,OADyB;UAE5B,gBAAgB,KAAK,kBAAkB;;AAGzC,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,CAAC,qBAAqB,CAAC,EAC9B,OAAM,EACN,OAAM;AAET,YAAI;AAAO,gBAAM;AAGjB,cAAM,eAAe,KAAK,aAAa;AACvC,aAAK,aAAa,KAAK,CAAC,GAAG,cAAc,IAAI,CAAC;AAG9C,mBAAW,MAAW;AACpB,cAAI;UAMJ,SAAS,UAAU;AAEjB,oBAAQ,MAAM,6CAA6C,QAAQ;UACrE;QACF,IAAG,CAAC;AAEJ,gBAAQ,IAAI,+CAA+C,KAAK,EAAE,EAAE;AAEpE,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,wBAAwB,KAAK;AAC3C,cAAM;MACR;IACF;;EAEM,aAAa,QAAc;;AAC/B,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,GAAG,YAAY,MAAM,EACrB,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AAEjB,aAAK,aAAa,KAAK,IAAI;AAC3B,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,eAAO,CAAA;MACT;IACF;;EAEM,eAAe,UAAgB;;AACnC,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,GAAG,aAAa,QAAQ,EACxB,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AACjB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,KAAK;AACnD,eAAO,CAAA;MACT;IACF;;EAEM,oBAAiB;;AACrB,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,GAAG,UAAU,WAAW,EACxB,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AACjB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,eAAO,CAAA;MACT;IACF;;EAEM,WAAW,QAAgB,UAAgB;;AAC/C,aAAO,KAAK,mBAAmB,QAAQ,QAAQ;IACjD;;EAEM,UAAU,QAAc;;AAC5B,aAAO,KAAK,iBAAiB,QAAQ,aAAa;IACpD;;EAEM,aAAa,QAAc;;AAC/B,aAAO,KAAK,iBAAiB,QAAQ,WAAW;IAClD;;EAEM,QAAQ,QAAc;;AAC1B,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,UAChC,KAAK,OAAO,EACZ,OAAO,GAAG,EACV,GAAG,MAAM,MAAM,EACf,OAAM;AAET,YAAI;AAAO,gBAAM;AACjB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,wBAAwB,KAAK;AAC3C,eAAO;MACT;IACF;;EAEM,WAAW,QAAgB,SAAsB;;AACrD,UAAI;AACF,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,UAC1B,KAAK,OAAO,EACZ,OAAO,iCACH,UADG;UAEN,aAAY,oBAAI,KAAI,GAAG,YAAW;UACnC,EACA,GAAG,MAAM,MAAM;AAElB,YAAI;AAAO,gBAAM;AAGjB,YAAI,QAAQ,mBAAmB,QAAQ,mBAAmB,UAAU,QAAQ,mBAAmB,cAAc;AAC3G,qBAAW,MAAW;AACpB,gBAAI;AACF,oBAAM,OAAO,MAAM,KAAK,QAAQ,MAAM;AACtC,kBAAI,QAAQ,QAAQ,QAAQ;AAC1B,sBAAM,KAAK,WAAW,oCAAoC,MAAM,QAAQ,MAAM;cAChF;YACF,SAAS,UAAU;AACjB,sBAAQ,MAAM,oDAAoD,QAAQ;YAC5E;UACF,IAAG,CAAC;QACN;AAGA,cAAM,KAAK,YAAW;AACtB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,wBAAwB,KAAK;AAC3C,eAAO;MACT;IACF;;EAEM,WAAW,QAAc;;AAC7B,aAAO,KAAK,iBAAiB,QAAQ,UAAU;IACjD;;EAEA,YAAY,OAAe,QAAkB;AAC3C,WAAO,MAAM,OAAO,UAAO;AAEzB,UAAI,OAAO,UAAU,KAAK,WAAW,OAAO,QAAQ;AAClD,eAAO;MACT;AAGA,UAAI,OAAO,WAAW,KAAK,aAAa,OAAO,SAAS;AACtD,eAAO;MACT;AAGA,UAAI,OAAO,YAAY,KAAK,cAAc,OAAO,UAAU;AACzD,eAAO;MACT;AAGA,UAAI,OAAO,WAAW;AACpB,cAAM,WAAW,IAAI,KAAK,KAAK,UAAU;AACzC,cAAM,YAAY,OAAO,UAAU;AACnC,cAAM,UAAU,OAAO,UAAU;AAEjC,YAAI,WAAW,aAAa,WAAW,SAAS;AAC9C,iBAAO;QACT;MACF;AAEA,aAAO;IACT,CAAC;EACH;;qCA1aW,cAAW,mBAAA,UAAA,GAAA,mBAAA,WAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;;;ACHD,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA,EAEf,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS,sBAAsB,KAAK;AAAA,EAC3C;AAAA,EACA,SAAS;AAAA,EACT,OAAO,YAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,IAC3B,WAAW,CAAC,QAAQ,aAAa,GAAG,aAAa;AAAA,IACjD,UAAU;AAAA,IACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,oBAAoB,IAAI,WAAW,aAAa,YAAY;AAC3E,QAAG,sBAAY,wBAAwB,IAAI,QAAQ,EAAE,0BAA0B,CAAC,IAAI,QAAQ,EAAE,qBAAqB,IAAI,KAAK;AAAA,MAC9H;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAAA,IAAC;AAAA,IACjD,QAAQ,CAAC,yeAAye;AAAA,IAClf,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,2BAA2B;AAAA,QAC3B,gCAAgC;AAAA,QAChC,kCAAkC;AAAA,QAClC,6BAA6B;AAAA,QAC7B,SAAS;AAAA,MACX;AAAA,MACA,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,yeAAye;AAAA,IACpf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,OAAO,YAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,eAAe;AAAA,EAC5C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,UAAU;AAAA,MACrC,SAAS,CAAC,YAAY,eAAe;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvFH,IAAM,MAAM,CAAC,oBAAoB;AACjC,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,yBAAe;AAClB,IAAG,yBAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,oBAAU,GAAG,UAAU,EAAE;AAC5B,IAAG,uBAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,wBAAc;AAChC,IAAG,sBAAY,WAAW,OAAO,SAAS,CAAC;AAC3C,IAAG,oBAAU;AACb,IAAG,sBAAY,oBAAoB,OAAO,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,OAAO,qBAAqB,IAAI,GAAG,IAAI,EAAE,gBAAgB,OAAO,mBAAmB,GAAG,GAAG;AACtL,IAAG,sBAAY,KAAK,OAAO,cAAc,CAAC;AAAA,EAC5C;AACF;AACA,IAAM,uCAAuC,IAAI,eAAe,wCAAwC;AAAA,EACtG,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,+CAA+C;AACtD,SAAO;AAAA,IACL,UAAU;AAAA,EACZ;AACF;AAIA,IAAM,YAAY;AAIlB,IAAM,oBAAoB;AAC1B,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EACA,IAAI,MAAM,OAAO;AACf,SAAK,SAAS;AAAA,EAChB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA;AAAA,EAEhB;AAAA,EACA,cAAc;AACZ,UAAM,gBAAgB,OAAO,uBAAuB;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,WAAW,OAAO,oCAAoC;AAC5D,SAAK,kBAAkB,kBAAkB,oBAAoB,CAAC,CAAC,YAAY,CAAC,SAAS;AACrF,SAAK,OAAO,KAAK,YAAY,cAAc,SAAS,YAAY,MAAM,gBAAgB,kBAAkB;AACxG,QAAI,UAAU;AACZ,UAAI,SAAS,OAAO;AAClB,aAAK,QAAQ,KAAK,gBAAgB,SAAS;AAAA,MAC7C;AACA,UAAI,SAAS,UAAU;AACrB,aAAK,WAAW,SAAS;AAAA,MAC3B;AACA,UAAI,SAAS,aAAa;AACxB,aAAK,cAAc,SAAS;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AAAA,EACrD;AAAA,EACA,IAAI,MAAM,GAAG;AACX,SAAK,SAAS,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,SAAS;AAAA;AAAA,EAET,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,MAAM;AACjB,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,cAAc;AAChB,WAAO,KAAK,gBAAgB,KAAK,WAAW;AAAA,EAC9C;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,eAAe,SAAS;AAAA,EAC/B;AAAA,EACA;AAAA;AAAA,EAEA,gBAAgB;AACd,YAAQ,KAAK,WAAW,qBAAqB;AAAA,EAC/C;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,UAAU,KAAK,cAAc,IAAI,IAAI,KAAK;AAChD,WAAO,OAAO,OAAO,IAAI,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,IAAI,KAAK,KAAK,KAAK,cAAc;AAAA,EAC1C;AAAA;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,SAAS,eAAe;AAC/B,aAAO,KAAK,qBAAqB,KAAK,MAAM,KAAK,UAAU;AAAA,IAC7D;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,cAAc,KAAK,WAAW;AAAA,EAC5C;AAAA,EACA,OAAO,YAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,YAAsB,gBAAG,4BAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,sBAAsB,GAAG,CAAC,aAAa,CAAC;AAAA,IACrD,WAAW,SAAS,yBAAyB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,yBAAe,KAAQ,sBAAY,CAAC,MAAM,IAAI,qBAAqB,GAAG;AAAA,MAC3E;AAAA,IACF;AAAA,IACA,WAAW,CAAC,QAAQ,eAAe,YAAY,MAAM,GAAG,4BAA4B,uBAAuB;AAAA,IAC3G,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,sBAAY,iBAAiB,CAAC,EAAE,iBAAiB,GAAG,EAAE,iBAAiB,IAAI,SAAS,gBAAgB,IAAI,QAAQ,IAAI,EAAE,QAAQ,IAAI,IAAI;AACzI,QAAG,qBAAW,SAAS,IAAI,KAAK;AAChC,QAAG,sBAAY,SAAS,IAAI,UAAU,IAAI,EAAE,UAAU,IAAI,UAAU,IAAI,EAAE,gCAAgC,IAAI,WAAW,IAAI,EAAE,kDAAkD,IAAI,WAAW,IAAI;AACpM,QAAG,sBAAY,2BAA2B,IAAI,eAAe,EAAE,wCAAwC,IAAI,SAAS,eAAe;AAAA,MACrI;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO,CAAC,GAAG,SAAS,SAAS,eAAe;AAAA,MAC5C,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,aAAa,CAAC,GAAG,eAAe,eAAe,eAAe;AAAA,IAChE;AAAA,IACA,UAAU,CAAC,oBAAoB;AAAA,IAC/B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,sBAAsB,EAAE,GAAG,CAAC,eAAe,QAAQ,GAAG,8CAA8C,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,mDAAmD,GAAG,CAAC,MAAM,OAAO,MAAM,OAAO,GAAG,2CAA2C,GAAG,CAAC,eAAe,QAAQ,GAAG,gDAAgD,GAAG,CAAC,GAAG,sCAAsC,GAAG,CAAC,GAAG,yCAAyC,oCAAoC,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kCAAkC,GAAG,CAAC,GAAG,yCAAyC,qCAAqC,GAAG,CAAC,SAAS,8BAA8B,aAAa,SAAS,GAAG,qDAAqD,GAAG,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC;AAAA,IAC7zB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,UAAI,KAAK,GAAG;AACV,QAAG,qBAAW,GAAG,2CAA2C,GAAG,GAAG,eAAe,MAAM,GAAM,gCAAsB;AACnH,QAAG,yBAAe,GAAG,OAAO,GAAG,CAAC;AAChC,QAAG,yBAAe;AAClB,QAAG,yBAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,oBAAU,GAAG,UAAU,CAAC;AAC3B,QAAG,uBAAa,EAAE;AAClB,QAAG,0BAAgB;AACnB,QAAG,yBAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,QAAG,6BAAmB,GAAG,CAAC;AAC1B,QAAG,uBAAa;AAChB,QAAG,yBAAe,IAAI,OAAO,CAAC;AAC9B,QAAG,6BAAmB,IAAI,CAAC;AAC3B,QAAG,uBAAa;AAChB,QAAG,yBAAe,IAAI,OAAO,EAAE;AAC/B,QAAG,6BAAmB,IAAI,CAAC;AAC3B,QAAG,uBAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,cAAM,YAAe,sBAAY,CAAC;AAClC,QAAG,oBAAU,CAAC;AACd,QAAG,sBAAY,WAAW,IAAI,SAAS,CAAC;AACxC,QAAG,oBAAU;AACb,QAAG,sBAAY,oBAAoB,IAAI,qBAAqB,GAAG,IAAI,EAAE,qBAAqB,IAAI,kBAAkB,GAAG,IAAI,EAAE,gBAAgB,IAAI,mBAAmB,GAAG,GAAG;AACtK,QAAG,sBAAY,KAAK,IAAI,cAAc,CAAC;AACvC,QAAG,oBAAU,CAAC;AACd,QAAG,qBAAW,oBAAoB,SAAS;AAC3C,QAAG,oBAAU,CAAC;AACd,QAAG,qBAAW,oBAAoB,SAAS;AAC3C,QAAG,oBAAU,CAAC;AACd,QAAG,qBAAW,oBAAoB,SAAS;AAAA,MAC7C;AAAA,IACF;AAAA,IACA,cAAc,CAAC,gBAAgB;AAAA,IAC/B,QAAQ,CAAC,mrIAAmrI;AAAA,IAC5rI,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,QAGT,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,mCAAmC;AAAA,QACnC,gDAAgD;AAAA,QAChD,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,wCAAwC;AAAA,QACxC,0DAA0D;AAAA,QAC1D,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,eAAe;AAAA,MACjB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,SAAS,CAAC,gBAAgB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ,CAAC,mrIAAmrI;AAAA,IAC9rI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,IAAM,aAAa;AACnB,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,YAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,YAAsB,gBAAG,2BAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,UAAU;AAAA,MACxC,SAAS,CAAC,oBAAoB,YAAY,eAAe;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;;;;ACjRO,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,eAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA,EAAI;;;;;AAG5B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmE,GAAA,GAAA;AAC9D,IAAA,iBAAA,GAAA,0CAAA;AAAwC,IAAA,uBAAA,EAAI;;;;;AAI/C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAEgH,GAAA,OAAA,EAAA;AACjF,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAClD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAoC,IAAA,uBAAA,EAAM;;;;;AAFjE,IAAA,qBAAA,WAAA,0BAAA,GAAAA,MAAA,WAAA,cAAA,OAAA,eAAA,WAAA,cAAA,OAAA,aAAA,CAAA;AAC0B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,OAAA;AACH,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,WAAA,UAAA,CAAA;;;;;AAL9B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wCAAA,GAAA,GAAA,OAAA,EAAA;AAMF,IAAA,uBAAA;;;;AAN2B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,QAAA;;;AAoG/B,IAAO,oBAAP,MAAO,mBAAiB;EAYlB;EACA;EACA;EAbD;EACA;EAET,WAAkB,CAAA;EAClB,aAAa;EACb,UAAU;EACV,UAAU;EACV,gBAAgB;EAChB,aAAa;EAEb,YACU,gBACA,aACA,aAAwB;AAFxB,SAAA,iBAAA;AACA,SAAA,cAAA;AACA,SAAA,cAAA;EACP;EAEG,WAAQ;;AACZ,WAAK,UAAU;AAEf,UAAI;AAEF,cAAM,OAAO,MAAM,KAAK,YAAY,eAAc;AAClD,YAAI,MAAM;AACR,eAAK,gBAAgB,KAAK;QAC5B;AAGA,YAAI,KAAK,UAAU,CAAC,KAAK,UAAU;AACjC,gBAAM,SAAS,MAAM,KAAK,eAAe,yBAAyB,KAAK,MAAM;AAC7E,eAAK,WAAW,OAAO;QACzB;AAGA,YAAI,KAAK,QAAQ;AACf,gBAAM,OAAO,MAAM,KAAK,YAAY,QAAQ,KAAK,MAAM;AACvD,cAAI,MAAM;AACR,iBAAK,aAAa,KAAK,aAAa,KAAK,gBAAgB,KAAK,YAAa,KAAK;UAClF;QACF;AAGA,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW,MAAM,KAAK,eAAe,kBAAkB,KAAK,QAAQ;AAGzE,gBAAM,KAAK,eAAe,mBAAmB,KAAK,QAAQ;QAC5D;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,4BAA4B,KAAK;MACjD;AACE,aAAK,UAAU;MACjB;IACF;;EAEM,cAAW;;AACf,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,CAAC,KAAK;AAAY;AAE5D,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,KAAK,eAAe,YAAY,KAAK,UAAU,KAAK,YAAY,KAAK,UAAU;AACrF,aAAK,aAAa;AAGlB,aAAK,WAAW,MAAM,KAAK,eAAe,kBAAkB,KAAK,QAAQ;MAC3E,SAAS,OAAO;AACd,gBAAQ,MAAM,0BAA0B,KAAK;MAC/C;AACE,aAAK,UAAU;MACjB;IACF;;EAEA,WAAW,WAAiB;AAC1B,UAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,WAAO,KAAK,mBAAmB,CAAA,GAAI,EAAE,MAAM,WAAW,QAAQ,UAAS,CAAE;EAC3E;;qCA5EW,oBAAiB,4BAAA,cAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,QAAA,EAAA,UAAA,YAAA,QAAA,SAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,cAAA,WAAA,GAAA,eAAA,GAAA,CAAA,YAAA,IAAA,QAAA,cAAA,eAAA,qBAAA,gBAAA,OAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,WAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApH1B,MAAA,yBAAA,GAAA,YAAA,CAAA,EAA4B,GAAA,iBAAA,EACT,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,MAAA;AAAI,MAAA,uBAAA,EAAiB;AAEvC,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA;AAEd,MAAA,qBAAA,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA,EAKoB,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA;AAYrE,MAAA,uBAAA,EAAM;AAER,MAAA,yBAAA,GAAA,kBAAA,EAAkB,IAAA,QAAA,CAAA;AACV,MAAA,qBAAA,YAAA,SAAA,uDAAA;AAAA,eAAY,IAAA,YAAA;MAAa,CAAA;AAC7B,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAA2D,IAAA,SAAA,CAAA;AACzC,MAAA,2BAAA,iBAAA,SAAA,2DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,eAAA;MAAA,CAAA;AAAhB,MAAA,uBAAA,EAA8G;AAEhH,MAAA,yBAAA,IAAA,UAAA,CAAA,EAA4F,IAAA,UAAA;AAChF,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA,EAAW,EAClB,EACJ,EACU;;;AA5BT,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,SAAA,WAAA,CAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,SAAA,SAAA,CAAA;AAaY,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,UAAA;AAEsC,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,CAAA,IAAA,cAAA,IAAA,OAAA;;oBAxC9D,cAAY,SAAA,SAAA,MACZ,aAAW,oBAAA,sBAAA,iBAAA,sBAAA,SAAA,QACX,eAAa,SAAA,gBAAA,gBAAA,eAAA,cACb,oBAAkB,cAClB,gBAAc,UACd,iBAAe,WACf,eAAa,SACb,0BAAwB,kBAAA,GAAA,QAAA,CAAA,i6CAAA,EAAA,CAAA;;;sEAuHf,mBAAiB,CAAA;UAlI7B;uBACW,iBAAe,YACb,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqCT,QAAA,CAAA,s4CAAA,EAAA,CAAA;wFAiFQ,UAAQ,CAAA;UAAhB;MACQ,QAAM,CAAA;UAAd;;;;6EAFU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,8DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": ["_c0"], "x_google_ignoreList": [1, 2]}