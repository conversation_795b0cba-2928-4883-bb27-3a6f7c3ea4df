{"version": 3, "sources": ["src/app/features/dashboard/admin/admin-reports/admin-reports.component.scss"], "sourcesContent": [".reports-container {\r\n  padding: 20px;\r\n}\r\n\r\n.controls-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.controls-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 16px;\r\n  align-items: start;\r\n}\r\n\r\n.button-container {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  grid-column: 1 / -1;\r\n  margin-top: 10px;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n}\r\n\r\n.report-content {\r\n  margin-top: 20px;\r\n}\r\n\r\n.date-range {\r\n  color: #666;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.charts-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n  gap: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-cards {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.summary-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin: 10px 0;\r\n}\r\n\r\n.summary-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  font-size: 14px;\r\n  color: #666;\r\n}\r\n\r\n.table-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\ntable {\r\n  width: 100%;\r\n}\r\n\r\n.no-data {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n  color: #666;\r\n}\r\n\r\n.no-data mat-icon {\r\n  font-size: 48px;\r\n  height: 48px;\r\n  width: 48px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .controls-card,\r\n  button {\r\n    display: none !important;\r\n  }\r\n\r\n  .report-content {\r\n    page-break-inside: avoid;\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;AACA,eAAA;AACA,eAAA,CAAA,CAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,SAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,UAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;AACA,SAAA;;AAGF,CATA,QASA;AACE,aAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;;AAIF,OAAA;AACE,GA1FF;EA0FE;AAEE,aAAA;;AAGF,GAnEF;AAoEI,uBAAA;;;", "names": []}