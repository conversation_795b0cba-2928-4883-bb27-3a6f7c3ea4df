{"version": 3, "sources": ["src/app/shared/components/map-test/map-test.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MapDisplayComponent } from '../map-display/map-display.component';\n\n@Component({\n  selector: 'app-map-test',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MapDisplayComponent\n  ],\n  template: `\n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>Google Maps Test</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <app-map-display\n          [origin]=\"'New York, NY'\"\n          [destination]=\"'Boston, MA'\">\n        </app-map-display>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [`\n    mat-card {\n      margin: 16px;\n    }\n  `]\n})\nexport class MapTestComponent {\n  // This is a simple test component to verify that our Google Maps implementation works\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCM,IAAO,mBAAP,MAAO,kBAAgB;;qCAAhB,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,UAAA,aAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAlBzB,MAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAiB;AAEnD,MAAA,yBAAA,GAAA,kBAAA;AACE,MAAA,oBAAA,GAAA,mBAAA,CAAA;AAIF,MAAA,uBAAA,EAAmB;;;AAHf,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,UAAA,cAAA,EAAyB,eAAA,YAAA;;;IAZ/B;IACA;IAAa;IAAA;IAAA;IAAA;IACb;IACA;EAAmB,GAAA,QAAA,CAAA,2GAAA,EAAA,CAAA;;;sEAqBV,kBAAgB,CAAA;UA5B5B;uBACW,gBAAc,YACZ,MAAI,SACP;MACP;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;KAYT,QAAA,CAAA,+RAAA,EAAA,CAAA;;;;6EAOU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,4DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}