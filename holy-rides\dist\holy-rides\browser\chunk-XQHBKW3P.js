import {
  AuthService
} from "./chunk-QNBL54OW.js";
import {
  BehaviorSubject,
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-THPQGTPB.js";
import {
  __async
} from "./chunk-S35DAJRX.js";

// src/app/core/services/message.service.ts
var MessageService = class _MessageService {
  authService;
  supabase;
  messagesSubject = new BehaviorSubject([]);
  messages$ = this.messagesSubject.asObservable();
  threadsSubject = new BehaviorSubject([]);
  threads$ = this.threadsSubject.asObservable();
  constructor(authService) {
    this.authService = authService;
    this.supabase = authService.supabase;
  }
  getOrCreateThreadForRide(rideId) {
    return __async(this, null, function* () {
      try {
        const { data: existingThreads, error: fetchError } = yield this.supabase.from("message_threads").select("*").eq("ride_id", rideId).limit(1);
        if (fetchError)
          throw fetchError;
        if (existingThreads && existingThreads.length > 0) {
          return existingThreads[0];
        }
        const { data: newThread, error: createError } = yield this.supabase.from("message_threads").insert([{ ride_id: rideId }]).select().single();
        if (createError)
          throw createError;
        return newThread;
      } catch (error) {
        console.error("Error getting or creating thread:", error);
        throw error;
      }
    });
  }
  getThreadMessages(threadId) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this.supabase.from("messages").select("*").eq("thread_id", threadId).order("created_at", { ascending: true });
        if (error)
          throw error;
        this.messagesSubject.next(data || []);
        return data || [];
      } catch (error) {
        console.error("Error fetching thread messages:", error);
        return [];
      }
    });
  }
  getUserThreads() {
    return __async(this, null, function* () {
      try {
        const user = yield this.authService.getCurrentUser();
        if (!user)
          throw new Error("User not authenticated");
        const { data, error } = yield this.supabase.from("message_threads").select(`
          *,
          rides!inner(rider_id, driver_id)
        `).or(`rides.rider_id.eq.${user.id},rides.driver_id.eq.${user.id}`).order("updated_at", { ascending: false });
        if (error)
          throw error;
        this.threadsSubject.next(data || []);
        return data || [];
      } catch (error) {
        console.error("Error fetching user threads:", error);
        return [];
      }
    });
  }
  sendMessage(threadId, receiverId, content) {
    return __async(this, null, function* () {
      try {
        const currentUser = yield this.authService.getCurrentUser();
        if (!currentUser)
          throw new Error("User not authenticated");
        const { data, error } = yield this.supabase.from("messages").insert([{
          thread_id: threadId,
          sender_id: currentUser.id,
          receiver_id: receiverId,
          content,
          is_read: false
        }]).select().single();
        if (error)
          throw error;
        const currentMessages = this.messagesSubject.value;
        this.messagesSubject.next([...currentMessages, data]);
        return data;
      } catch (error) {
        console.error("Error sending message:", error);
        throw error;
      }
    });
  }
  markMessagesAsRead(threadId) {
    return __async(this, null, function* () {
      try {
        const currentUser = yield this.authService.getCurrentUser();
        if (!currentUser)
          throw new Error("User not authenticated");
        const { error } = yield this.supabase.from("messages").update({ is_read: true }).eq("thread_id", threadId).eq("receiver_id", currentUser.id).eq("is_read", false);
        if (error)
          throw error;
        yield this.getThreadMessages(threadId);
      } catch (error) {
        console.error("Error marking messages as read:", error);
      }
    });
  }
  getUnreadMessageCount() {
    return __async(this, null, function* () {
      try {
        const currentUser = yield this.authService.getCurrentUser();
        if (!currentUser)
          return 0;
        const { data, error } = yield this.supabase.from("messages").select("id", { count: "exact" }).eq("receiver_id", currentUser.id).eq("is_read", false);
        if (error)
          throw error;
        return data?.length || 0;
      } catch (error) {
        console.error("Error getting unread message count:", error);
        return 0;
      }
    });
  }
  static \u0275fac = function MessageService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MessageService)(\u0275\u0275inject(AuthService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _MessageService, factory: _MessageService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MessageService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }], null);
})();

export {
  MessageService
};
//# sourceMappingURL=chunk-XQHBKW3P.js.map
