{"version": 3, "sources": ["src/app/shared/components/rating-display/rating-display.component.ts"], "sourcesContent": ["\n    .rating-stars {\n      display: flex;\n      margin: 10px 0;\n    }\n    \n    .rating-stars.small {\n      margin: 0;\n    }\n    \n    .full {\n      color: #ffc107;\n    }\n    \n    .half {\n      color: #ffc107;\n    }\n    \n    .empty {\n      color: #e0e0e0;\n    }\n    \n    .small-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n      color: #ffc107;\n    }\n    \n    .rating-divider {\n      margin: 20px 0;\n    }\n    \n    .recent-ratings h3 {\n      margin-bottom: 16px;\n      font-weight: 500;\n    }\n    \n    .rating-item {\n      margin-bottom: 20px;\n      padding-bottom: 10px;\n      border-bottom: 1px solid #f0f0f0;\n    }\n    \n    .rating-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n    }\n    \n    .rating-info {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-end;\n    }\n    \n    .rating-user {\n      font-weight: 500;\n    }\n    \n    .rating-date {\n      font-size: 0.8em;\n      color: #757575;\n    }\n    \n    .rating-feedback {\n      margin: 0;\n      font-style: italic;\n      color: #555;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,UAAA,KAAA;;AAGF,CALA,YAKA,CAAA;AACE,UAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA,eAAA;AACE,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,iBAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA;AACA,cAAA;AACA,SAAA;;", "names": []}