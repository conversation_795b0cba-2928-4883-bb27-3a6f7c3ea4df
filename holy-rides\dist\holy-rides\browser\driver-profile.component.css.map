{"version": 3, "sources": ["src/app/features/dashboard/driver/driver-profile/driver-profile.component.ts"], "sourcesContent": ["\n    .profile-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 16px;\n    }\n\n    .vehicle-card {\n      margin-top: 20px;\n    }\n\n    .vehicle-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 0;\n      border-bottom: 1px solid #eee;\n    }\n\n    .vehicle-item:last-child {\n      border-bottom: none;\n    }\n\n    .vehicle-details h3 {\n      margin: 0 0 8px 0;\n      font-weight: 500;\n    }\n\n    .vehicle-details p {\n      margin: 4px 0;\n      color: #666;\n    }\n\n    .vehicle-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .divider {\n      margin: 16px 0;\n    }\n\n    .vehicle-form {\n      margin-top: 16px;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 16px;\n      margin-bottom: 8px;\n    }\n\n    .form-row mat-form-field {\n      flex: 1;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 8px;\n      margin-top: 16px;\n    }\n\n    .no-vehicles {\n      padding: 16px 0;\n      color: #666;\n      font-style: italic;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,SAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA,KAAA;AACA,iBAAA,IAAA,MAAA;;AAGF,CARA,YAQA;AACE,iBAAA;;AAGF,CAAA,gBAAA;AACE,UAAA,EAAA,EAAA,IAAA;AACA,eAAA;;AAGF,CALA,gBAKA;AACE,UAAA,IAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CANA,SAMA;AACE,QAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,SAAA;AACA,cAAA;;", "names": []}