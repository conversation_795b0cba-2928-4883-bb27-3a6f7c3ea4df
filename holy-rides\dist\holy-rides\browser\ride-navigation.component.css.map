{"version": 3, "sources": ["src/app/features/dashboard/driver/ride-navigation/ride-navigation.component.ts"], "sourcesContent": ["\n    .navigation-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .navigation-card {\n      width: 90%;\n      max-width: 600px;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .close-button {\n      position: absolute;\n      right: 8px;\n      top: 8px;\n    }\n\n    .navigation-details {\n      padding: 16px 0;\n    }\n\n    .location-info {\n      margin-bottom: 24px;\n    }\n\n    .location-item {\n      display: flex;\n      align-items: flex-start;\n      margin-bottom: 16px;\n    }\n\n    .location-icon {\n      margin-right: 16px;\n      color: #3f51b5;\n    }\n\n    .location-icon.pickup {\n      color: #4caf50;\n    }\n\n    .location-icon.dropoff {\n      color: #f44336;\n    }\n\n    .location-text {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .location-label {\n      font-weight: 500;\n      margin-bottom: 4px;\n    }\n\n    .location-value {\n      color: #666;\n    }\n\n    .route-info {\n      background-color: #f5f5f5;\n      border-radius: 4px;\n      padding: 16px;\n      margin-bottom: 24px;\n    }\n\n    .route-info p {\n      margin: 8px 0;\n    }\n\n    .navigation-links {\n      display: flex;\n      justify-content: space-around;\n      flex-wrap: wrap;\n      gap: 16px;\n    }\n\n    .nav-link {\n      text-decoration: none;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,YAAA;AACA,SAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,gBAAA;AACA,SAAA;;AAGF,CALA,aAKA,CAAA;AACE,SAAA;;AAGF,CATA,aASA,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;;AAGF,CAAA;AACE,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,iBAAA;AACA,WAAA;AACA,iBAAA;;AAGF,CAPA,WAOA;AACE,UAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,aAAA;AACA,OAAA;;AAGF,CAAA;AACE,mBAAA;;", "names": []}