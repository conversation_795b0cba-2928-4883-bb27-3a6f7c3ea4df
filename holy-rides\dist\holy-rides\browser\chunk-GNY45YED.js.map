{"version": 3, "sources": ["src/app/features/auth/login/login.component.ts", "src/app/features/auth/login/login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, ActivatedRoute, RouterLink } from '@angular/router';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { AuthService } from '../../../core/services/auth.service';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { filter } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    RouterLink\r\n  ],\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss']\r\n})\r\nexport class LoginComponent {\r\n  loginForm: FormGroup;\r\n  error: string = '';\r\n  loading: boolean = false;\r\n  private readonly MAX_ROLE_CHECK_ATTEMPTS = 3;\r\n  private readonly RETRY_DELAY = 1000;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private authService: AuthService\r\n  ) {\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required, Validators.minLength(6)]]\r\n    });\r\n  }\r\n\r\n  async onSubmit() {\r\n    if (this.loginForm.invalid) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    try {\r\n      const { error } = await this.authService.login(\r\n        this.loginForm.value.email,\r\n        this.loginForm.value.password\r\n      );\r\n\r\n      if (error) {\r\n        this.error = error.message;\r\n        return;\r\n      }\r\n\r\n      // Wait for auth state to be updated and get the user\r\n      const user = await firstValueFrom(\r\n        this.authService.user$.pipe(\r\n          filter(user => user !== null)\r\n        )\r\n      );\r\n\r\n      // Check if user is active\r\n      if (user && !user.is_approved) {\r\n        const message = user.role === 'admin' ? \r\n          'Your account has been deactivated. Please contact support.' :\r\n          'Your account is pending approval. Please wait for administrator approval.';\r\n        \r\n        this.error = message;\r\n        await this.authService.logout();\r\n        return;\r\n      }\r\n\r\n      // Try getting the role multiple times if needed\r\n      let role = null;\r\n      let attempts = 0;\r\n      while (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS) {\r\n        console.log('Attempting to get user role...');\r\n        role = await this.authService.getUserRole();\r\n        if (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS - 1) {\r\n          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));\r\n        }\r\n        attempts++;\r\n      }\r\n\r\n      if (role) {\r\n        const dashboardRoute = this.authService.getDashboardRouteForRole(role);\r\n        await this.router.navigate([dashboardRoute]);\r\n      } else {\r\n        this.error = 'Unable to determine user role. Please try logging in again.';\r\n        await this.authService.logout();\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      this.error = 'An error occurred during login. Please try again.';\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"login-container\">\r\n  <!-- <div class=\"logo-container\">\r\n    <img src=\"assets/hr.png\" alt=\"Holy Rides Logo\" class=\"logo\">\r\n\r\n  </div> -->\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title>Login</mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput type=\"email\" formControlName=\"email\" placeholder=\"Enter your email\">\r\n          <mat-error *ngIf=\"loginForm.get('email')?.errors?.['required']\">Email is required</mat-error>\r\n          <mat-error *ngIf=\"loginForm.get('email')?.errors?.['email']\">Please enter a valid email</mat-error>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Password</mat-label>\r\n          <input matInput type=\"password\" formControlName=\"password\" placeholder=\"Enter your password\">\r\n          <mat-error *ngIf=\"loginForm.get('password')?.errors?.['required']\">Password is required</mat-error>\r\n          <mat-error *ngIf=\"loginForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</mat-error>\r\n        </mat-form-field>\r\n\r\n        <div class=\"error-message\" *ngIf=\"error\">{{ error }}</div>\r\n\r\n        <div class=\"button-container\">\r\n          <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loginForm.invalid || loading\">\r\n            {{ loading ? 'Logging in...' : 'Login' }}\r\n          </button>\r\n        </div>\r\n\r\n        <div class=\"links\">\r\n          <a routerLink=\"/auth/register\">Don't have an account? Register</a>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACcU,IAAA,yBAAA,GAAA,WAAA;AAAgE,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;;;;;AACjF,IAAA,yBAAA,GAAA,WAAA;AAA6D,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA;;;;;AAMvF,IAAA,yBAAA,GAAA,WAAA;AAAmE,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;;;;;AACvF,IAAA,yBAAA,GAAA,WAAA;AAAoE,IAAA,iBAAA,GAAA,wCAAA;AAAsC,IAAA,uBAAA;;;;;AAG5G,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAyC,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA;;;;AAAX,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;ADE3C,IAAO,iBAAP,MAAO,gBAAc;EAQf;EACA;EACA;EACA;EAVV;EACA,QAAgB;EAChB,UAAmB;EACF,0BAA0B;EAC1B,cAAc;EAE/B,YACU,aACA,QACA,OACA,aAAwB;AAHxB,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,QAAA;AACA,SAAA,cAAA;AAER,SAAK,YAAY,KAAK,YAAY,MAAM;MACtC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,UAAU,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;KAC9D;EACH;EAEM,WAAQ;;AACZ,UAAI,KAAK,UAAU,SAAS;AAC1B;MACF;AAEA,WAAK,UAAU;AACf,WAAK,QAAQ;AAEb,UAAI;AACF,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,YAAY,MACvC,KAAK,UAAU,MAAM,OACrB,KAAK,UAAU,MAAM,QAAQ;AAG/B,YAAI,OAAO;AACT,eAAK,QAAQ,MAAM;AACnB;QACF;AAGA,cAAM,OAAO,MAAM,eACjB,KAAK,YAAY,MAAM,KACrB,OAAO,CAAAA,UAAQA,UAAS,IAAI,CAAC,CAC9B;AAIH,YAAI,QAAQ,CAAC,KAAK,aAAa;AAC7B,gBAAM,UAAU,KAAK,SAAS,UAC5B,+DACA;AAEF,eAAK,QAAQ;AACb,gBAAM,KAAK,YAAY,OAAM;AAC7B;QACF;AAGA,YAAI,OAAO;AACX,YAAI,WAAW;AACf,eAAO,CAAC,QAAQ,WAAW,KAAK,yBAAyB;AACvD,kBAAQ,IAAI,gCAAgC;AAC5C,iBAAO,MAAM,KAAK,YAAY,YAAW;AACzC,cAAI,CAAC,QAAQ,WAAW,KAAK,0BAA0B,GAAG;AACxD,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,WAAW,CAAC;UACpE;AACA;QACF;AAEA,YAAI,MAAM;AACR,gBAAM,iBAAiB,KAAK,YAAY,yBAAyB,IAAI;AACrE,gBAAM,KAAK,OAAO,SAAS,CAAC,cAAc,CAAC;QAC7C,OAAO;AACL,eAAK,QAAQ;AACb,gBAAM,KAAK,YAAY,OAAM;QAC/B;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,gBAAgB,KAAK;AACnC,aAAK,QAAQ;MACf;AACE,aAAK,UAAU;MACjB;IACF;;;qCAjFW,iBAAc,4BAAA,WAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,QAAA,SAAA,mBAAA,SAAA,eAAA,kBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,YAAA,IAAA,QAAA,YAAA,mBAAA,YAAA,eAAA,qBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,cAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AC3B3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,UAAA,EAKjB,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,OAAA;AAAK,MAAA,uBAAA,EAAiB;AAExC,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,QAAA,CAAA;AACc,MAAA,qBAAA,YAAA,SAAA,mDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AAClD,MAAA,yBAAA,GAAA,kBAAA,CAAA,EAAqC,GAAA,WAAA;AACxB,MAAA,iBAAA,GAAA,OAAA;AAAK,MAAA,uBAAA;AAChB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,sCAAA,GAAA,GAAA,aAAA,CAAA,EAAgE,IAAA,sCAAA,GAAA,GAAA,aAAA,CAAA;AAElE,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACnB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,sCAAA,GAAA,GAAA,aAAA,CAAA,EAAmE,IAAA,sCAAA,GAAA,GAAA,aAAA,CAAA;AAErE,MAAA,uBAAA;AAEA,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,CAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,UAAA,CAAA;AAE1B,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS;AAGX,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAmB,IAAA,KAAA,EAAA;AACc,MAAA,iBAAA,IAAA,iCAAA;AAA+B,MAAA,uBAAA,EAAI,EAC9D,EACD,EACU,EACV;;;;;;;AA5BD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,SAAA;AAIU,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,UAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,UAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,OAAA,CAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,UAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,UAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,WAAA,CAAA;AAGc,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAG8B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,WAAA,IAAA,OAAA;AACtD,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,UAAA,kBAAA,SAAA,GAAA;;oBDbR,cAAY,MACZ,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,iBACnB,oBAAkB,cAAA,UAAA,UAClB,gBAAc,UACd,iBAAe,WACf,eAAa,SAAA,gBAAA,eAAA,cACb,UAAU,GAAA,QAAA,CAAA,8vDAAA,EAAA,CAAA;;;sEAKD,gBAAc,CAAA;UAf1B;uBACW,aAAW,YACT,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,gwCAAA,EAAA,CAAA;;;;6EAIU,gBAAc,EAAA,WAAA,kBAAA,UAAA,kDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["user"]}