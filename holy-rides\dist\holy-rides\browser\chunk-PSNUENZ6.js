import {
  MessageService
} from "./chunk-XQHBKW3P.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-R24BZZME.js";
import {
  SmsService
} from "./chunk-NVGZCMKL.js";
import {
  AuthService,
  DefaultValueAccessor,
  FormsModule,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  ANIMATION_MODULE_TYPE,
  BehaviorSubject,
  ChangeDetectionStrategy,
  CommonModule,
  Component,
  ElementRef,
  Injectable,
  InjectionToken,
  Input,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardActions,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  MatCommonModule,
  NgClass,
  NgForOf,
  NgIf,
  NgModule,
  NgTemplateOutlet,
  ViewChild,
  ViewEncapsulation,
  coerceBooleanProperty,
  inject,
  numberAttribute,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainer,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction2,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty,
  ɵɵviewQuery
} from "./chunk-THPQGTPB.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-S35DAJRX.js";

// src/app/core/services/ride.service.ts
var RideService = class _RideService {
  smsService;
  authService;
  _supabase;
  ridesSubject = new BehaviorSubject([]);
  rides$ = this.ridesSubject.asObservable();
  rideSubscription = null;
  // Expose supabase client for use in other components
  get supabase() {
    return this._supabase;
  }
  constructor(smsService, authService) {
    this.smsService = smsService;
    this.authService = authService;
    this._supabase = authService.supabase;
    this.initializeRealTimeSubscription();
  }
  ngOnDestroy() {
    if (this.rideSubscription) {
      console.log("\u{1F9F9} Cleaning up rides realtime subscription");
      this.rideSubscription.unsubscribe();
      this.rideSubscription = null;
    }
  }
  initializeRealTimeSubscription() {
    if (this.rideSubscription) {
      this.rideSubscription.unsubscribe();
    }
    this.rideSubscription = this._supabase.channel("rides-channel").on("postgres_changes", { event: "*", schema: "public", table: "rides" }, (payload) => __async(this, null, function* () {
      console.log("\u{1F504} Realtime event received:", payload.eventType, payload.new || payload.old);
      try {
        yield this.refreshRides();
        console.log("\u2705 Rides refreshed successfully after realtime event");
      } catch (error) {
        console.error("\u274C Error refreshing rides after realtime event:", error);
      }
    })).subscribe((status) => {
      console.log("\u{1F4E1} Realtime subscription status:", status);
      if (status === "SUBSCRIBED") {
        console.log("\u2705 Successfully subscribed to rides realtime updates");
      } else if (status === "CHANNEL_ERROR") {
        console.error("\u274C Realtime subscription error, attempting to reconnect...");
        setTimeout(() => {
          this.initializeRealTimeSubscription();
        }, 5e3);
      }
    });
  }
  refreshRides() {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").order("created_at", { ascending: false });
        if (error)
          throw error;
        this.ridesSubject.next(data || []);
      } catch (error) {
        console.error("Error refreshing rides:", error);
      }
    });
  }
  getAllRides() {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").order("created_at", { ascending: false });
        if (error)
          throw error;
        this.ridesSubject.next(data || []);
        return data || [];
      } catch (error) {
        console.error("Error fetching all rides:", error);
        return this.ridesSubject.value;
      }
    });
  }
  getRidesByStatus(status) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").eq("status", status).order("created_at", { ascending: false });
        if (error)
          throw error;
        return data;
      } catch (error) {
        console.error(`Error fetching rides with status ${status}:`, error);
        return [];
      }
    });
  }
  assignRideToDriver(rideId, driverId) {
    return __async(this, null, function* () {
      try {
        const currentRide = yield this.getRide(rideId);
        if (!currentRide || currentRide.status !== "requested" && currentRide.status !== "assigned") {
          throw new Error("Ride is no longer available for assignment");
        }
        const { error } = yield this._supabase.from("rides").update({
          driver_id: driverId,
          status: "assigned",
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }).eq("id", rideId).in("status", ["requested", "assigned"]);
        if (error)
          throw error;
        const updatedRide = yield this.getRide(rideId);
        if (updatedRide) {
          setTimeout(() => __async(this, null, function* () {
            try {
              yield this.smsService.sendRideAssignmentNotifications(updatedRide, driverId);
            } catch (smsError) {
              console.error("Error sending SMS notifications:", smsError);
            }
          }), 0);
          console.log(`Sending ride assignment notifications for ride ${rideId} to rider and driver ${driverId}`);
        }
        return true;
      } catch (error) {
        console.error("Error assigning ride to driver:", error);
        throw error;
      }
    });
  }
  updateRideStatus(rideId, status) {
    return __async(this, null, function* () {
      try {
        const currentRide = yield this.getRide(rideId);
        if (!currentRide) {
          throw new Error("Ride not found");
        }
        if (!this.isValidStatusTransition(currentRide.status, status)) {
          throw new Error(`Invalid status transition from ${currentRide.status} to ${status}`);
        }
        const { error } = yield this._supabase.from("rides").update({
          status,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }).eq("id", rideId);
        if (error)
          throw error;
        yield this.refreshRides();
        const updatedRide = yield this.getRide(rideId);
        if (updatedRide) {
          setTimeout(() => __async(this, null, function* () {
            try {
              if (status === "canceled") {
                yield this.smsService.sendRideCancellationNotifications(updatedRide);
              } else if (status === "completed") {
                yield this.smsService.sendRideCompletionNotification(updatedRide);
                yield this.smsService.sendAdminRideStatusNotification(updatedRide, status);
              } else if (status === "in-progress") {
                yield this.smsService.sendRideStatusUpdateNotifications(updatedRide, status);
                yield this.smsService.sendAdminRideStatusNotification(updatedRide, status);
              }
            } catch (smsError) {
              console.error("Error sending status update notifications:", smsError);
            }
          }), 0);
          console.log(`Sending ride status update (${status}) notifications for ride ${rideId}`);
        }
        return true;
      } catch (error) {
        console.error("Error updating ride status:", error);
        throw error;
      }
    });
  }
  isValidStatusTransition(from, to) {
    const transitions = {
      "requested": ["assigned", "canceled"],
      "assigned": ["in-progress", "canceled"],
      "in-progress": ["completed", "canceled"],
      "completed": [],
      "canceled": []
    };
    return transitions[from]?.includes(to) || false;
  }
  createRide(ride) {
    return __async(this, null, function* () {
      try {
        const rideWithPaymentStatus = __spreadProps(__spreadValues({}, ride), {
          payment_status: ride.payment_status || "pending"
        });
        const { data, error } = yield this._supabase.from("rides").insert([rideWithPaymentStatus]).select().single();
        if (error)
          throw error;
        const currentRides = this.ridesSubject.value;
        this.ridesSubject.next([...currentRides, data]);
        setTimeout(() => __async(this, null, function* () {
          try {
          } catch (smsError) {
            console.error("Error sending ride booking notifications:", smsError);
          }
        }), 0);
        console.log(`Sending ride booking notifications for ride ${data.id}`);
        return data;
      } catch (error) {
        console.error("Error creating ride:", error);
        throw error;
      }
    });
  }
  getUserRides(userId) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").eq("rider_id", userId).order("created_at", { ascending: false });
        if (error)
          throw error;
        this.ridesSubject.next(data);
        return data;
      } catch (error) {
        console.error("Error fetching user rides:", error);
        return [];
      }
    });
  }
  getDriverRides(driverId) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").eq("driver_id", driverId).order("created_at", { ascending: false });
        if (error)
          throw error;
        return data;
      } catch (error) {
        console.error("Error fetching driver rides:", error);
        return [];
      }
    });
  }
  getAvailableRides() {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").eq("status", "requested").order("created_at", { ascending: false });
        if (error)
          throw error;
        return data;
      } catch (error) {
        console.error("Error fetching available rides:", error);
        return [];
      }
    });
  }
  acceptRide(rideId, driverId) {
    return __async(this, null, function* () {
      return this.assignRideToDriver(rideId, driverId);
    });
  }
  startRide(rideId) {
    return __async(this, null, function* () {
      return this.updateRideStatus(rideId, "in-progress");
    });
  }
  completeRide(rideId) {
    return __async(this, null, function* () {
      return this.updateRideStatus(rideId, "completed");
    });
  }
  getRide(rideId) {
    return __async(this, null, function* () {
      try {
        const { data, error } = yield this._supabase.from("rides").select("*").eq("id", rideId).single();
        if (error)
          throw error;
        return data;
      } catch (error) {
        console.error("Error fetching ride:", error);
        return null;
      }
    });
  }
  updateRide(rideId, updates) {
    return __async(this, null, function* () {
      try {
        const { error } = yield this._supabase.from("rides").update(__spreadProps(__spreadValues({}, updates), {
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        })).eq("id", rideId);
        if (error)
          throw error;
        if (updates.payment_status && (updates.payment_status === "paid" || updates.payment_status === "completed")) {
          setTimeout(() => __async(this, null, function* () {
            try {
              const ride = yield this.getRide(rideId);
              if (ride && updates.amount) {
                yield this.smsService.sendPaymentConfirmationNotification(ride, updates.amount);
              }
            } catch (smsError) {
              console.error("Error sending payment confirmation notification:", smsError);
            }
          }), 0);
        }
        yield this.getAllRides();
        return true;
      } catch (error) {
        console.error("Error updating ride:", error);
        return false;
      }
    });
  }
  cancelRide(rideId) {
    return __async(this, null, function* () {
      return this.updateRideStatus(rideId, "canceled");
    });
  }
  filterRides(rides, filter) {
    return rides.filter((ride) => {
      if (filter.status && ride.status !== filter.status) {
        return false;
      }
      if (filter.riderId && ride.rider_id !== filter.riderId) {
        return false;
      }
      if (filter.driverId && ride.driver_id !== filter.driverId) {
        return false;
      }
      if (filter.dateRange) {
        const rideDate = new Date(ride.created_at);
        const startDate = filter.dateRange.start;
        const endDate = filter.dateRange.end;
        if (rideDate < startDate || rideDate > endDate) {
          return false;
        }
      }
      return true;
    });
  }
  static \u0275fac = function RideService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RideService)(\u0275\u0275inject(SmsService), \u0275\u0275inject(AuthService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _RideService, factory: _RideService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RideService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: SmsService }, { type: AuthService }], null);
})();

// node_modules/@angular/material/fesm2022/divider.mjs
var MatDivider = class _MatDivider {
  /** Whether the divider is vertically aligned. */
  get vertical() {
    return this._vertical;
  }
  set vertical(value) {
    this._vertical = coerceBooleanProperty(value);
  }
  _vertical = false;
  /** Whether the divider is an inset divider. */
  get inset() {
    return this._inset;
  }
  set inset(value) {
    this._inset = coerceBooleanProperty(value);
  }
  _inset = false;
  static \u0275fac = function MatDivider_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatDivider)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
    type: _MatDivider,
    selectors: [["mat-divider"]],
    hostAttrs: ["role", "separator", 1, "mat-divider"],
    hostVars: 7,
    hostBindings: function MatDivider_HostBindings(rf, ctx) {
      if (rf & 2) {
        \u0275\u0275attribute("aria-orientation", ctx.vertical ? "vertical" : "horizontal");
        \u0275\u0275classProp("mat-divider-vertical", ctx.vertical)("mat-divider-horizontal", !ctx.vertical)("mat-divider-inset", ctx.inset);
      }
    },
    inputs: {
      vertical: "vertical",
      inset: "inset"
    },
    decls: 0,
    vars: 0,
    template: function MatDivider_Template(rf, ctx) {
    },
    styles: [".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\n"],
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatDivider, [{
    type: Component,
    args: [{
      selector: "mat-divider",
      host: {
        "role": "separator",
        "[attr.aria-orientation]": 'vertical ? "vertical" : "horizontal"',
        "[class.mat-divider-vertical]": "vertical",
        "[class.mat-divider-horizontal]": "!vertical",
        "[class.mat-divider-inset]": "inset",
        "class": "mat-divider"
      },
      template: "",
      encapsulation: ViewEncapsulation.None,
      changeDetection: ChangeDetectionStrategy.OnPush,
      styles: [".mat-divider{display:block;margin:0;border-top-style:solid;border-top-color:var(--mat-divider-color, var(--mat-sys-outline));border-top-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-vertical{border-top:0;border-right-style:solid;border-right-color:var(--mat-divider-color, var(--mat-sys-outline));border-right-width:var(--mat-divider-width, 1px)}.mat-divider.mat-divider-inset{margin-left:80px}[dir=rtl] .mat-divider.mat-divider-inset{margin-left:auto;margin-right:80px}\n"]
    }]
  }], null, {
    vertical: [{
      type: Input
    }],
    inset: [{
      type: Input
    }]
  });
})();
var MatDividerModule = class _MatDividerModule {
  static \u0275fac = function MatDividerModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatDividerModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
    type: _MatDividerModule
  });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
    imports: [MatCommonModule, MatCommonModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatDividerModule, [{
    type: NgModule,
    args: [{
      imports: [MatCommonModule, MatDivider],
      exports: [MatDivider, MatCommonModule]
    }]
  }], null, null);
})();

// node_modules/@angular/material/fesm2022/progress-spinner.mjs
var _c0 = ["determinateSpinner"];
function MatProgressSpinner_ng_template_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 11);
    \u0275\u0275element(1, "circle", 12);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275attribute("viewBox", ctx_r0._viewBox());
    \u0275\u0275advance();
    \u0275\u0275styleProp("stroke-dasharray", ctx_r0._strokeCircumference(), "px")("stroke-dashoffset", ctx_r0._strokeCircumference() / 2, "px")("stroke-width", ctx_r0._circleStrokeWidth(), "%");
    \u0275\u0275attribute("r", ctx_r0._circleRadius());
  }
}
var MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS = new InjectionToken("mat-progress-spinner-default-options", {
  providedIn: "root",
  factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY
});
function MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY() {
  return {
    diameter: BASE_SIZE
  };
}
var BASE_SIZE = 100;
var BASE_STROKE_WIDTH = 10;
var MatProgressSpinner = class _MatProgressSpinner {
  _elementRef = inject(ElementRef);
  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */
  _noopAnimations;
  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.
  /**
   * Theme color of the progress spinner. This API is supported in M2 themes only, it
   * has no effect in M3 themes. For color customization in M3, see https://material.angular.io/components/progress-spinner/styling.
   *
   * For information on applying color variants in M3, see
   * https://material.angular.io/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants
   */
  get color() {
    return this._color || this._defaultColor;
  }
  set color(value) {
    this._color = value;
  }
  _color;
  _defaultColor = "primary";
  /** The element of the determinate spinner. */
  _determinateCircle;
  constructor() {
    const animationMode = inject(ANIMATION_MODULE_TYPE, {
      optional: true
    });
    const defaults = inject(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);
    this._noopAnimations = animationMode === "NoopAnimations" && !!defaults && !defaults._forceAnimations;
    this.mode = this._elementRef.nativeElement.nodeName.toLowerCase() === "mat-spinner" ? "indeterminate" : "determinate";
    if (defaults) {
      if (defaults.color) {
        this.color = this._defaultColor = defaults.color;
      }
      if (defaults.diameter) {
        this.diameter = defaults.diameter;
      }
      if (defaults.strokeWidth) {
        this.strokeWidth = defaults.strokeWidth;
      }
    }
  }
  /**
   * Mode of the progress bar.
   *
   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to
   * 'determinate'.
   * Mirrored to mode attribute.
   */
  mode;
  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */
  get value() {
    return this.mode === "determinate" ? this._value : 0;
  }
  set value(v) {
    this._value = Math.max(0, Math.min(100, v || 0));
  }
  _value = 0;
  /** The diameter of the progress spinner (will set width and height of svg). */
  get diameter() {
    return this._diameter;
  }
  set diameter(size) {
    this._diameter = size || 0;
  }
  _diameter = BASE_SIZE;
  /** Stroke width of the progress spinner. */
  get strokeWidth() {
    return this._strokeWidth ?? this.diameter / 10;
  }
  set strokeWidth(value) {
    this._strokeWidth = value || 0;
  }
  _strokeWidth;
  /** The radius of the spinner, adjusted for stroke width. */
  _circleRadius() {
    return (this.diameter - BASE_STROKE_WIDTH) / 2;
  }
  /** The view box of the spinner's svg element. */
  _viewBox() {
    const viewBox = this._circleRadius() * 2 + this.strokeWidth;
    return `0 0 ${viewBox} ${viewBox}`;
  }
  /** The stroke circumference of the svg circle. */
  _strokeCircumference() {
    return 2 * Math.PI * this._circleRadius();
  }
  /** The dash offset of the svg circle. */
  _strokeDashOffset() {
    if (this.mode === "determinate") {
      return this._strokeCircumference() * (100 - this._value) / 100;
    }
    return null;
  }
  /** Stroke width of the circle in percent. */
  _circleStrokeWidth() {
    return this.strokeWidth / this.diameter * 100;
  }
  static \u0275fac = function MatProgressSpinner_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatProgressSpinner)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({
    type: _MatProgressSpinner,
    selectors: [["mat-progress-spinner"], ["mat-spinner"]],
    viewQuery: function MatProgressSpinner_Query(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275viewQuery(_c0, 5);
      }
      if (rf & 2) {
        let _t;
        \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx._determinateCircle = _t.first);
      }
    },
    hostAttrs: ["role", "progressbar", "tabindex", "-1", 1, "mat-mdc-progress-spinner", "mdc-circular-progress"],
    hostVars: 18,
    hostBindings: function MatProgressSpinner_HostBindings(rf, ctx) {
      if (rf & 2) {
        \u0275\u0275attribute("aria-valuemin", 0)("aria-valuemax", 100)("aria-valuenow", ctx.mode === "determinate" ? ctx.value : null)("mode", ctx.mode);
        \u0275\u0275classMap("mat-" + ctx.color);
        \u0275\u0275styleProp("width", ctx.diameter, "px")("height", ctx.diameter, "px")("--mdc-circular-progress-size", ctx.diameter + "px")("--mdc-circular-progress-active-indicator-width", ctx.diameter + "px");
        \u0275\u0275classProp("_mat-animation-noopable", ctx._noopAnimations)("mdc-circular-progress--indeterminate", ctx.mode === "indeterminate");
      }
    },
    inputs: {
      color: "color",
      mode: "mode",
      value: [2, "value", "value", numberAttribute],
      diameter: [2, "diameter", "diameter", numberAttribute],
      strokeWidth: [2, "strokeWidth", "strokeWidth", numberAttribute]
    },
    exportAs: ["matProgressSpinner"],
    decls: 14,
    vars: 11,
    consts: [["circle", ""], ["determinateSpinner", ""], ["aria-hidden", "true", 1, "mdc-circular-progress__determinate-container"], ["xmlns", "http://www.w3.org/2000/svg", "focusable", "false", 1, "mdc-circular-progress__determinate-circle-graphic"], ["cx", "50%", "cy", "50%", 1, "mdc-circular-progress__determinate-circle"], ["aria-hidden", "true", 1, "mdc-circular-progress__indeterminate-container"], [1, "mdc-circular-progress__spinner-layer"], [1, "mdc-circular-progress__circle-clipper", "mdc-circular-progress__circle-left"], [3, "ngTemplateOutlet"], [1, "mdc-circular-progress__gap-patch"], [1, "mdc-circular-progress__circle-clipper", "mdc-circular-progress__circle-right"], ["xmlns", "http://www.w3.org/2000/svg", "focusable", "false", 1, "mdc-circular-progress__indeterminate-circle-graphic"], ["cx", "50%", "cy", "50%"]],
    template: function MatProgressSpinner_Template(rf, ctx) {
      if (rf & 1) {
        \u0275\u0275template(0, MatProgressSpinner_ng_template_0_Template, 2, 8, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
        \u0275\u0275elementStart(2, "div", 2, 1);
        \u0275\u0275namespaceSVG();
        \u0275\u0275elementStart(4, "svg", 3);
        \u0275\u0275element(5, "circle", 4);
        \u0275\u0275elementEnd()();
        \u0275\u0275namespaceHTML();
        \u0275\u0275elementStart(6, "div", 5)(7, "div", 6)(8, "div", 7);
        \u0275\u0275elementContainer(9, 8);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(10, "div", 9);
        \u0275\u0275elementContainer(11, 8);
        \u0275\u0275elementEnd();
        \u0275\u0275elementStart(12, "div", 10);
        \u0275\u0275elementContainer(13, 8);
        \u0275\u0275elementEnd()()();
      }
      if (rf & 2) {
        const circle_r2 = \u0275\u0275reference(1);
        \u0275\u0275advance(4);
        \u0275\u0275attribute("viewBox", ctx._viewBox());
        \u0275\u0275advance();
        \u0275\u0275styleProp("stroke-dasharray", ctx._strokeCircumference(), "px")("stroke-dashoffset", ctx._strokeDashOffset(), "px")("stroke-width", ctx._circleStrokeWidth(), "%");
        \u0275\u0275attribute("r", ctx._circleRadius());
        \u0275\u0275advance(4);
        \u0275\u0275property("ngTemplateOutlet", circle_r2);
        \u0275\u0275advance(2);
        \u0275\u0275property("ngTemplateOutlet", circle_r2);
        \u0275\u0275advance(2);
        \u0275\u0275property("ngTemplateOutlet", circle_r2);
      }
    },
    dependencies: [NgTemplateOutlet],
    styles: [".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\n"],
    encapsulation: 2,
    changeDetection: 0
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatProgressSpinner, [{
    type: Component,
    args: [{
      selector: "mat-progress-spinner, mat-spinner",
      exportAs: "matProgressSpinner",
      host: {
        "role": "progressbar",
        "class": "mat-mdc-progress-spinner mdc-circular-progress",
        // set tab index to -1 so screen readers will read the aria-label
        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox
        "tabindex": "-1",
        "[class]": '"mat-" + color',
        "[class._mat-animation-noopable]": `_noopAnimations`,
        "[class.mdc-circular-progress--indeterminate]": 'mode === "indeterminate"',
        "[style.width.px]": "diameter",
        "[style.height.px]": "diameter",
        "[style.--mdc-circular-progress-size]": 'diameter + "px"',
        "[style.--mdc-circular-progress-active-indicator-width]": 'diameter + "px"',
        "[attr.aria-valuemin]": "0",
        "[attr.aria-valuemax]": "100",
        "[attr.aria-valuenow]": 'mode === "determinate" ? value : null',
        "[attr.mode]": "mode"
      },
      changeDetection: ChangeDetectionStrategy.OnPush,
      encapsulation: ViewEncapsulation.None,
      imports: [NgTemplateOutlet],
      template: '<ng-template #circle>\n  <svg [attr.viewBox]="_viewBox()" class="mdc-circular-progress__indeterminate-circle-graphic"\n       xmlns="http://www.w3.org/2000/svg" focusable="false">\n    <circle [attr.r]="_circleRadius()"\n            [style.stroke-dasharray.px]="_strokeCircumference()"\n            [style.stroke-dashoffset.px]="_strokeCircumference() / 2"\n            [style.stroke-width.%]="_circleStrokeWidth()"\n            cx="50%" cy="50%"/>\n  </svg>\n</ng-template>\n\n<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class="mdc-circular-progress__determinate-container" aria-hidden="true" #determinateSpinner>\n  <svg [attr.viewBox]="_viewBox()" class="mdc-circular-progress__determinate-circle-graphic"\n       xmlns="http://www.w3.org/2000/svg" focusable="false">\n    <circle [attr.r]="_circleRadius()"\n            [style.stroke-dasharray.px]="_strokeCircumference()"\n            [style.stroke-dashoffset.px]="_strokeDashOffset()"\n            [style.stroke-width.%]="_circleStrokeWidth()"\n            class="mdc-circular-progress__determinate-circle"\n            cx="50%" cy="50%"/>\n  </svg>\n</div>\n<!--TODO: figure out why there are 3 separate svgs-->\n<div class="mdc-circular-progress__indeterminate-container" aria-hidden="true">\n  <div class="mdc-circular-progress__spinner-layer">\n    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">\n      <ng-container [ngTemplateOutlet]="circle"></ng-container>\n    </div>\n    <div class="mdc-circular-progress__gap-patch">\n      <ng-container [ngTemplateOutlet]="circle"></ng-container>\n    </div>\n    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">\n      <ng-container [ngTemplateOutlet]="circle"></ng-container>\n    </div>\n  </div>\n</div>\n',
      styles: [".mat-mdc-progress-spinner{display:block;overflow:hidden;line-height:0;position:relative;direction:ltr;transition:opacity 250ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-progress-spinner circle{stroke-width:var(--mdc-circular-progress-active-indicator-width, 4px)}.mat-mdc-progress-spinner._mat-animation-noopable,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__determinate-circle{transition:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__spinner-layer,.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container{animation:none !important}.mat-mdc-progress-spinner._mat-animation-noopable .mdc-circular-progress__indeterminate-container circle{stroke-dasharray:0 !important}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic,.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle{stroke:currentColor;stroke:CanvasText}}.mdc-circular-progress__determinate-container,.mdc-circular-progress__indeterminate-circle-graphic,.mdc-circular-progress__indeterminate-container,.mdc-circular-progress__spinner-layer{position:absolute;width:100%;height:100%}.mdc-circular-progress__determinate-container{transform:rotate(-90deg)}.mdc-circular-progress--indeterminate .mdc-circular-progress__determinate-container{opacity:0}.mdc-circular-progress__indeterminate-container{font-size:0;letter-spacing:0;white-space:nowrap;opacity:0}.mdc-circular-progress--indeterminate .mdc-circular-progress__indeterminate-container{opacity:1;animation:mdc-circular-progress-container-rotate 1568.2352941176ms linear infinite}.mdc-circular-progress__determinate-circle-graphic,.mdc-circular-progress__indeterminate-circle-graphic{fill:rgba(0,0,0,0)}.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:var(--mdc-circular-progress-active-indicator-color, var(--mat-sys-primary))}@media(forced-colors: active){.mat-mdc-progress-spinner .mdc-circular-progress__determinate-circle,.mat-mdc-progress-spinner .mdc-circular-progress__indeterminate-circle-graphic{stroke:CanvasText}}.mdc-circular-progress__determinate-circle{transition:stroke-dashoffset 500ms cubic-bezier(0, 0, 0.2, 1)}.mdc-circular-progress__gap-patch{position:absolute;top:0;left:47.5%;box-sizing:border-box;width:5%;height:100%;overflow:hidden}.mdc-circular-progress__gap-patch .mdc-circular-progress__indeterminate-circle-graphic{left:-900%;width:2000%;transform:rotate(180deg)}.mdc-circular-progress__circle-clipper .mdc-circular-progress__indeterminate-circle-graphic{width:200%}.mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{left:-100%}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-left .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress--indeterminate .mdc-circular-progress__circle-right .mdc-circular-progress__indeterminate-circle-graphic{animation:mdc-circular-progress-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}.mdc-circular-progress__circle-clipper{display:inline-flex;position:relative;width:50%;height:100%;overflow:hidden}.mdc-circular-progress--indeterminate .mdc-circular-progress__spinner-layer{animation:mdc-circular-progress-spinner-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both}@keyframes mdc-circular-progress-container-rotate{to{transform:rotate(360deg)}}@keyframes mdc-circular-progress-spinner-layer-rotate{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}100%{transform:rotate(1080deg)}}@keyframes mdc-circular-progress-left-spin{from{transform:rotate(265deg)}50%{transform:rotate(130deg)}to{transform:rotate(265deg)}}@keyframes mdc-circular-progress-right-spin{from{transform:rotate(-265deg)}50%{transform:rotate(-130deg)}to{transform:rotate(-265deg)}}\n"]
    }]
  }], () => [], {
    color: [{
      type: Input
    }],
    _determinateCircle: [{
      type: ViewChild,
      args: ["determinateSpinner"]
    }],
    mode: [{
      type: Input
    }],
    value: [{
      type: Input,
      args: [{
        transform: numberAttribute
      }]
    }],
    diameter: [{
      type: Input,
      args: [{
        transform: numberAttribute
      }]
    }],
    strokeWidth: [{
      type: Input,
      args: [{
        transform: numberAttribute
      }]
    }]
  });
})();
var MatSpinner = MatProgressSpinner;
var MatProgressSpinnerModule = class _MatProgressSpinnerModule {
  static \u0275fac = function MatProgressSpinnerModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatProgressSpinnerModule)();
  };
  static \u0275mod = /* @__PURE__ */ \u0275\u0275defineNgModule({
    type: _MatProgressSpinnerModule
  });
  static \u0275inj = /* @__PURE__ */ \u0275\u0275defineInjector({
    imports: [MatCommonModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatProgressSpinnerModule, [{
    type: NgModule,
    args: [{
      imports: [MatProgressSpinner, MatSpinner],
      exports: [MatProgressSpinner, MatSpinner, MatCommonModule]
    }]
  }], null, null);
})();

// src/app/shared/components/ride-chat/ride-chat.component.ts
var _c02 = (a0, a1) => ({ "sent": a0, "received": a1 });
function RideChatComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 9);
    \u0275\u0275element(1, "mat-spinner", 10);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading messages...");
    \u0275\u0275elementEnd()();
  }
}
function RideChatComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11)(1, "p");
    \u0275\u0275text(2, "No messages yet. Start the conversation!");
    \u0275\u0275elementEnd()();
  }
}
function RideChatComponent_div_8_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "div", 15);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 16);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const message_r1 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction2(3, _c02, message_r1.sender_id === ctx_r1.currentUserId, message_r1.sender_id !== ctx_r1.currentUserId));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(message_r1.content);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatTime(message_r1.created_at));
  }
}
function RideChatComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12);
    \u0275\u0275template(1, RideChatComponent_div_8_div_1_Template, 5, 6, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r1.messages);
  }
}
var RideChatComponent = class _RideChatComponent {
  messageService;
  authService;
  rideService;
  threadId;
  rideId;
  messages = [];
  newMessage = "";
  loading = false;
  sending = false;
  currentUserId = "";
  receiverId = "";
  constructor(messageService, authService, rideService) {
    this.messageService = messageService;
    this.authService = authService;
    this.rideService = rideService;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      this.loading = true;
      try {
        const user = yield this.authService.getCurrentUser();
        if (user) {
          this.currentUserId = user.id;
        }
        if (this.rideId && !this.threadId) {
          const thread = yield this.messageService.getOrCreateThreadForRide(this.rideId);
          this.threadId = thread.id;
        }
        if (this.rideId) {
          const ride = yield this.rideService.getRide(this.rideId);
          if (ride) {
            this.receiverId = ride.rider_id === this.currentUserId ? ride.driver_id : ride.rider_id;
          }
        }
        if (this.threadId) {
          this.messages = yield this.messageService.getThreadMessages(this.threadId);
          yield this.messageService.markMessagesAsRead(this.threadId);
        }
      } catch (error) {
        console.error("Error initializing chat:", error);
      } finally {
        this.loading = false;
      }
    });
  }
  sendMessage() {
    return __async(this, null, function* () {
      if (!this.newMessage || !this.threadId || !this.receiverId)
        return;
      this.sending = true;
      try {
        yield this.messageService.sendMessage(this.threadId, this.receiverId, this.newMessage);
        this.newMessage = "";
        this.messages = yield this.messageService.getThreadMessages(this.threadId);
      } catch (error) {
        console.error("Error sending message:", error);
      } finally {
        this.sending = false;
      }
    });
  }
  formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  }
  static \u0275fac = function RideChatComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RideChatComponent)(\u0275\u0275directiveInject(MessageService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(RideService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RideChatComponent, selectors: [["app-ride-chat"]], inputs: { threadId: "threadId", rideId: "rideId" }, decls: 16, vars: 5, consts: [[1, "chat-card"], [1, "messages-container"], ["class", "loading-container", 4, "ngIf"], ["class", "no-messages", 4, "ngIf"], ["class", "messages-list", 4, "ngIf"], [1, "message-form", 3, "ngSubmit"], ["appearance", "outline", 1, "message-input"], ["matInput", "", "name", "newMessage", "placeholder", "Type a message...", "autocomplete", "off", 3, "ngModelChange", "ngModel"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], [1, "loading-container"], ["diameter", "40"], [1, "no-messages"], [1, "messages-list"], ["class", "message-bubble", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "message-bubble", 3, "ngClass"], [1, "message-content"], [1, "message-time"]], template: function RideChatComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card", 0)(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Chat");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "mat-card-content")(5, "div", 1);
      \u0275\u0275template(6, RideChatComponent_div_6_Template, 4, 0, "div", 2)(7, RideChatComponent_div_7_Template, 3, 0, "div", 3)(8, RideChatComponent_div_8_Template, 2, 1, "div", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "mat-card-actions")(10, "form", 5);
      \u0275\u0275listener("ngSubmit", function RideChatComponent_Template_form_ngSubmit_10_listener() {
        return ctx.sendMessage();
      });
      \u0275\u0275elementStart(11, "mat-form-field", 6)(12, "input", 7);
      \u0275\u0275twoWayListener("ngModelChange", function RideChatComponent_Template_input_ngModelChange_12_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.newMessage, $event) || (ctx.newMessage = $event);
        return $event;
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "button", 8)(14, "mat-icon");
      \u0275\u0275text(15, "send");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.messages.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.messages.length > 0);
      \u0275\u0275advance(4);
      \u0275\u0275twoWayProperty("ngModel", ctx.newMessage);
      \u0275\u0275advance();
      \u0275\u0275property("disabled", !ctx.newMessage || ctx.sending);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, FormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, NgModel, NgForm, MatCardModule, MatCard, MatCardActions, MatCardContent, MatCardHeader, MatCardTitle, MatFormFieldModule, MatFormField, MatInputModule, MatInput, MatButtonModule, MatButton, MatIconModule, MatIcon, MatProgressSpinnerModule, MatProgressSpinner], styles: ["\n\n.chat-card[_ngcontent-%COMP%] {\n  margin: 16px;\n  max-width: 800px;\n}\n.messages-container[_ngcontent-%COMP%] {\n  height: 300px;\n  overflow-y: auto;\n  padding: 16px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n.no-messages[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(0, 0, 0, 0.5);\n}\n.messages-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n.message-bubble[_ngcontent-%COMP%] {\n  max-width: 80%;\n  padding: 8px 12px;\n  border-radius: 16px;\n  position: relative;\n}\n.sent[_ngcontent-%COMP%] {\n  align-self: flex-end;\n  background-color: #2196f3;\n  color: white;\n  border-bottom-right-radius: 4px;\n}\n.received[_ngcontent-%COMP%] {\n  align-self: flex-start;\n  background-color: white;\n  border-bottom-left-radius: 4px;\n}\n.message-content[_ngcontent-%COMP%] {\n  word-break: break-word;\n}\n.message-time[_ngcontent-%COMP%] {\n  font-size: 0.7em;\n  opacity: 0.7;\n  text-align: right;\n  margin-top: 4px;\n}\n.message-form[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  width: 100%;\n  padding: 0 16px 16px;\n}\n.message-input[_ngcontent-%COMP%] {\n  flex: 1;\n}\n/*# sourceMappingURL=ride-chat.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RideChatComponent, [{
    type: Component,
    args: [{ selector: "app-ride-chat", standalone: true, imports: [
      CommonModule,
      FormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatIconModule,
      MatProgressSpinnerModule
    ], template: `
    <mat-card class="chat-card">
      <mat-card-header>
        <mat-card-title>Chat</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="messages-container">
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading messages...</p>
          </div>
          
          <div *ngIf="!loading && messages.length === 0" class="no-messages">
            <p>No messages yet. Start the conversation!</p>
          </div>
          
          <div *ngIf="!loading && messages.length > 0" class="messages-list">
            <div *ngFor="let message of messages" 
                 class="message-bubble" 
                 [ngClass]="{'sent': message.sender_id === currentUserId, 'received': message.sender_id !== currentUserId}">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.created_at) }}</div>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <form (ngSubmit)="sendMessage()" class="message-form">
          <mat-form-field appearance="outline" class="message-input">
            <input matInput [(ngModel)]="newMessage" name="newMessage" placeholder="Type a message..." autocomplete="off">
          </mat-form-field>
          <button mat-raised-button color="primary" type="submit" [disabled]="!newMessage || sending">
            <mat-icon>send</mat-icon>
          </button>
        </form>
      </mat-card-actions>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;f65ed01586b5f97ae4d7ea647dfb28ed66d5c44eed68892344d9035c5118201b;C:/Users/<USER>/code/holy rides/holy-rides/src/app/shared/components/ride-chat/ride-chat.component.ts */\n.chat-card {\n  margin: 16px;\n  max-width: 800px;\n}\n.messages-container {\n  height: 300px;\n  overflow-y: auto;\n  padding: 16px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n.no-messages {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: rgba(0, 0, 0, 0.5);\n}\n.messages-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n.message-bubble {\n  max-width: 80%;\n  padding: 8px 12px;\n  border-radius: 16px;\n  position: relative;\n}\n.sent {\n  align-self: flex-end;\n  background-color: #2196f3;\n  color: white;\n  border-bottom-right-radius: 4px;\n}\n.received {\n  align-self: flex-start;\n  background-color: white;\n  border-bottom-left-radius: 4px;\n}\n.message-content {\n  word-break: break-word;\n}\n.message-time {\n  font-size: 0.7em;\n  opacity: 0.7;\n  text-align: right;\n  margin-top: 4px;\n}\n.message-form {\n  display: flex;\n  gap: 8px;\n  width: 100%;\n  padding: 0 16px 16px;\n}\n.message-input {\n  flex: 1;\n}\n/*# sourceMappingURL=ride-chat.component.css.map */\n"] }]
  }], () => [{ type: MessageService }, { type: AuthService }, { type: RideService }], { threadId: [{
    type: Input
  }], rideId: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RideChatComponent, { className: "RideChatComponent", filePath: "src/app/shared/components/ride-chat/ride-chat.component.ts", lineNumber: 144 });
})();

export {
  RideService,
  MatDivider,
  MatDividerModule,
  MatProgressSpinner,
  MatProgressSpinnerModule,
  RideChatComponent
};
//# sourceMappingURL=chunk-PSNUENZ6.js.map
