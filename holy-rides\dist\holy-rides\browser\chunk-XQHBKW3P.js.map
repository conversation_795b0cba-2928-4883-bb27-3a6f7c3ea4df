{"version": 3, "sources": ["src/app/core/services/message.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { BehaviorSubject } from 'rxjs';\nimport { AuthService } from './auth.service';\n\ninterface Message {\n  id: string;\n  thread_id: string;\n  sender_id: string;\n  receiver_id: string;\n  content: string;\n  is_read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface MessageThread {\n  id: string;\n  ride_id: string;\n  created_at: string;\n  updated_at: string;\n  rides: {\n    rider_id: string;\n    driver_id: string;\n    pickup_location: string;\n    dropoff_location: string;\n  };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MessageService {\n  private supabase: SupabaseClient;\n  private messagesSubject = new BehaviorSubject<Message[]>([]);\n  messages$ = this.messagesSubject.asObservable();\n  private threadsSubject = new BehaviorSubject<MessageThread[]>([]);\n  threads$ = this.threadsSubject.asObservable();\n\n  constructor(private authService: AuthService) {\n    this.supabase = authService.supabase;\n  }\n\n  async getOrCreateThreadForRide(rideId: string): Promise<MessageThread> {\n    try {\n      // Check if thread exists\n      const { data: existingThreads, error: fetchError } = await this.supabase\n        .from('message_threads')\n        .select('*')\n        .eq('ride_id', rideId)\n        .limit(1);\n\n      if (fetchError) throw fetchError;\n\n      // If thread exists, return it\n      if (existingThreads && existingThreads.length > 0) {\n        return existingThreads[0];\n      }\n\n      // Create new thread\n      const { data: newThread, error: createError } = await this.supabase\n        .from('message_threads')\n        .insert([{ ride_id: rideId }])\n        .select()\n        .single();\n\n      if (createError) throw createError;\n      return newThread;\n    } catch (error) {\n      console.error('Error getting or creating thread:', error);\n      throw error;\n    }\n  }\n\n  async getThreadMessages(threadId: string): Promise<Message[]> {\n    try {\n      const { data, error } = await this.supabase\n        .from('messages')\n        .select('*')\n        .eq('thread_id', threadId)\n        .order('created_at', { ascending: true });\n\n      if (error) throw error;\n\n      this.messagesSubject.next(data || []);\n      return data || [];\n    } catch (error) {\n      console.error('Error fetching thread messages:', error);\n      return [];\n    }\n  }\n\n  async getUserThreads(): Promise<MessageThread[]> {\n    try {\n      const user = await this.authService.getCurrentUser();\n      if (!user) throw new Error('User not authenticated');\n\n      // Get all threads where the user is either the rider or driver\n      const { data, error } = await this.supabase\n        .from('message_threads')\n        .select(`\n          *,\n          rides!inner(rider_id, driver_id)\n        `)\n        .or(`rides.rider_id.eq.${user.id},rides.driver_id.eq.${user.id}`)\n        .order('updated_at', { ascending: false });\n\n      if (error) throw error;\n\n      this.threadsSubject.next(data || []);\n      return data || [];\n    } catch (error) {\n      console.error('Error fetching user threads:', error);\n      return [];\n    }\n  }\n\n  async sendMessage(threadId: string, receiverId: string, content: string): Promise<Message> {\n    try {\n      const currentUser = await this.authService.getCurrentUser();\n      if (!currentUser) throw new Error('User not authenticated');\n\n      const { data, error } = await this.supabase\n        .from('messages')\n        .insert([{\n          thread_id: threadId,\n          sender_id: currentUser.id,\n          receiver_id: receiverId,\n          content: content,\n          is_read: false\n        }])\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Update local messages state\n      const currentMessages = this.messagesSubject.value;\n      this.messagesSubject.next([...currentMessages, data]);\n\n      return data;\n    } catch (error) {\n      console.error('Error sending message:', error);\n      throw error;\n    }\n  }\n\n  async markMessagesAsRead(threadId: string): Promise<void> {\n    try {\n      const currentUser = await this.authService.getCurrentUser();\n      if (!currentUser) throw new Error('User not authenticated');\n\n      const { error } = await this.supabase\n        .from('messages')\n        .update({ is_read: true })\n        .eq('thread_id', threadId)\n        .eq('receiver_id', currentUser.id)\n        .eq('is_read', false);\n\n      if (error) throw error;\n\n      // Update local messages state\n      await this.getThreadMessages(threadId);\n    } catch (error) {\n      console.error('Error marking messages as read:', error);\n    }\n  }\n\n  async getUnreadMessageCount(): Promise<number> {\n    try {\n      const currentUser = await this.authService.getCurrentUser();\n      if (!currentUser) return 0;\n\n      const { data, error } = await this.supabase\n        .from('messages')\n        .select('id', { count: 'exact' })\n        .eq('receiver_id', currentUser.id)\n        .eq('is_read', false);\n\n      if (error) throw error;\n\n      return data?.length || 0;\n    } catch (error) {\n      console.error('Error getting unread message count:', error);\n      return 0;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAgCM,IAAO,iBAAP,MAAO,gBAAc;EAOL;EANZ;EACA,kBAAkB,IAAI,gBAA2B,CAAA,CAAE;EAC3D,YAAY,KAAK,gBAAgB,aAAY;EACrC,iBAAiB,IAAI,gBAAiC,CAAA,CAAE;EAChE,WAAW,KAAK,eAAe,aAAY;EAE3C,YAAoB,aAAwB;AAAxB,SAAA,cAAA;AAClB,SAAK,WAAW,YAAY;EAC9B;EAEM,yBAAyB,QAAc;;AAC3C,UAAI;AAEF,cAAM,EAAE,MAAM,iBAAiB,OAAO,WAAU,IAAK,MAAM,KAAK,SAC7D,KAAK,iBAAiB,EACtB,OAAO,GAAG,EACV,GAAG,WAAW,MAAM,EACpB,MAAM,CAAC;AAEV,YAAI;AAAY,gBAAM;AAGtB,YAAI,mBAAmB,gBAAgB,SAAS,GAAG;AACjD,iBAAO,gBAAgB,CAAC;QAC1B;AAGA,cAAM,EAAE,MAAM,WAAW,OAAO,YAAW,IAAK,MAAM,KAAK,SACxD,KAAK,iBAAiB,EACtB,OAAO,CAAC,EAAE,SAAS,OAAM,CAAE,CAAC,EAC5B,OAAM,EACN,OAAM;AAET,YAAI;AAAa,gBAAM;AACvB,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,qCAAqC,KAAK;AACxD,cAAM;MACR;IACF;;EAEM,kBAAkB,UAAgB;;AACtC,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,GAAG,EACV,GAAG,aAAa,QAAQ,EACxB,MAAM,cAAc,EAAE,WAAW,KAAI,CAAE;AAE1C,YAAI;AAAO,gBAAM;AAEjB,aAAK,gBAAgB,KAAK,QAAQ,CAAA,CAAE;AACpC,eAAO,QAAQ,CAAA;MACjB,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,eAAO,CAAA;MACT;IACF;;EAEM,iBAAc;;AAClB,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,YAAY,eAAc;AAClD,YAAI,CAAC;AAAM,gBAAM,IAAI,MAAM,wBAAwB;AAGnD,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,iBAAiB,EACtB,OAAO;;;SAGP,EACA,GAAG,qBAAqB,KAAK,EAAE,uBAAuB,KAAK,EAAE,EAAE,EAC/D,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI;AAAO,gBAAM;AAEjB,aAAK,eAAe,KAAK,QAAQ,CAAA,CAAE;AACnC,eAAO,QAAQ,CAAA;MACjB,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,KAAK;AACnD,eAAO,CAAA;MACT;IACF;;EAEM,YAAY,UAAkB,YAAoB,SAAe;;AACrE,UAAI;AACF,cAAM,cAAc,MAAM,KAAK,YAAY,eAAc;AACzD,YAAI,CAAC;AAAa,gBAAM,IAAI,MAAM,wBAAwB;AAE1D,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,CAAC;UACP,WAAW;UACX,WAAW,YAAY;UACvB,aAAa;UACb;UACA,SAAS;SACV,CAAC,EACD,OAAM,EACN,OAAM;AAET,YAAI;AAAO,gBAAM;AAGjB,cAAM,kBAAkB,KAAK,gBAAgB;AAC7C,aAAK,gBAAgB,KAAK,CAAC,GAAG,iBAAiB,IAAI,CAAC;AAEpD,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,0BAA0B,KAAK;AAC7C,cAAM;MACR;IACF;;EAEM,mBAAmB,UAAgB;;AACvC,UAAI;AACF,cAAM,cAAc,MAAM,KAAK,YAAY,eAAc;AACzD,YAAI,CAAC;AAAa,gBAAM,IAAI,MAAM,wBAAwB;AAE1D,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,SAC1B,KAAK,UAAU,EACf,OAAO,EAAE,SAAS,KAAI,CAAE,EACxB,GAAG,aAAa,QAAQ,EACxB,GAAG,eAAe,YAAY,EAAE,EAChC,GAAG,WAAW,KAAK;AAEtB,YAAI;AAAO,gBAAM;AAGjB,cAAM,KAAK,kBAAkB,QAAQ;MACvC,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;MACxD;IACF;;EAEM,wBAAqB;;AACzB,UAAI;AACF,cAAM,cAAc,MAAM,KAAK,YAAY,eAAc;AACzD,YAAI,CAAC;AAAa,iBAAO;AAEzB,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,MAAM,EAAE,OAAO,QAAO,CAAE,EAC/B,GAAG,eAAe,YAAY,EAAE,EAChC,GAAG,WAAW,KAAK;AAEtB,YAAI;AAAO,gBAAM;AAEjB,eAAO,MAAM,UAAU;MACzB,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAuC,KAAK;AAC1D,eAAO;MACT;IACF;;;qCA1JW,iBAAc,mBAAA,WAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}