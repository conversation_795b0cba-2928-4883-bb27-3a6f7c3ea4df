import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatListModule
} from "./chunk-XLQ6RWBY.js";
import {
  MatBadgeModule
} from "./chunk-22KA3K3Q.js";
import {
  MatDividerModule,
  MatProgressSpinner,
  MatProgressSpinnerModule,
  RideChatComponent,
  RideService
} from "./chunk-PSNUENZ6.js";
import {
  MessageService
} from "./chunk-XQHBKW3P.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-R24BZZME.js";
import "./chunk-NVGZCMKL.js";
import "./chunk-5DER6JXC.js";
import "./chunk-QIPXWAWB.js";
import {
  ActivatedRoute,
  RouterModule
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  FormsModule
} from "./chunk-QNBL54OW.js";
import {
  CommonModule,
  Component,
  MatButtonModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ard<PERSON>od<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgI<PERSON>,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import "./chunk-S35DAJRX.js";

// src/app/features/dashboard/shared/messages/messages.component.ts
function MessagesComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7);
    \u0275\u0275element(1, "mat-spinner", 8);
    \u0275\u0275elementEnd();
  }
}
function MessagesComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 9);
    \u0275\u0275text(1, " No message threads found. ");
    \u0275\u0275elementEnd();
  }
}
function MessagesComponent_mat_list_8_mat_list_item_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-list-item", 11);
    \u0275\u0275listener("click", function MessagesComponent_mat_list_8_mat_list_item_1_Template_mat_list_item_click_0_listener() {
      const thread_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.selectThread(thread_r2));
    });
    \u0275\u0275elementStart(1, "div", 12)(2, "mat-icon", 13);
    \u0275\u0275text(3, "forum");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 14)(5, "div", 15);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 16);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const thread_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("active-thread", ctx_r2.selectedThreadId === thread_r2.id);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate2(" ", thread_r2.rides.pickup_location, " to ", thread_r2.rides.dropoff_location, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r2.formatDate(thread_r2.updated_at), " ");
  }
}
function MessagesComponent_mat_list_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-list");
    \u0275\u0275template(1, MessagesComponent_mat_list_8_mat_list_item_1_Template, 9, 5, "mat-list-item", 10);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r2.threads);
  }
}
function MessagesComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275element(1, "app-ride-chat", 18);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("threadId", ctx_r2.selectedThreadId)("rideId", ctx_r2.selectedRideId);
  }
}
function MessagesComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "mat-icon", 20);
    \u0275\u0275text(2, "chat");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Select a conversation to start messaging");
    \u0275\u0275elementEnd()();
  }
}
var MessagesComponent = class _MessagesComponent {
  messageService;
  authService;
  rideService;
  route;
  threads = [];
  selectedThreadId = null;
  selectedRideId = null;
  loading = false;
  routeSubscription = null;
  threadsSubscription = null;
  constructor(messageService, authService, rideService, route) {
    this.messageService = messageService;
    this.authService = authService;
    this.rideService = rideService;
    this.route = route;
  }
  ngOnInit() {
    this.loading = true;
    this.routeSubscription = this.route.paramMap.subscribe((params) => {
      const threadId = params.get("threadId");
      if (threadId) {
        this.selectedThreadId = threadId;
      }
      this.setupThreadsSubscription();
    });
  }
  ngOnDestroy() {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.threadsSubscription) {
      this.threadsSubscription.unsubscribe();
    }
  }
  setupThreadsSubscription() {
    this.loading = true;
    if (this.threadsSubscription) {
      this.threadsSubscription.unsubscribe();
    }
    this.threadsSubscription = this.messageService.threads$.subscribe((threads) => {
      this.threads = threads;
      if (this.selectedThreadId) {
        const selectedThread = this.threads.find((t) => t.id === this.selectedThreadId);
        if (selectedThread) {
          this.selectedRideId = selectedThread.ride_id;
        } else {
          this.selectedThreadId = null;
          this.selectedRideId = null;
        }
      }
      this.loading = false;
    }, (error) => {
      console.error("Error loading threads:", error);
      this.loading = false;
    });
  }
  selectThread(thread) {
    this.selectedThreadId = thread.id;
    this.selectedRideId = thread.ride_id;
  }
  formatDate(dateString) {
    const date = new Date(dateString);
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1e3 * 60 * 60 * 24));
    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    } else if (diffDays === 1) {
      return "Yesterday";
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: "long" });
    } else {
      return date.toLocaleDateString();
    }
  }
  static \u0275fac = function MessagesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MessagesComponent)(\u0275\u0275directiveInject(MessageService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(RideService), \u0275\u0275directiveInject(ActivatedRoute));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MessagesComponent, selectors: [["app-messages"]], features: [\u0275\u0275ProvidersFeature([ActivatedRoute])], decls: 11, vars: 5, consts: [[1, "messages-container"], [1, "threads-card"], ["class", "loading-container", 4, "ngIf"], ["class", "no-threads", 4, "ngIf"], [4, "ngIf"], ["class", "chat-container", 4, "ngIf"], ["class", "empty-chat", 4, "ngIf"], [1, "loading-container"], ["diameter", "40"], [1, "no-threads"], [3, "active-thread", "click", 4, "ngFor", "ngForOf"], [3, "click"], [1, "thread-item"], [1, "thread-icon"], [1, "thread-details"], [1, "thread-title"], [1, "thread-date"], [1, "chat-container"], [3, "threadId", "rideId"], [1, "empty-chat"], [1, "empty-icon"]], template: function MessagesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Messages");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content");
      \u0275\u0275template(6, MessagesComponent_div_6_Template, 2, 0, "div", 2)(7, MessagesComponent_div_7_Template, 2, 0, "div", 3)(8, MessagesComponent_mat_list_8_Template, 2, 1, "mat-list", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(9, MessagesComponent_div_9_Template, 2, 2, "div", 5)(10, MessagesComponent_div_10_Template, 5, 0, "div", 6);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.threads.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.threads.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedThreadId && ctx.selectedRideId);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.selectedThreadId && !ctx.loading);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatListModule,
    MatList,
    MatListItem,
    MatDividerModule,
    MatIconModule,
    MatIcon,
    MatBadgeModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    RideChatComponent
  ], styles: ["\n\n.messages-container[_ngcontent-%COMP%] {\n  display: flex;\n  height: calc(100vh - 150px);\n  padding: 20px;\n  gap: 20px;\n}\n.threads-card[_ngcontent-%COMP%] {\n  width: 300px;\n  overflow-y: auto;\n}\n.chat-container[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  padding: 20px;\n}\n.no-threads[_ngcontent-%COMP%] {\n  padding: 20px;\n  text-align: center;\n  color: rgba(0, 0, 0, 0.5);\n}\n.thread-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 8px 0;\n}\n.thread-icon[_ngcontent-%COMP%] {\n  margin-right: 16px;\n  color: #3f51b5;\n}\n.thread-details[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.thread-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.thread-date[_ngcontent-%COMP%] {\n  font-size: 0.8em;\n  color: rgba(0, 0, 0, 0.5);\n}\n.active-thread[_ngcontent-%COMP%] {\n  background-color: rgba(63, 81, 181, 0.1);\n}\n.empty-chat[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  color: rgba(0, 0, 0, 0.5);\n}\n.empty-icon[_ngcontent-%COMP%] {\n  font-size: 64px;\n  height: 64px;\n  width: 64px;\n  margin-bottom: 16px;\n}\n@media (max-width: 768px) {\n  .messages-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .threads-card[_ngcontent-%COMP%] {\n    width: 100%;\n    max-height: 200px;\n  }\n}\n/*# sourceMappingURL=messages.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MessagesComponent, [{
    type: Component,
    args: [{ selector: "app-messages", standalone: true, imports: [
      CommonModule,
      FormsModule,
      RouterModule,
      MatCardModule,
      MatListModule,
      MatDividerModule,
      MatIconModule,
      MatBadgeModule,
      MatButtonModule,
      MatProgressSpinnerModule,
      RideChatComponent
    ], providers: [ActivatedRoute], template: `
    <div class="messages-container">
      <mat-card class="threads-card">
        <mat-card-header>
          <mat-card-title>Messages</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
          </div>
          
          <div *ngIf="!loading && threads.length === 0" class="no-threads">
            No message threads found.
          </div>
          
          <mat-list *ngIf="!loading && threads.length > 0">
            <mat-list-item *ngFor="let thread of threads" 
                          [class.active-thread]="selectedThreadId === thread.id"
                          (click)="selectThread(thread)">
              <div class="thread-item">
                <mat-icon class="thread-icon">forum</mat-icon>
                <div class="thread-details">
                  <div class="thread-title">
                    {{ thread.rides.pickup_location }} to {{ thread.rides.dropoff_location }}
                  </div>
                  <div class="thread-date">
                    {{ formatDate(thread.updated_at) }}
                  </div>
                </div>
              </div>
            </mat-list-item>
          </mat-list>
        </mat-card-content>
      </mat-card>
      
      <div class="chat-container" *ngIf="selectedThreadId && selectedRideId">
        <app-ride-chat [threadId]="selectedThreadId" [rideId]="selectedRideId"></app-ride-chat>
      </div>
      
      <div class="empty-chat" *ngIf="!selectedThreadId && !loading">
        <mat-icon class="empty-icon">chat</mat-icon>
        <p>Select a conversation to start messaging</p>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;d2a413985852f3639be54a04fd2f5ae0ac043460552e9326220c5af56d872987;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/shared/messages/messages.component.ts */\n.messages-container {\n  display: flex;\n  height: calc(100vh - 150px);\n  padding: 20px;\n  gap: 20px;\n}\n.threads-card {\n  width: 300px;\n  overflow-y: auto;\n}\n.chat-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  padding: 20px;\n}\n.no-threads {\n  padding: 20px;\n  text-align: center;\n  color: rgba(0, 0, 0, 0.5);\n}\n.thread-item {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 8px 0;\n}\n.thread-icon {\n  margin-right: 16px;\n  color: #3f51b5;\n}\n.thread-details {\n  flex: 1;\n}\n.thread-title {\n  font-weight: 500;\n}\n.thread-date {\n  font-size: 0.8em;\n  color: rgba(0, 0, 0, 0.5);\n}\n.active-thread {\n  background-color: rgba(63, 81, 181, 0.1);\n}\n.empty-chat {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  color: rgba(0, 0, 0, 0.5);\n}\n.empty-icon {\n  font-size: 64px;\n  height: 64px;\n  width: 64px;\n  margin-bottom: 16px;\n}\n@media (max-width: 768px) {\n  .messages-container {\n    flex-direction: column;\n  }\n  .threads-card {\n    width: 100%;\n    max-height: 200px;\n  }\n}\n/*# sourceMappingURL=messages.component.css.map */\n"] }]
  }], () => [{ type: MessageService }, { type: AuthService }, { type: RideService }, { type: ActivatedRoute }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MessagesComponent, { className: "MessagesComponent", filePath: "src/app/features/dashboard/shared/messages/messages.component.ts", lineNumber: 182 });
})();
export {
  MessagesComponent
};
//# sourceMappingURL=chunk-HLRKE4M5.js.map
