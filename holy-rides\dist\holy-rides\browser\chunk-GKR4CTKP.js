import{a as G,b as J,c as K}from"./chunk-TUEROBIF.js";import{b as q}from"./chunk-EMQER2I7.js";import{a as z,b as B,c as $,e as H,f as Y}from"./chunk-4XM5VEPX.js";import{a as U}from"./chunk-EACHO2FA.js";import{a as R,b as k}from"./chunk-WSXVBUWR.js";import"./chunk-3NZGXQSR.js";import"./chunk-YTNZ52NK.js";import"./chunk-Q34CP4BD.js";import{a as f,f as w}from"./chunk-3EEDYH74.js";import{J as F,t as E}from"./chunk-AG3SD6JT.js";import{Fc as b,Jd as D,Kb as d,La as o,Ld as j,Mb as S,Md as A,Nb as y,Nd as L,Pa as l,Qd as N,Rd as V,Sb as T,Wa as M,ab as g,hb as s,jb as x,la as _,ma as v,qb as r,rb as a,sb as h,uc as O,vc as P,wb as I,yb as C,zb as m}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{i as u}from"./chunk-ODN5LVDJ.js";function W(i,t){i&1&&(r(0,"div",7),h(1,"mat-spinner",8),a())}function X(i,t){i&1&&(r(0,"div",9),d(1," No message threads found. "),a())}function Z(i,t){if(i&1){let e=I();r(0,"mat-list-item",11),C("click",function(){let p=_(e).$implicit,c=m(2);return v(c.selectThread(p))}),r(1,"div",12)(2,"mat-icon",13),d(3,"forum"),a(),r(4,"div",14)(5,"div",15),d(6),a(),r(7,"div",16),d(8),a()()()()}if(i&2){let e=t.$implicit,n=m(2);x("active-thread",n.selectedThreadId===e.id),o(6),y(" ",e.rides.pickup_location," to ",e.rides.dropoff_location," "),o(2),S(" ",n.formatDate(e.updated_at)," ")}}function ee(i,t){if(i&1&&(r(0,"mat-list"),g(1,Z,9,5,"mat-list-item",10),a()),i&2){let e=m();o(),s("ngForOf",e.threads)}}function te(i,t){if(i&1&&(r(0,"div",17),h(1,"app-ride-chat",18),a()),i&2){let e=m();o(),s("threadId",e.selectedThreadId)("rideId",e.selectedRideId)}}function ie(i,t){i&1&&(r(0,"div",19)(1,"mat-icon",20),d(2,"chat"),a(),r(3,"p"),d(4,"Select a conversation to start messaging"),a()())}var Q=class i{constructor(t,e,n,p){this.messageService=t;this.authService=e;this.rideService=n;this.route=p}threads=[];selectedThreadId=null;selectedRideId=null;loading=!1;routeSubscription=null;ngOnInit(){this.loading=!0,this.routeSubscription=this.route.paramMap.subscribe(t=>{let e=t.get("threadId");e&&(this.selectedThreadId=e),this.loadThreads()})}ngOnDestroy(){this.routeSubscription&&this.routeSubscription.unsubscribe()}loadThreads(){return u(this,null,function*(){try{if(this.threads=yield this.messageService.getUserThreads(),this.selectedThreadId){let t=this.threads.find(e=>e.id===this.selectedThreadId);t&&(this.selectedRideId=t.ride_id)}}catch(t){console.error("Error loading threads:",t)}finally{this.loading=!1}})}selectThread(t){this.selectedThreadId=t.id,this.selectedRideId=t.ride_id}formatDate(t){let e=new Date(t),p=new Date().getTime()-e.getTime(),c=Math.floor(p/(1e3*60*60*24));return c===0?e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):c===1?"Yesterday":c<7?e.toLocaleDateString([],{weekday:"long"}):e.toLocaleDateString()}static \u0275fac=function(e){return new(e||i)(l(U),l(F),l(z),l(f))};static \u0275cmp=M({type:i,selectors:[["app-messages"]],features:[T([f])],decls:11,vars:5,consts:[[1,"messages-container"],[1,"threads-card"],["class","loading-container",4,"ngIf"],["class","no-threads",4,"ngIf"],[4,"ngIf"],["class","chat-container",4,"ngIf"],["class","empty-chat",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"no-threads"],[3,"active-thread","click",4,"ngFor","ngForOf"],[3,"click"],[1,"thread-item"],[1,"thread-icon"],[1,"thread-details"],[1,"thread-title"],[1,"thread-date"],[1,"chat-container"],[3,"threadId","rideId"],[1,"empty-chat"],[1,"empty-icon"]],template:function(e,n){e&1&&(r(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),d(4,"Messages"),a()(),r(5,"mat-card-content"),g(6,W,2,0,"div",2)(7,X,2,0,"div",3)(8,ee,2,1,"mat-list",4),a()(),g(9,te,2,2,"div",5)(10,ie,5,0,"div",6),a()),e&2&&(o(6),s("ngIf",n.loading),o(),s("ngIf",!n.loading&&n.threads.length===0),o(),s("ngIf",!n.loading&&n.threads.length>0),o(),s("ngIf",n.selectedThreadId&&n.selectedRideId),o(),s("ngIf",!n.selectedThreadId&&!n.loading))},dependencies:[b,O,P,E,w,V,j,L,N,A,K,G,J,H,k,R,q,D,$,B,Y],styles:[".messages-container[_ngcontent-%COMP%]{display:flex;height:calc(100vh - 150px);padding:20px;gap:20px}.threads-card[_ngcontent-%COMP%]{width:300px;overflow-y:auto}.chat-container[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column}.loading-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px}.no-threads[_ngcontent-%COMP%]{padding:20px;text-align:center;color:#00000080}.thread-item[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;padding:8px 0}.thread-icon[_ngcontent-%COMP%]{margin-right:16px;color:#3f51b5}.thread-details[_ngcontent-%COMP%]{flex:1}.thread-title[_ngcontent-%COMP%]{font-weight:500}.thread-date[_ngcontent-%COMP%]{font-size:.8em;color:#00000080}.active-thread[_ngcontent-%COMP%]{background-color:#3f51b51a}.empty-chat[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;color:#00000080}.empty-icon[_ngcontent-%COMP%]{font-size:64px;height:64px;width:64px;margin-bottom:16px}@media (max-width: 768px){.messages-container[_ngcontent-%COMP%]{flex-direction:column}.threads-card[_ngcontent-%COMP%]{width:100%;max-height:200px}}"]})};export{Q as MessagesComponent};
