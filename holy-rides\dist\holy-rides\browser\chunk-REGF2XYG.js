import {
  MatSnackBar
} from "./chunk-MSLYEYGK.js";
import "./chunk-LTJSKJGW.js";
import "./chunk-QIPXWAWB.js";
import {
  Router
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  DefaultV<PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MatError,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  MatLabel,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  CommonModule,
  Component,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import {
  __async
} from "./chunk-S35DAJRX.js";

// src/app/features/auth/profile/profile.component.ts
function ProfileComponent_mat_error_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Full name is required");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_mat_error_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Phone number is required");
    \u0275\u0275elementEnd();
  }
}
var ProfileComponent = class _ProfileComponent {
  formBuilder;
  authService;
  snackBar;
  router;
  profileForm;
  loading = false;
  userId = null;
  constructor(formBuilder, authService, snackBar, router) {
    this.formBuilder = formBuilder;
    this.authService = authService;
    this.snackBar = snackBar;
    this.router = router;
    this.profileForm = this.formBuilder.group({
      full_name: ["", Validators.required],
      phone: ["", Validators.required],
      email: [{ value: "", disabled: true }]
    });
  }
  ngOnInit() {
    return __async(this, null, function* () {
      const user = yield this.authService.getCurrentUser();
      if (user) {
        this.userId = user.id;
        this.profileForm.patchValue({
          email: user.email,
          full_name: user.full_name || "",
          phone: user.phone || ""
        });
      }
    });
  }
  onSubmit() {
    return __async(this, null, function* () {
      if (this.profileForm.invalid || !this.userId) {
        return;
      }
      this.loading = true;
      try {
        const success = yield this.authService.updateProfile(this.profileForm.getRawValue());
        if (!success) {
          throw new Error("Failed to update profile");
        }
        this.snackBar.open("Profile updated successfully", "Close", {
          duration: 3e3
        });
        const role = yield this.authService.getUserRole();
        if (role) {
          yield this.router.navigate([this.authService.getDashboardRouteForRole(role)]);
        } else {
          throw new Error("User role not found");
        }
      } catch (error) {
        this.snackBar.open(error.message || "An error occurred", "Close", {
          duration: 3e3
        });
      } finally {
        this.loading = false;
      }
    });
  }
  static \u0275fac = function ProfileComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProfileComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(MatSnackBar), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProfileComponent, selectors: [["app-profile"]], decls: 24, vars: 5, consts: [[1, "profile-container"], [3, "ngSubmit", "formGroup"], ["appearance", "outline"], ["matInput", "", "formControlName", "email", "readonly", ""], ["matInput", "", "formControlName", "full_name", "placeholder", "Enter your full name"], [4, "ngIf"], ["matInput", "", "formControlName", "phone", "placeholder", "Enter your phone number"], [1, "button-container"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"]], template: function ProfileComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Profile");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "form", 1);
      \u0275\u0275listener("ngSubmit", function ProfileComponent_Template_form_ngSubmit_6_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(7, "mat-form-field", 2)(8, "mat-label");
      \u0275\u0275text(9, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(10, "input", 3);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "mat-form-field", 2)(12, "mat-label");
      \u0275\u0275text(13, "Full Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(14, "input", 4);
      \u0275\u0275template(15, ProfileComponent_mat_error_15_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "mat-form-field", 2)(17, "mat-label");
      \u0275\u0275text(18, "Phone Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(19, "input", 6);
      \u0275\u0275template(20, ProfileComponent_mat_error_20_Template, 2, 0, "mat-error", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "div", 7)(22, "button", 8);
      \u0275\u0275text(23);
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_2_0;
      \u0275\u0275advance(6);
      \u0275\u0275property("formGroup", ctx.profileForm);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.profileForm.get("full_name")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors["required"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_2_0 = ctx.profileForm.get("phone")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors["required"]);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.profileForm.invalid || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.loading ? "Saving..." : "Save Changes", " ");
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, MatFormFieldModule, MatFormField, MatLabel, MatError, MatInputModule, MatInput, MatButtonModule, MatButton, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle], styles: ["\n\n.profile-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: flex-start;\n  min-height: 100vh;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n.profile-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.profile-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.profile-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.profile-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 8px;\n}\n/*# sourceMappingURL=profile.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfileComponent, [{
    type: Component,
    args: [{ selector: "app-profile", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatCardModule
    ], template: `<div class="profile-container">\r
  <mat-card>\r
    <mat-card-header>\r
      <mat-card-title>Profile</mat-card-title>\r
    </mat-card-header>\r
    <mat-card-content>\r
      <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">\r
        <mat-form-field appearance="outline">\r
          <mat-label>Email</mat-label>\r
          <input matInput formControlName="email" readonly>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Full Name</mat-label>\r
          <input matInput formControlName="full_name" placeholder="Enter your full name">\r
          <mat-error *ngIf="profileForm.get('full_name')?.errors?.['required']">Full name is required</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Phone Number</mat-label>\r
          <input matInput formControlName="phone" placeholder="Enter your phone number">\r
          <mat-error *ngIf="profileForm.get('phone')?.errors?.['required']">Phone number is required</mat-error>\r
        </mat-form-field>\r
\r
        <div class="button-container">\r
          <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || loading">\r
            {{ loading ? 'Saving...' : 'Save Changes' }}\r
          </button>\r
        </div>\r
      </form>\r
    </mat-card-content>\r
  </mat-card>\r
</div>\r
`, styles: ["/* src/app/features/auth/profile/profile.component.scss */\n.profile-container {\n  display: flex;\n  justify-content: center;\n  align-items: flex-start;\n  min-height: 100vh;\n  padding: 20px;\n  background-color: #f5f5f5;\n}\n.profile-container mat-card {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.profile-container mat-form-field {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.profile-container .button-container {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.profile-container .button-container button {\n  width: 100%;\n  padding: 8px;\n}\n/*# sourceMappingURL=profile.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: MatSnackBar }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProfileComponent, { className: "ProfileComponent", filePath: "src/app/features/auth/profile/profile.component.ts", lineNumber: 26 });
})();
export {
  ProfileComponent
};
//# sourceMappingURL=chunk-REGF2XYG.js.map
