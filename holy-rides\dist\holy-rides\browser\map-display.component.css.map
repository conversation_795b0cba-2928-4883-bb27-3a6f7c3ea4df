{"version": 3, "sources": ["src/app/shared/components/map-display/map-display.component.ts"], "sourcesContent": ["\n    .map-card {\n      margin-bottom: 16px;\n    }\n\n    .map-container {\n      height: 300px;\n      width: 100%;\n    }\n\n    .map-placeholder {\n      height: 300px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background-color: #f5f5f5;\n      color: #666;\n    }\n\n    .map-actions {\n      margin-top: 16px;\n      display: flex;\n      justify-content: center;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,UAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,mBAAA;;", "names": []}