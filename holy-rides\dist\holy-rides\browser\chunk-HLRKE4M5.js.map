{"version": 3, "sources": ["src/app/features/dashboard/shared/messages/messages.component.ts"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MessageService } from '../../../../core/services/message.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { RideChatComponent } from '../../../../shared/components/ride-chat/ride-chat.component';\nimport { Subscription } from 'rxjs';\n\ninterface MessageThread {\n  id: string;\n  ride_id: string;\n  created_at: string;\n  updated_at: string;\n  rides: {\n    rider_id: string;\n    driver_id: string;\n    pickup_location: string;\n    dropoff_location: string;\n  };\n}\n\n@Component({\n  selector: 'app-messages',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    RouterModule,\n    MatCardModule,\n    MatListModule,\n    MatDividerModule,\n    MatIconModule,\n    MatBadgeModule,\n    MatButtonModule,\n    MatProgressSpinnerModule,\n    RideChatComponent\n  ],\n  providers: [ActivatedRoute],\n  template: `\n    <div class=\"messages-container\">\n      <mat-card class=\"threads-card\">\n        <mat-card-header>\n          <mat-card-title>Messages</mat-card-title>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <div *ngIf=\"loading\" class=\"loading-container\">\n            <mat-spinner diameter=\"40\"></mat-spinner>\n          </div>\n          \n          <div *ngIf=\"!loading && threads.length === 0\" class=\"no-threads\">\n            No message threads found.\n          </div>\n          \n          <mat-list *ngIf=\"!loading && threads.length > 0\">\n            <mat-list-item *ngFor=\"let thread of threads\" \n                          [class.active-thread]=\"selectedThreadId === thread.id\"\n                          (click)=\"selectThread(thread)\">\n              <div class=\"thread-item\">\n                <mat-icon class=\"thread-icon\">forum</mat-icon>\n                <div class=\"thread-details\">\n                  <div class=\"thread-title\">\n                    {{ thread.rides.pickup_location }} to {{ thread.rides.dropoff_location }}\n                  </div>\n                  <div class=\"thread-date\">\n                    {{ formatDate(thread.updated_at) }}\n                  </div>\n                </div>\n              </div>\n            </mat-list-item>\n          </mat-list>\n        </mat-card-content>\n      </mat-card>\n      \n      <div class=\"chat-container\" *ngIf=\"selectedThreadId && selectedRideId\">\n        <app-ride-chat [threadId]=\"selectedThreadId\" [rideId]=\"selectedRideId\"></app-ride-chat>\n      </div>\n      \n      <div class=\"empty-chat\" *ngIf=\"!selectedThreadId && !loading\">\n        <mat-icon class=\"empty-icon\">chat</mat-icon>\n        <p>Select a conversation to start messaging</p>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .messages-container {\n      display: flex;\n      height: calc(100vh - 150px);\n      padding: 20px;\n      gap: 20px;\n    }\n    \n    .threads-card {\n      width: 300px;\n      overflow-y: auto;\n    }\n    \n    .chat-container {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 20px;\n    }\n    \n    .no-threads {\n      padding: 20px;\n      text-align: center;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .thread-item {\n      display: flex;\n      align-items: center;\n      width: 100%;\n      padding: 8px 0;\n    }\n    \n    .thread-icon {\n      margin-right: 16px;\n      color: #3f51b5;\n    }\n    \n    .thread-details {\n      flex: 1;\n    }\n    \n    .thread-title {\n      font-weight: 500;\n    }\n    \n    .thread-date {\n      font-size: 0.8em;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .active-thread {\n      background-color: rgba(63, 81, 181, 0.1);\n    }\n    \n    .empty-chat {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .empty-icon {\n      font-size: 64px;\n      height: 64px;\n      width: 64px;\n      margin-bottom: 16px;\n    }\n    \n    @media (max-width: 768px) {\n      .messages-container {\n        flex-direction: column;\n      }\n      \n      .threads-card {\n        width: 100%;\n        max-height: 200px;\n      }\n    }\n  `]\n})\nexport class MessagesComponent implements OnInit, OnDestroy {\n  threads: MessageThread[] = [];\n  selectedThreadId: string | null = null;\n  selectedRideId: string | null = null;\n  loading = false;\n  private routeSubscription: Subscription | null = null;\n  private threadsSubscription: Subscription | null = null;\n  \n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private rideService: RideService,\n    private route: ActivatedRoute\n  ) {}\n  \n  ngOnInit() {\n    this.loading = true;\n    \n    // Check if a thread ID was provided in the route\n    this.routeSubscription = this.route.paramMap.subscribe((params: { get: (arg0: string) => any; }) => {\n      const threadId = params.get('threadId');\n      if (threadId) {\n        this.selectedThreadId = threadId;\n      }\n      \n      this.setupThreadsSubscription();\n    });\n  }\n  \n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.threadsSubscription) {\n      this.threadsSubscription.unsubscribe();\n    }\n  }\n  \n  setupThreadsSubscription() {\n    this.loading = true;\n    if (this.threadsSubscription) {\n        this.threadsSubscription.unsubscribe();\n    }\n    // Assuming messageService has a threads$ observable\n    this.threadsSubscription = this.messageService.threads$.subscribe(threads => {\n        this.threads = threads;\n        if (this.selectedThreadId) {\n            const selectedThread = this.threads.find(t => t.id === this.selectedThreadId);\n            if (selectedThread) {\n                this.selectedRideId = selectedThread.ride_id;\n            } else {\n                this.selectedThreadId = null;\n                this.selectedRideId = null;\n            }\n        }\n        this.loading = false;\n    }, (error: any) => {\n        console.error('Error loading threads:', error);\n        this.loading = false;\n    });\n  }\n  \n  selectThread(thread: MessageThread) {\n    this.selectedThreadId = thread.id;\n    this.selectedRideId = thread.ride_id;\n  }\n  \n  formatDate(dateString: string): string {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 0) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } else if (diffDays === 1) {\n      return 'Yesterday';\n    } else if (diffDays < 7) {\n      return date.toLocaleDateString([], { weekday: 'long' });\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDU,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,eAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;;AAGE,IAAA,yBAAA,GAAA,iBAAA,EAAA;AAEc,IAAA,qBAAA,SAAA,SAAA,uFAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,SAAA,CAAoB;IAAA,CAAA;AACzC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,YAAA,EAAA;AACO,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACnC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,OAAA,EAAA;AAExB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM,EACF,EACF;;;;;AAZM,IAAA,sBAAA,iBAAA,OAAA,qBAAA,UAAA,EAAA;AAMN,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,MAAA,iBAAA,QAAA,UAAA,MAAA,kBAAA,GAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,UAAA,UAAA,GAAA,GAAA;;;;;AAXV,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,iBAAA,EAAA;AAeF,IAAA,uBAAA;;;;AAfoC,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,OAAA;;;;;AAmBxC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;;;;AADiB,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,gBAAA,EAA6B,UAAA,OAAA,cAAA;;;;;AAG9C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,YAAA,EAAA;AAC/B,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,0CAAA;AAAwC,IAAA,uBAAA,EAAI;;;AA4FjD,IAAO,oBAAP,MAAO,mBAAiB;EASlB;EACA;EACA;EACA;EAXV,UAA2B,CAAA;EAC3B,mBAAkC;EAClC,iBAAgC;EAChC,UAAU;EACF,oBAAyC;EACzC,sBAA2C;EAEnD,YACU,gBACA,aACA,aACA,OAAqB;AAHrB,SAAA,iBAAA;AACA,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,QAAA;EACP;EAEH,WAAQ;AACN,SAAK,UAAU;AAGf,SAAK,oBAAoB,KAAK,MAAM,SAAS,UAAU,CAAC,WAA2C;AACjG,YAAM,WAAW,OAAO,IAAI,UAAU;AACtC,UAAI,UAAU;AACZ,aAAK,mBAAmB;MAC1B;AAEA,WAAK,yBAAwB;IAC/B,CAAC;EACH;EAEA,cAAW;AACT,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAW;IACpC;AACA,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,YAAW;IACtC;EACF;EAEA,2BAAwB;AACtB,SAAK,UAAU;AACf,QAAI,KAAK,qBAAqB;AAC1B,WAAK,oBAAoB,YAAW;IACxC;AAEA,SAAK,sBAAsB,KAAK,eAAe,SAAS,UAAU,aAAU;AACxE,WAAK,UAAU;AACf,UAAI,KAAK,kBAAkB;AACvB,cAAM,iBAAiB,KAAK,QAAQ,KAAK,OAAK,EAAE,OAAO,KAAK,gBAAgB;AAC5E,YAAI,gBAAgB;AAChB,eAAK,iBAAiB,eAAe;QACzC,OAAO;AACH,eAAK,mBAAmB;AACxB,eAAK,iBAAiB;QAC1B;MACJ;AACA,WAAK,UAAU;IACnB,GAAG,CAAC,UAAc;AACd,cAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAK,UAAU;IACnB,CAAC;EACH;EAEA,aAAa,QAAqB;AAChC,SAAK,mBAAmB,OAAO;AAC/B,SAAK,iBAAiB,OAAO;EAC/B;EAEA,WAAW,YAAkB;AAC3B,UAAM,OAAO,IAAI,KAAK,UAAU;AAChC,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,SAAS,IAAI,QAAO,IAAK,KAAK,QAAO;AAC3C,UAAM,WAAW,KAAK,MAAM,UAAU,MAAO,KAAK,KAAK,GAAG;AAE1D,QAAI,aAAa,GAAG;AAClB,aAAO,KAAK,mBAAmB,CAAA,GAAI,EAAE,MAAM,WAAW,QAAQ,UAAS,CAAE;IAC3E,WAAW,aAAa,GAAG;AACzB,aAAO;IACT,WAAW,WAAW,GAAG;AACvB,aAAO,KAAK,mBAAmB,CAAA,GAAI,EAAE,SAAS,OAAM,CAAE;IACxD,OAAO;AACL,aAAO,KAAK,mBAAkB;IAChC;EACF;;qCAlFW,oBAAiB,4BAAA,cAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,cAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,UAAA,CAAA,6BAvIjB,CAAC,cAAc,CAAC,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,QAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAEzB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,YAAA,CAAA,EACC,GAAA,iBAAA,EACZ,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA,EAAiB;AAG3C,MAAA,yBAAA,GAAA,kBAAA;AACE,MAAA,qBAAA,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA,EAIkB,GAAA,uCAAA,GAAA,GAAA,YAAA,CAAA;AAqBnE,MAAA,uBAAA,EAAmB;AAGrB,MAAA,qBAAA,GAAA,kCAAA,GAAA,GAAA,OAAA,CAAA,EAAuE,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAQzE,MAAA,uBAAA;;;AApCY,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,WAAA,CAAA;AAIK,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,SAAA,CAAA;AAoBc,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,oBAAA,IAAA,cAAA;AAIJ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,oBAAA,CAAA,IAAA,OAAA;;;IArD3B;IAAY;IAAA;IACZ;IACA;IACA;IAAa;IAAA;IAAA;IAAA;IACb;IAAa;IAAA;IACb;IACA;IAAa;IACb;IACA;IACA;IAAwB;IACxB;EAAiB,GAAA,QAAA,CAAA,qgDAAA,EAAA,CAAA;;;sEAyIR,mBAAiB,CAAA;UAvJ7B;uBACW,gBAAc,YACZ,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,WACU,CAAC,cAAc,GAAC,UACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6CT,QAAA,CAAA,u7CAAA,EAAA,CAAA;;;;6EAyFU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,oEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}