import {
  GoogleMap,
  GoogleMapsModule,
  MapDirectionsRenderer,
  MapDirectionsService,
  MapMarker
} from "./chunk-ZG6RKMCP.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-R24BZZME.js";
import {
  AsyncPipe,
  BehaviorSubject,
  CommonModule,
  Component,
  Inject,
  Injectable,
  Input,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardModule,
  NgIf,
  PLATFORM_ID,
  environment,
  isPlatformBrowser,
  map,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵinject,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵsanitizeUrl,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext
} from "./chunk-THPQGTPB.js";
import {
  __async
} from "./chunk-S35DAJRX.js";

// src/app/core/services/google-maps-loader.service.ts
var GoogleMapsLoaderService = class _GoogleMapsLoaderService {
  platformId;
  isLoaded = false;
  loadingPromise = null;
  constructor(platformId) {
    this.platformId = platformId;
  }
  /**
   * Load the Google Maps API script
   */
  loadGoogleMapsApi() {
    if (!isPlatformBrowser(this.platformId)) {
      return Promise.resolve();
    }
    if (this.loadingPromise) {
      return this.loadingPromise;
    }
    if (this.isLoaded) {
      return Promise.resolve();
    }
    this.loadingPromise = new Promise((resolve, reject) => {
      if (window.google && window.google.maps) {
        this.isLoaded = true;
        resolve();
        return;
      }
      const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1e6)}`;
      window[callbackName] = () => {
        this.isLoaded = true;
        resolve();
        delete window[callbackName];
      };
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${environment.googleMapsApiKey}&libraries=places&callback=${callbackName}`;
      script.async = true;
      script.defer = true;
      script.onerror = (error) => {
        reject(new Error(`Failed to load Google Maps API: ${error}`));
        delete window[callbackName];
      };
      document.head.appendChild(script);
    });
    return this.loadingPromise;
  }
  /**
   * Check if the Google Maps API is loaded
   */
  isGoogleMapsLoaded() {
    return this.isLoaded;
  }
  static \u0275fac = function GoogleMapsLoaderService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _GoogleMapsLoaderService)(\u0275\u0275inject(PLATFORM_ID));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _GoogleMapsLoaderService, factory: _GoogleMapsLoaderService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(GoogleMapsLoaderService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }], null);
})();

// src/app/core/services/location.service.ts
var LocationService = class _LocationService {
  googleMapsLoader;
  currentLocationSubject = new BehaviorSubject(null);
  currentLocation$ = this.currentLocationSubject.asObservable();
  constructor(googleMapsLoader) {
    this.googleMapsLoader = googleMapsLoader;
  }
  /**
   * Get the user's current location using the Geolocation API
   */
  getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by your browser"));
        return;
      }
      navigator.geolocation.getCurrentPosition((position) => {
        const coords = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        };
        this.currentLocationSubject.next(coords);
        resolve(coords);
      }, (error) => {
        reject(error);
      }, { enableHighAccuracy: true, timeout: 1e4, maximumAge: 0 });
    });
  }
  /**
   * Convert an address to coordinates (geocoding)
   * Uses the Google Maps Geocoding API
   */
  geocodeAddress(address) {
    return __async(this, null, function* () {
      try {
        yield this.googleMapsLoader.loadGoogleMapsApi();
        return new Promise((resolve, reject) => {
          if (!google || !google.maps || !google.maps.Geocoder) {
            console.warn("Google Maps Geocoder not available, using mock data");
            const latitude = 40 + (Math.random() * 10 - 5);
            const longitude = -74 + (Math.random() * 10 - 5);
            resolve({ latitude, longitude });
            return;
          }
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode({ address }, (results, status) => {
            if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
              const location = results[0].geometry.location;
              resolve({
                latitude: location.lat(),
                longitude: location.lng()
              });
            } else {
              console.warn(`Geocoding failed for address: ${address}. Status: ${status}`);
              const latitude = 40 + (Math.random() * 10 - 5);
              const longitude = -74 + (Math.random() * 10 - 5);
              resolve({ latitude, longitude });
            }
          });
        });
      } catch (error) {
        console.error("Failed to load Google Maps API:", error);
        return {
          latitude: 40 + (Math.random() * 10 - 5),
          longitude: -74 + (Math.random() * 10 - 5)
        };
      }
    });
  }
  /**
   * Calculate route between two points
   * Uses the Google Maps Directions API
   */
  calculateRoute(origin, destination) {
    return __async(this, null, function* () {
      try {
        yield this.googleMapsLoader.loadGoogleMapsApi();
        return new Promise((resolve, reject) => {
          if (!google || !google.maps || !google.maps.DirectionsService) {
            console.warn("Google Maps DirectionsService not available, using mock data");
            const distance = Math.floor(Math.random() * 18) + 2;
            const duration = Math.floor(Math.random() * 55) + 5;
            resolve({
              distance,
              duration,
              polyline: "mock_polyline_string"
            });
            return;
          }
          const directionsService = new google.maps.DirectionsService();
          const originStr = typeof origin === "string" ? origin : `${origin.latitude},${origin.longitude}`;
          const destinationStr = typeof destination === "string" ? destination : `${destination.latitude},${destination.longitude}`;
          const request = {
            origin: originStr,
            destination: destinationStr,
            travelMode: google.maps.TravelMode.DRIVING
          };
          directionsService.route(request, (result, status) => {
            if (status === google.maps.DirectionsStatus.OK && result) {
              const route = result.routes[0];
              if (route && route.legs && route.legs.length > 0) {
                const leg = route.legs[0];
                const distanceInMiles = leg.distance ? leg.distance.value / 1609.34 : 0;
                const durationInMinutes = leg.duration ? Math.ceil(leg.duration.value / 60) : 0;
                const polyline = route.overview_polyline ? route.overview_polyline : "";
                resolve({
                  distance: parseFloat(distanceInMiles.toFixed(2)),
                  duration: durationInMinutes,
                  polyline
                });
              } else {
                console.warn("No route found");
                resolve({
                  distance: Math.floor(Math.random() * 18) + 2,
                  duration: Math.floor(Math.random() * 55) + 5,
                  polyline: "mock_polyline_string"
                });
              }
            } else {
              console.warn(`Directions request failed. Status: ${status}`);
              resolve({
                distance: Math.floor(Math.random() * 18) + 2,
                duration: Math.floor(Math.random() * 55) + 5,
                polyline: "mock_polyline_string"
              });
            }
          });
        });
      } catch (error) {
        console.error("Failed to load Google Maps API:", error);
        return {
          distance: Math.floor(Math.random() * 18) + 2,
          duration: Math.floor(Math.random() * 55) + 5,
          polyline: "mock_polyline_string"
        };
      }
    });
  }
  /**
   * Generate a Google Maps URL for navigation
   */
  getGoogleMapsUrl(address) {
    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
  }
  /**
   * Generate a Google Maps Directions URL
   */
  getGoogleMapsDirectionsUrl(origin, destination) {
    return `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&travelmode=driving`;
  }
  static \u0275fac = function LocationService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LocationService)(\u0275\u0275inject(GoogleMapsLoaderService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _LocationService, factory: _LocationService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LocationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: GoogleMapsLoaderService }], null);
})();

// src/app/shared/components/map-display/map-display.component.ts
function MapDisplayComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 5)(1, "p");
    \u0275\u0275text(2, "Loading map...");
    \u0275\u0275elementEnd()();
  }
}
function MapDisplayComponent_google_map_4_map_marker_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "map-marker", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("position", ctx_r0.originMarker)("title", "Origin")("options", ctx_r0.markerOptions);
  }
}
function MapDisplayComponent_google_map_4_map_marker_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "map-marker", 9);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275property("position", ctx_r0.destinationMarker)("title", "Destination")("options", ctx_r0.markerOptions);
  }
}
function MapDisplayComponent_google_map_4_map_directions_renderer_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "map-directions-renderer", 10);
  }
  if (rf & 2) {
    const directionsResults_r2 = ctx.ngIf;
    \u0275\u0275property("directions", directionsResults_r2);
  }
}
function MapDisplayComponent_google_map_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "google-map", 6);
    \u0275\u0275template(1, MapDisplayComponent_google_map_4_map_marker_1_Template, 1, 3, "map-marker", 7)(2, MapDisplayComponent_google_map_4_map_marker_2_Template, 1, 3, "map-marker", 7)(3, MapDisplayComponent_google_map_4_map_directions_renderer_3_Template, 1, 1, "map-directions-renderer", 8);
    \u0275\u0275pipe(4, "async");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("center", ctx_r0.center)("zoom", ctx_r0.zoom)("options", ctx_r0.options);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.originMarker);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.destinationMarker);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", \u0275\u0275pipeBind1(4, 6, ctx_r0.directionsResults$));
  }
}
function MapDisplayComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11)(1, "a", 12)(2, "button", 13)(3, "mat-icon");
    \u0275\u0275text(4, "directions");
    \u0275\u0275elementEnd();
    \u0275\u0275text(5, " Get Directions ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("href", ctx_r0.directionsUrl, \u0275\u0275sanitizeUrl);
  }
}
var MapDisplayComponent = class _MapDisplayComponent {
  locationService;
  mapDirectionsService;
  googleMapsLoader;
  origin;
  destination;
  showDirectionsLink = true;
  // Google Maps properties
  apiLoaded = false;
  directionsUrl = "";
  center = { lat: 40.7128, lng: -74.006 };
  // Default to NYC
  zoom = 12;
  options = {
    mapTypeId: "roadmap",
    zoomControl: true,
    scrollwheel: true,
    disableDoubleClickZoom: false,
    maxZoom: 20,
    minZoom: 4
  };
  originMarker;
  destinationMarker;
  markerAnimation = null;
  markerOptions = {};
  directionsResults$;
  constructor(locationService, mapDirectionsService, googleMapsLoader) {
    this.locationService = locationService;
    this.mapDirectionsService = mapDirectionsService;
    this.googleMapsLoader = googleMapsLoader;
  }
  ngOnInit() {
    this.loadGoogleMapsApi();
    this.updateDirectionsUrl();
  }
  ngOnChanges(changes) {
    if ((changes["origin"] || changes["destination"]) && this.apiLoaded) {
      this.updateMap();
      this.updateDirectionsUrl();
    }
  }
  ngAfterViewInit() {
    if (this.apiLoaded) {
      this.initMap();
    }
  }
  loadGoogleMapsApi() {
    return __async(this, null, function* () {
      try {
        yield this.googleMapsLoader.loadGoogleMapsApi();
        this.apiLoaded = true;
        if (typeof google !== "undefined" && google.maps) {
          this.markerAnimation = google.maps.Animation;
          this.markerOptions = {
            animation: google.maps.Animation.DROP
          };
        }
        this.initMap();
      } catch (error) {
        console.error("Failed to load Google Maps API:", error);
        this.apiLoaded = false;
      }
    });
  }
  initMap() {
    if (this.origin && this.destination) {
      this.updateMap();
    }
  }
  updateMap() {
    return __async(this, null, function* () {
      if (!this.origin || !this.destination)
        return;
      try {
        const originCoords = typeof this.origin === "string" ? yield this.locationService.geocodeAddress(this.origin) : this.origin;
        const destCoords = typeof this.destination === "string" ? yield this.locationService.geocodeAddress(this.destination) : this.destination;
        this.originMarker = {
          lat: originCoords.latitude,
          lng: originCoords.longitude
        };
        this.destinationMarker = {
          lat: destCoords.latitude,
          lng: destCoords.longitude
        };
        this.center = {
          lat: (originCoords.latitude + destCoords.latitude) / 2,
          lng: (originCoords.longitude + destCoords.longitude) / 2
        };
        const distance = this.calculateDistance(originCoords.latitude, originCoords.longitude, destCoords.latitude, destCoords.longitude);
        this.zoom = this.calculateZoomLevel(distance);
        this.getDirections(this.originMarker, this.destinationMarker);
      } catch (error) {
        console.error("Error updating map:", error);
      }
    });
  }
  getDirections(origin, destination) {
    const request = {
      origin,
      destination,
      travelMode: google.maps.TravelMode.DRIVING
    };
    this.directionsResults$ = this.mapDirectionsService.route(request).pipe(map((response) => {
      return response.result;
    }));
  }
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371;
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }
  deg2rad(deg) {
    return deg * (Math.PI / 180);
  }
  calculateZoomLevel(distance) {
    if (distance > 1e3)
      return 4;
    if (distance > 500)
      return 5;
    if (distance > 200)
      return 6;
    if (distance > 100)
      return 7;
    if (distance > 50)
      return 8;
    if (distance > 20)
      return 9;
    if (distance > 10)
      return 10;
    if (distance > 5)
      return 11;
    if (distance > 2)
      return 12;
    if (distance > 1)
      return 13;
    if (distance > 0.5)
      return 14;
    return 15;
  }
  updateDirectionsUrl() {
    if (!this.origin || !this.destination)
      return;
    let originStr = typeof this.origin === "string" ? this.origin : `${this.origin.latitude},${this.origin.longitude}`;
    let destinationStr = typeof this.destination === "string" ? this.destination : `${this.destination.latitude},${this.destination.longitude}`;
    this.directionsUrl = this.locationService.getGoogleMapsDirectionsUrl(originStr, destinationStr);
  }
  static \u0275fac = function MapDisplayComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MapDisplayComponent)(\u0275\u0275directiveInject(LocationService), \u0275\u0275directiveInject(MapDirectionsService), \u0275\u0275directiveInject(GoogleMapsLoaderService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MapDisplayComponent, selectors: [["app-map-display"]], inputs: { origin: "origin", destination: "destination", showDirectionsLink: "showDirectionsLink" }, features: [\u0275\u0275NgOnChangesFeature], decls: 6, vars: 5, consts: [[1, "map-card"], ["class", "map-placeholder", 4, "ngIf"], [1, "map-container"], ["height", "300px", "width", "100%", 3, "center", "zoom", "options", 4, "ngIf"], ["class", "map-actions", 4, "ngIf"], [1, "map-placeholder"], ["height", "300px", "width", "100%", 3, "center", "zoom", "options"], [3, "position", "title", "options", 4, "ngIf"], [3, "directions", 4, "ngIf"], [3, "position", "title", "options"], [3, "directions"], [1, "map-actions"], ["target", "_blank", 3, "href"], ["mat-raised-button", "", "color", "primary"]], template: function MapDisplayComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card", 0)(1, "mat-card-content");
      \u0275\u0275template(2, MapDisplayComponent_div_2_Template, 3, 0, "div", 1);
      \u0275\u0275elementStart(3, "div", 2);
      \u0275\u0275template(4, MapDisplayComponent_google_map_4_Template, 5, 8, "google-map", 3);
      \u0275\u0275elementEnd();
      \u0275\u0275template(5, MapDisplayComponent_div_5_Template, 6, 1, "div", 4);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.apiLoaded);
      \u0275\u0275advance();
      \u0275\u0275styleProp("display", ctx.apiLoaded ? "block" : "none");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.apiLoaded);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showDirectionsLink && ctx.origin && ctx.destination);
    }
  }, dependencies: [CommonModule, NgIf, AsyncPipe, MatCardModule, MatCard, MatCardContent, MatButtonModule, MatButton, MatIconModule, MatIcon, GoogleMapsModule, GoogleMap, MapDirectionsRenderer, MapMarker], styles: ["\n\n.map-card[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n}\n.map-container[_ngcontent-%COMP%] {\n  height: 300px;\n  width: 100%;\n}\n.map-placeholder[_ngcontent-%COMP%] {\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: #f5f5f5;\n  color: #666;\n}\n.map-actions[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  display: flex;\n  justify-content: center;\n}\n/*# sourceMappingURL=map-display.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MapDisplayComponent, [{
    type: Component,
    args: [{ selector: "app-map-display", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      GoogleMapsModule
    ], template: `
    <mat-card class="map-card">
      <mat-card-content>
        <div *ngIf="!apiLoaded" class="map-placeholder">
          <p>Loading map...</p>
        </div>
        <div class="map-container" [style.display]="apiLoaded ? 'block' : 'none'">
          <google-map
            *ngIf="apiLoaded"
            height="300px"
            width="100%"
            [center]="center"
            [zoom]="zoom"
            [options]="options">
            <map-marker
              *ngIf="originMarker"
              [position]="originMarker"
              [title]="'Origin'"
              [options]="markerOptions">
            </map-marker>
            <map-marker
              *ngIf="destinationMarker"
              [position]="destinationMarker"
              [title]="'Destination'"
              [options]="markerOptions">
            </map-marker>
            <map-directions-renderer
              *ngIf="directionsResults$ | async as directionsResults"
              [directions]="directionsResults">
            </map-directions-renderer>
          </google-map>
        </div>

        <div class="map-actions" *ngIf="showDirectionsLink && origin && destination">
          <a [href]="directionsUrl" target="_blank">
            <button mat-raised-button color="primary">
              <mat-icon>directions</mat-icon>
              Get Directions
            </button>
          </a>
        </div>
      </mat-card-content>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;f36db1207a406f02b008a8c1cd5ee3eb769e3f0458ef030be072b848a3c432ee;C:/Users/<USER>/code/holy rides/holy-rides/src/app/shared/components/map-display/map-display.component.ts */\n.map-card {\n  margin-bottom: 16px;\n}\n.map-container {\n  height: 300px;\n  width: 100%;\n}\n.map-placeholder {\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: #f5f5f5;\n  color: #666;\n}\n.map-actions {\n  margin-top: 16px;\n  display: flex;\n  justify-content: center;\n}\n/*# sourceMappingURL=map-display.component.css.map */\n"] }]
  }], () => [{ type: LocationService }, { type: MapDirectionsService }, { type: GoogleMapsLoaderService }], { origin: [{
    type: Input
  }], destination: [{
    type: Input
  }], showDirectionsLink: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MapDisplayComponent, { className: "MapDisplayComponent", filePath: "src/app/shared/components/map-display/map-display.component.ts", lineNumber: 92 });
})();

export {
  LocationService,
  MapDisplayComponent
};
//# sourceMappingURL=chunk-ZBUZHDSF.js.map
