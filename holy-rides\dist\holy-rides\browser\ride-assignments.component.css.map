{"version": 3, "sources": ["src/app/features/dashboard/driver/ride-assignments/ride-assignments.component.ts"], "sourcesContent": ["\n    .assignments-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .table-container {\n      margin: 20px;\n    }\n\n    .ride-table {\n      width: 100%;\n    }\n\n    \n    .no-rides {\n      padding: 20px;\n      text-align: center;\n      color: #666;\n      font-style: italic;\n    }\n\n    .status-chip {\n      border-radius: 16px;\n      padding: 4px 12px;\n      color: white;\n      font-weight: 500;\n    }\n\n    .status-requested {\n      background-color: #ff9800;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n    }\n\n    .status-in-progress {\n      background-color: #673ab7;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n    }\n\n    .header-with-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n      color: #666;\n    }\n\n    .loading-container p {\n      margin-top: 10px;\n    }\n\n    .ride-detail-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .realtime-indicator {\n      color: #4caf50;\n      font-size: 12px;\n      animation: pulse 2s infinite;\n    }\n\n    @keyframes pulse {\n      0% { opacity: 1; }\n      50% { opacity: 0.5; }\n      100% { opacity: 1; }\n    }\n\n    .desktop-view {\n      display: block;\n    }\n    .mobile-view {\n      display: none;\n    }\n\n    .filter-container {\n      margin: 0 20px 20px;\n    }\n    \n    .ride-actions-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    @media (max-width: 600px) {\n      .desktop-view {\n        display: none;\n      }\n      .mobile-view {\n        display: block;\n      }\n      .assignments-container {\n        padding: 0;\n      }\n      .table-container {\n        margin: 0;\n      }\n      .mat-tab-body-content {\n        overflow: hidden;\n      }\n      .filter-container {\n        margin: 0 0 16px;\n      }\n    }\n\n    .mobile-view .mat-expansion-panel {\n      margin: 8px 0;\n    }\n    .mobile-view .mat-expansion-panel-header {\n      font-size: 14px;\n    }\n    .mobile-view .mat-panel-title {\n      font-weight: 500;\n    }\n    .mobile-view .mat-panel-description {\n      justify-content: flex-end;\n      align-items: center;\n    }\n    .mobile-view .ride-details {\n      padding: 0 24px 16px;\n      font-size: 14px;\n    }\n    .mobile-view .ride-details p {\n      margin: 4px 0;\n    }\n    .mobile-view .mat-action-row {\n      justify-content: flex-end;\n      padding: 8px 12px 8px 24px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,UAAA;;AAGF,CAAA;AACE,SAAA;;AAIF,CAAA;AACE,WAAA;AACA,cAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA,IAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;AACA,SAAA;;AAGF,CATA,kBASA;AACE,cAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,aAAA,MAAA,GAAA;;AAGF,WAHE;AAIA;AAAK,aAAA;;AACL;AAAM,aAAA;;AACN;AAAO,aAAA;;;AAGT,CAAA;AACE,WAAA;;AAEF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,UAAA,EAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAlBF;AAmBI,aAAA;;AAEF,GAlBF;AAmBI,aAAA;;AAEF,GAtHF;AAuHI,aAAA;;AAEF,GAnHF;AAoHI,YAAA;;AAEF,GAAA;AACE,cAAA;;AAEF,GA1BF;AA2BI,YAAA,EAAA,EAAA;;;AAIJ,CAnCA,YAmCA,CAAA;AACE,UAAA,IAAA;;AAEF,CAtCA,YAsCA,CAAA;AACE,aAAA;;AAEF,CAzCA,YAyCA,CAAA;AACE,eAAA;;AAEF,CA5CA,YA4CA,CAAA;AACE,mBAAA;AACA,eAAA;;AAEF,CAhDA,YAgDA,CAAA;AACE,WAAA,EAAA,KAAA;AACA,aAAA;;AAEF,CApDA,YAoDA,CAJA,aAIA;AACE,UAAA,IAAA;;AAEF,CAvDA,YAuDA,CAAA;AACE,mBAAA;AACA,WAAA,IAAA,KAAA,IAAA;;", "names": []}