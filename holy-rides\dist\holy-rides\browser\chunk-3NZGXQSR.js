import{J as f}from"./chunk-AG3SD6JT.js";import{Z as u,ca as p,g as v}from"./chunk-ST4QC4E3.js";import{a as m,b as g,i as c}from"./chunk-ODN5LVDJ.js";var h=class d{constructor(e){this.authService=e;this.supabase=e.supabase}supabase;usersSubject=new v([]);users$=this.usersSubject.asObservable();getAllUsers(){return c(this,null,function*(){try{let{data:e,error:r}=yield this.supabase.from("profiles").select("*").order("created_at",{ascending:!1});if(r)throw r;return this.usersSubject.next(e),e}catch(e){return console.error("Error fetching users:",e),[]}})}getUsersByRole(e){return c(this,null,function*(){try{let{data:r,error:t}=yield this.supabase.from("profiles").select("*").eq("role",e).order("created_at",{ascending:!1});if(t)throw t;return r}catch(r){return console.error(`Error fetching ${e}s:`,r),[]}})}approveDriver(e){return c(this,null,function*(){try{let{error:r}=yield this.supabase.from("profiles").update({is_approved:"TRUE"}).eq("id",e);if(r)throw r;let o=this.usersSubject.value.map(i=>i.id===e?g(m({},i),{is_approved:!0,is_active:!0}):i);return this.usersSubject.next(o),!0}catch(r){return console.error("Error approving driver:",r),!1}})}updateUserStatus(e,r){return c(this,null,function*(){try{let{data:t,error:o}=yield this.supabase.from("profiles").select("role, is_approved").eq("id",e).single();if(o)throw o;if(r&&t.role!=="admin"&&!t.is_approved)throw new Error("Cannot activate unapproved user");let{error:i}=yield this.supabase.from("profiles").update({is_approved:r,updated_at:new Date().toISOString()}).eq("id",e);if(i)throw i;let a=this.usersSubject.value.map(n=>n.id===e?g(m({},n),{is_approved:r}):n);return this.usersSubject.next(a),!0}catch(t){return console.error("Error updating user status:",t),!1}})}getUserById(e){return c(this,null,function*(){try{let{data:r,error:t}=yield this.supabase.from("profiles").select("*").eq("id",e).single();if(t)throw t;return r}catch(r){return console.error("Error fetching user by ID:",r),null}})}filterUsers(e,r){return e.filter(t=>{if(r.role&&t.role!==r.role||r.isApproved!==void 0&&t.role==="driver"&&t.is_approved!==r.isApproved)return!1;if(r.searchTerm){let o=r.searchTerm.toLowerCase(),i=t.full_name?.toLowerCase()||"",s=t.email.toLowerCase(),a=t.phone?.toLowerCase()||"";if(!i.includes(o)&&!s.includes(o)&&!a.includes(o))return!1}return!0})}static \u0275fac=function(r){return new(r||d)(p(f))};static \u0275prov=u({token:d,factory:d.\u0275fac,providedIn:"root"})};var y=class d{constructor(e,r){this.userService=e;this.authService=r;this.supabase=r.supabase}supabase;sendSms(e,r){return c(this,null,function*(){try{let t=this.formatPhoneNumber(e),{data:o,error:i}=yield this.supabase.functions.invoke("twilio",{body:{to:t,message:r,from:"+17272025413"}});if(i)throw console.error("Error calling Twilio lambda function:",i),i;if(!o||!o.sid)throw new Error("No message SID returned from Twilio lambda function");return console.log(`SMS sent successfully to ${e}, SID: ${o.sid}`),o.sid}catch(t){throw console.error("Error sending SMS:",t),t}})}sendRideAssignmentNotifications(e,r){return c(this,null,function*(){try{let[t,o]=yield Promise.all([this.userService.getUserById(e.rider_id),this.userService.getUserById(r)]);if(!t||!o)throw new Error("Could not find rider or driver information");if(!t.phone||!o.phone){console.warn("Phone number missing for rider or driver. SMS notification skipped.");return}let i=new Date(e.pickup_time).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});yield this.sendEnhancedDriverAssignedNotification(e,o);let s=`You have been assigned a new ride. Pick up ${t.full_name||"your rider"} at ${e.pickup_location} at ${i} and drop off at ${e.dropoff_location}. Rider phone: ${t.phone}`,a=yield Promise.allSettled([this.sendSmsWithRetry(o.phone,s)]),[n]=a;n.status==="rejected"?console.error("Failed to send SMS to driver:",n.reason):console.log("Ride assignment notification sent successfully to driver")}catch(t){console.error("Error sending ride assignment notifications:",t)}})}sendSmsWithRetry(e,r,t=2){return c(this,null,function*(){let o;for(let i=0;i<=t;i++)try{return i>0&&(yield new Promise(s=>setTimeout(s,1e3*Math.pow(2,i-1)))),yield this.sendSms("+1"+e,r)}catch(s){o=s,console.warn(`SMS sending attempt ${i+1}/${t+1} failed:`,s)}throw o||new Error("Failed to send SMS after multiple attempts")})}sendRideStatusUpdateNotifications(e,r){return c(this,null,function*(){try{if(!e.rider_id||!e.driver_id){console.warn("Ride is missing rider or driver ID. Status update notification skipped.");return}let[t,o]=yield Promise.all([this.userService.getUserById(e.rider_id),this.userService.getUserById(e.driver_id)]);if(!t||!o)throw new Error("Could not find rider or driver information");if(!t.phone||!o.phone){console.warn("Phone number missing for rider or driver. Status update notification skipped.");return}let i="",s="";switch(r){case"in-progress":i=`Your ride has started. Your driver ${o.full_name||"is"} on the way to ${e.dropoff_location}.`,s=`You have started the ride with ${t.full_name||"your rider"}. Destination: ${e.dropoff_location}.`;break;case"completed":i=`Your ride to ${e.dropoff_location} has been completed. Thank you for using Holy Rides!`,s=`You have completed the ride to ${e.dropoff_location}. Thank you for your service!`;break;case"canceled":i="Your ride has been canceled. Please contact support if you did not request this cancellation.",s=`The ride to ${e.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;break;default:return}let a=yield Promise.allSettled([this.sendSmsWithRetry(t.phone,i),this.sendSmsWithRetry(o.phone,s)]),[n,l]=a;n.status==="rejected"&&console.error("Failed to send status update SMS to rider:",n.reason),l.status==="rejected"&&console.error("Failed to send status update SMS to driver:",l.reason),n.status==="fulfilled"&&l.status==="fulfilled"&&console.log(`Ride status update (${r}) notifications sent successfully to both rider and driver`)}catch(t){console.error("Error sending ride status update notifications:",t)}})}sendRideBookingConfirmation(e){return c(this,null,function*(){try{let r=yield this.userService.getUserById(e.rider_id);if(!r||!r.phone){console.warn("Rider not found or phone number missing. Booking confirmation SMS skipped.");return}let t=new Date(e.pickup_time),i=t.getTime()<=Date.now()+30*60*1e3?"ASAP":t.toLocaleString(),s=t.toLocaleDateString(),a=e.fare?`$${e.fare.toFixed(2)}`:"TBD",n=`Your ride has been booked!
Pickup: ${e.pickup_location}
Dropoff: ${e.dropoff_location}
Pick up date: ${s}
Pickup Time: ${i}
Fare Estimate: ${a}
Driver will be assigned shortly. You'll receive updates as your ride details are confirmed.
Msg & data rates may apply. Message frequency varies.
Reply STOP to unsubscribe. View our policy at: https://bookholyrides.com/?p=1163`;yield this.sendSmsWithRetry(r.phone,n),console.log("Ride booking confirmation sent to rider")}catch(r){console.error("Error sending ride booking confirmation:",r)}})}sendRideBookingNotificationToDrivers(e){return c(this,null,function*(){try{let t=(yield this.userService.getUsersByRole("driver")).filter(n=>n.is_approved&&n.phone);if(t.length===0){console.warn("No approved drivers with phone numbers found. Driver notification skipped.");return}let o="A new trip has been booked! Login now at https://app.bookholyrides.com to view the ride details in the Holy Rides app.",i=yield Promise.allSettled(t.map(n=>this.sendSmsWithRetry(n.phone,o))),s=i.filter(n=>n.status==="fulfilled").length,a=i.filter(n=>n.status==="rejected").length;console.log(`Ride booking notification sent to ${s} drivers, ${a} failed`)}catch(r){console.error("Error sending ride booking notifications to drivers:",r)}})}sendEnhancedDriverAssignedNotification(e,r){return c(this,null,function*(){try{let t=yield this.userService.getUserById(e.rider_id);if(!t||!t.phone){console.warn("Rider not found or phone number missing. Driver assigned notification skipped.");return}let o=r.vehicle_info||"Vehicle details will be provided",i=r.license_number||"License details will be provided",a=`Great news \u2014 a driver has been assigned to your trip! \u{1F698}
Driver Name: ${r.full_name||"Driver"}
Vehicle: ${o}
License Number: ${i}
ETA: ETA will be calculated
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(t.phone,a),console.log("Enhanced driver assigned notification sent to rider")}catch(t){console.error("Error sending enhanced driver assigned notification:",t)}})}sendRideCancellationNotifications(e){return c(this,null,function*(){try{let r=[],t=yield this.userService.getUserById(e.rider_id);if(t&&t.phone){let o=`Your ride from ${e.pickup_location} to ${e.dropoff_location} has been cancelled. You can book a new ride at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;r.push(this.sendSmsWithRetry(t.phone,o))}if(e.driver_id){let o=yield this.userService.getUserById(e.driver_id);if(o&&o.phone){let i=`The ride to ${e.dropoff_location} has been cancelled. Please check your dashboard for new ride opportunities at https://app.bookholyrides.com
Msg & data rates may apply. Reply STOP to unsubscribe.`;r.push(this.sendSmsWithRetry(o.phone,i))}}if(r.length>0){let o=yield Promise.allSettled(r),i=o.filter(a=>a.status==="fulfilled").length,s=o.filter(a=>a.status==="rejected").length;console.log(`Ride cancellation notifications: ${i} sent, ${s} failed`)}}catch(r){console.error("Error sending ride cancellation notifications:",r)}})}sendRideCompletionNotification(e){return c(this,null,function*(){try{let r=yield this.userService.getUserById(e.rider_id);if(!r||!r.phone){console.warn("Rider not found or phone number missing. Ride completion notification skipped.");return}let o=`Your ride is complete! \u{1F698}
Thanks for riding with Holy Rides. Please complete payment now in the amount of ${e.fare?`$${e.fare.toFixed(2)}`:"$0.00"} using one of the following methods:
Cash app: https://cash.app/$HolyRides24
Square: https://square.link/u/o9zzuiAv?src=sheet
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(r.phone,o),console.log("Ride completion notification sent to rider")}catch(r){console.error("Error sending ride completion notification:",r)}})}sendPaymentConfirmationNotification(e,r){return c(this,null,function*(){try{let t=yield this.userService.getUserById(e.rider_id);if(!t||!t.phone){console.warn("Rider not found or phone number missing. Payment confirmation notification skipped.");return}let o=new Date(e.pickup_time),s=o.getTime()<=Date.now()+30*60*1e3?"ASAP":o.toLocaleString(),a=o.toLocaleDateString(),l=`Thank you! \u{1F389}
We've received your payment of ${`$${r.toFixed(2)}`} for your recent ride.
Pickup: ${e.pickup_location}
Dropoff: ${e.dropoff_location}
Pick up date: ${a}
Pickup Time: ${s}

Thanks for riding with Holy Rides. See you next time!
Msg & data rates may apply. Reply STOP to unsubscribe.`;yield this.sendSmsWithRetry(t.phone,l),console.log("Payment confirmation notification sent to rider")}catch(t){console.error("Error sending payment confirmation notification:",t)}})}formatPhoneNumber(e){return e.startsWith("+")?e:e.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)?`+${e}`:`+1${e.replace(/\D/g,"")}`}static \u0275fac=function(r){return new(r||d)(p(h),p(f))};static \u0275prov=u({token:d,factory:d.\u0275fac,providedIn:"root"})};export{h as a,y as b};
