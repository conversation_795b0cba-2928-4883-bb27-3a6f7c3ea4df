{"version": 3, "sources": ["src/app/features/dashboard/rider/rider.component.ts"], "sourcesContent": ["\n    .dashboard-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n        background-color: #f5f5f5;\n    }\n\n    .table-container {\n      margin: 20px;\n    }\n\n    .ride-table {\n      width: 100%;\n    }\n\n    .status-chip {\n      border-radius: 16px;\n      padding: 4px 12px;\n      color: white;\n      font-weight: 500;\n    }\n\n    .status-requested {\n      background-color: #ff9800;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n    }\n\n    .status-in-progress {\n      background-color: #673ab7;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n    }\n\n    .payment-status-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .payment-status-paid {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .payment-status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-status-refunded {\n      background-color: #9e9e9e;\n      color: white;\n    }\n\n    .payment-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .close-payment-button {\n      position: absolute;\n      top: 20px;\n      right: 20px;\n      background-color: white;\n    }\n\n    .ride-detail-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    /* Mobile View Styles */\n    .mobile-view {\n      display: none;\n    }\n\n    .desktop-view {\n      display: block;\n    }\n\n    .ride-actions-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .ride-time {\n      font-size: 12px;\n      color: #666;\n    }\n\n    @media (max-width: 600px) {\n      .desktop-view {\n        display: none;\n      }\n      .mobile-view {\n        display: block;\n      }\n      .dashboard-container {\n        padding: 0;\n      }\n      .table-container {\n        margin: 0;\n      }\n      .mat-tab-body-content {\n        overflow: hidden;\n      }\n    }\n\n    .mobile-view .mat-expansion-panel {\n      margin: 8px 0;\n    }\n    .mobile-view .mat-expansion-panel-header {\n      font-size: 14px;\n    }\n    .mobile-view .mat-panel-title {\n      font-weight: 500;\n    }\n    .mobile-view .mat-panel-description {\n      justify-content: flex-end;\n      align-items: center;\n    }\n    .mobile-view .ride-details {\n      padding: 0 24px 16px;\n      font-size: 14px;\n    }\n    .mobile-view .ride-details p {\n      margin: 4px 0;\n    }\n    .mobile-view .mat-action-row {\n      justify-content: flex-end;\n      padding: 8px 12px 8px 24px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;AACE,oBAAA;;AAGJ,CAAA;AACE,UAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA,IAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,oBAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;;AAIF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GAhBF;AAiBI,aAAA;;AAEF,GAvBF;AAwBI,aAAA;;AAEF,GA1HF;AA2HI,aAAA;;AAEF,GAtHF;AAuHI,YAAA;;AAEF,GAAA;AACE,cAAA;;;AAIJ,CArCA,YAqCA,CAAA;AACE,UAAA,IAAA;;AAEF,CAxCA,YAwCA,CAAA;AACE,aAAA;;AAEF,CA3CA,YA2CA,CAAA;AACE,eAAA;;AAEF,CA9CA,YA8CA,CAAA;AACE,mBAAA;AACA,eAAA;;AAEF,CAlDA,YAkDA,CAAA;AACE,WAAA,EAAA,KAAA;AACA,aAAA;;AAEF,CAtDA,YAsDA,CAJA,aAIA;AACE,UAAA,IAAA;;AAEF,CAzDA,YAyDA,CAAA;AACE,mBAAA;AACA,WAAA,IAAA,KAAA,IAAA;;", "names": []}