{"version": 3, "sources": ["src/app/features/auth/profile/profile.component.ts", "src/app/features/auth/profile/profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { AuthService } from '../../../core/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-profile',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatCardModule\r\n  ],\r\n  templateUrl: './profile.component.html',\r\n  styleUrls: ['./profile.component.scss']\r\n})\r\nexport class ProfileComponent implements OnInit {\r\n  profileForm: FormGroup;\r\n  loading = false;\r\n  userId: string | null = null;\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private authService: AuthService,\r\n    private snackBar: MatSnackBar,\r\n    private router: Router\r\n  ) {\r\n    this.profileForm = this.formBuilder.group({\r\n      full_name: ['', Validators.required],\r\n      phone: ['', Validators.required],\r\n      email: [{ value: '', disabled: true }]\r\n    });\r\n  }\r\n\r\n  async ngOnInit() {\r\n    const user = await this.authService.getCurrentUser();\r\n    if (user) {\r\n      this.userId = user.id;\r\n      this.profileForm.patchValue({\r\n        email: user.email,\r\n        full_name: user.full_name || '',\r\n        phone: user.phone || ''\r\n      });\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    if (this.profileForm.invalid || !this.userId) {\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n\r\n    try {\r\n      const success = await this.authService.updateProfile(this.profileForm.getRawValue());\r\n\r\n      if (!success) {\r\n        throw new Error('Failed to update profile');\r\n      }\r\n\r\n      this.snackBar.open('Profile updated successfully', 'Close', {\r\n        duration: 3000\r\n      });\r\n\r\n      // Get user role and navigate to appropriate dashboard\r\n      const role = await this.authService.getUserRole();\r\n      if (role) {\r\n        await this.router.navigate([this.authService.getDashboardRouteForRole(role)]);\r\n      } else {\r\n        throw new Error('User role not found');\r\n      }\r\n\r\n    } catch (error: any) {\r\n      this.snackBar.open(error.message || 'An error occurred', 'Close', {\r\n        duration: 3000\r\n      });\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n}\r\n", "<div class=\"profile-container\">\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title>Profile</mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <form [formGroup]=\"profileForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput formControlName=\"email\" readonly>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Full Name</mat-label>\r\n          <input matInput formControlName=\"full_name\" placeholder=\"Enter your full name\">\r\n          <mat-error *ngIf=\"profileForm.get('full_name')?.errors?.['required']\">Full name is required</mat-error>\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Phone Number</mat-label>\r\n          <input matInput formControlName=\"phone\" placeholder=\"Enter your phone number\">\r\n          <mat-error *ngIf=\"profileForm.get('phone')?.errors?.['required']\">Phone number is required</mat-error>\r\n        </mat-form-field>\r\n\r\n        <div class=\"button-container\">\r\n          <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"profileForm.invalid || loading\">\r\n            {{ loading ? 'Saving...' : 'Save Changes' }}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACeU,IAAA,yBAAA,GAAA,WAAA;AAAsE,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA;;;;;AAM3F,IAAA,yBAAA,GAAA,WAAA;AAAkE,IAAA,iBAAA,GAAA,0BAAA;AAAwB,IAAA,uBAAA;;;ADI9F,IAAO,mBAAP,MAAO,kBAAgB;EAMjB;EACA;EACA;EACA;EARV;EACA,UAAU;EACV,SAAwB;EAExB,YACU,aACA,aACA,UACA,QAAc;AAHd,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,WAAA;AACA,SAAA,SAAA;AAER,SAAK,cAAc,KAAK,YAAY,MAAM;MACxC,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,OAAO,CAAC,IAAI,WAAW,QAAQ;MAC/B,OAAO,CAAC,EAAE,OAAO,IAAI,UAAU,KAAI,CAAE;KACtC;EACH;EAEM,WAAQ;;AACZ,YAAM,OAAO,MAAM,KAAK,YAAY,eAAc;AAClD,UAAI,MAAM;AACR,aAAK,SAAS,KAAK;AACnB,aAAK,YAAY,WAAW;UAC1B,OAAO,KAAK;UACZ,WAAW,KAAK,aAAa;UAC7B,OAAO,KAAK,SAAS;SACtB;MACH;IACF;;EAEM,WAAQ;;AACZ,UAAI,KAAK,YAAY,WAAW,CAAC,KAAK,QAAQ;AAC5C;MACF;AAEA,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,YAAY,cAAc,KAAK,YAAY,YAAW,CAAE;AAEnF,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM,0BAA0B;QAC5C;AAEA,aAAK,SAAS,KAAK,gCAAgC,SAAS;UAC1D,UAAU;SACX;AAGD,cAAM,OAAO,MAAM,KAAK,YAAY,YAAW;AAC/C,YAAI,MAAM;AACR,gBAAM,KAAK,OAAO,SAAS,CAAC,KAAK,YAAY,yBAAyB,IAAI,CAAC,CAAC;QAC9E,OAAO;AACL,gBAAM,IAAI,MAAM,qBAAqB;QACvC;MAEF,SAAS,OAAY;AACnB,aAAK,SAAS,KAAK,MAAM,WAAW,qBAAqB,SAAS;UAChE,UAAU;SACX;MACH;AACE,aAAK,UAAU;MACjB;IACF;;;qCA/DW,mBAAgB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,SAAA,YAAA,EAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,aAAA,eAAA,sBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,SAAA,eAAA,yBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACzB7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,UAAA,EACnB,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA,EAAiB;AAE1C,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,QAAA,CAAA;AACgB,MAAA,qBAAA,YAAA,SAAA,qDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AACpD,MAAA,yBAAA,GAAA,kBAAA,CAAA,EAAqC,GAAA,WAAA;AACxB,MAAA,iBAAA,GAAA,OAAA;AAAK,MAAA,uBAAA;AAChB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACpB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,aAAA,CAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACvB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,aAAA,CAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA8B,IAAA,UAAA,CAAA;AAE1B,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD,EACU,EACV;;;;;AAzBD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,WAAA;AASU,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAI4C,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,YAAA,WAAA,IAAA,OAAA;AACtD,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,UAAA,cAAA,gBAAA,GAAA;;oBDXR,cAAY,MACZ,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,iBACnB,oBAAkB,cAAA,UAAA,UAClB,gBAAc,UACd,iBAAe,WACf,eAAa,SAAA,gBAAA,eAAA,YAAA,GAAA,QAAA,CAAA,kxBAAA,EAAA,CAAA;;;sEAKJ,kBAAgB,CAAA;UAd5B;uBACW,eAAa,YACX,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;OACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,ooBAAA,EAAA,CAAA;;;;6EAIU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,sDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}