{"version": 3, "sources": ["src/app/features/dashboard/rider/ride-request/ride-request.component.ts", "src/app/features/dashboard/rider/payment-methods/payment-methods.component.ts", "src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts", "src/app/features/dashboard/rider/rider.component.ts"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { RideService } from '../../../../core/services/ride.service';\r\nimport { AuthService } from '../../../../core/services/auth.service';\r\nimport { PaymentService } from '../../../../core/services/payment.service';\r\nimport { MapDisplayComponent } from '../../../../shared/components/map-display/map-display.component';\r\nimport { Coordinates, LocationService } from '../../../../core/services/location.service';\r\nimport { NgxMatTimepickerModule } from 'ngx-mat-timepicker';\r\n\r\n@Component({\r\n  selector: 'app-ride-request',\r\n  standalone: true,\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatCardModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatButtonModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule,\r\n    MatIconModule,\r\n    MapDisplayComponent,\r\n    NgxMatTimepickerModule\r\n  ],\r\n  template: `\r\n    <mat-card>\r\n      <mat-card-header>\r\n        <mat-card-title>Request a Ride</mat-card-title>\r\n      </mat-card-header>\r\n      <mat-card-content>\r\n        <form [formGroup]=\"rideForm\" (ngSubmit)=\"onSubmit()\">\r\n          <div class=\"location-fields\">\r\n            <mat-form-field appearance=\"outline\">\r\n              <mat-label>Pickup Location</mat-label>\r\n              <input matInput formControlName=\"pickup_location\" placeholder=\"Enter pickup location\">\r\n              <button mat-icon-button matSuffix type=\"button\" (click)=\"useCurrentLocation()\" title=\"Use current location\">\r\n                <mat-icon>my_location</mat-icon>\r\n              </button>\r\n              <mat-error *ngIf=\"rideForm.get('pickup_location')?.errors?.['required']\">\r\n                Pickup location is required\r\n              </mat-error>\r\n            </mat-form-field>\r\n\r\n            <mat-form-field appearance=\"outline\">\r\n              <mat-label>Dropoff Location</mat-label>\r\n              <input matInput formControlName=\"dropoff_location\" placeholder=\"Enter dropoff location\">\r\n              <mat-error *ngIf=\"rideForm.get('dropoff_location')?.errors?.['required']\">\r\n                Dropoff location is required\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <div *ngIf=\"showMap && rideForm.get('pickup_location')?.value && rideForm.get('dropoff_location')?.value\">\r\n            <app-map-display\r\n              [origin]=\"rideForm.get('pickup_location')?.value\"\r\n              [destination]=\"rideForm.get('dropoff_location')?.value\">\r\n            </app-map-display>\r\n\r\n            <div *ngIf=\"estimatedFare\" class=\"fare-estimate\">\r\n              <p>Estimated fare: <strong>{{estimatedFare ? '$' + estimatedFare.toFixed(2) : ''}}</strong></p>\r\n              <p *ngIf=\"estimatedDistance\">Distance: {{estimatedDistance}} miles</p>\r\n              <p *ngIf=\"estimatedDuration\">Duration: {{estimatedDuration}} minutes</p>\r\n            </div>\r\n          </div>\r\n<!--\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Number of Passengers</mat-label>\r\n            <input matInput type=\"number\" formControlName=\"passengers\" min=\"1\" max=\"4\">\r\n            <mat-error *ngIf=\"rideForm.get('passengers')?.errors?.['required']\">\r\n              Number of passengers is required\r\n            </mat-error>\r\n            <mat-error *ngIf=\"rideForm.get('passengers')?.errors?.['min']\">\r\n              Must have at least 1 passenger\r\n            </mat-error>\r\n            <mat-error *ngIf=\"rideForm.get('passengers')?.errors?.['max']\">\r\n              Maximum 4 passengers allowed\r\n            </mat-error>\r\n          </mat-form-field> -->\r\n\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Pickup Date</mat-label>\r\n            <input matInput [matDatepicker]=\"picker\" formControlName=\"pickup_date\">\r\n            <mat-datepicker-toggle matSuffix [for]=\"picker\"></mat-datepicker-toggle>\r\n            <mat-datepicker #picker></mat-datepicker>\r\n            <mat-error *ngIf=\"rideForm.get('pickup_date')?.errors?.['required']\">\r\n              Pickup date is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <mat-form-field appearance=\"outline\">\r\n            <mat-label>Pick a time</mat-label>\r\n            <input matInput [ngxMatTimepicker]=\"timepicker\" formControlName=\"pickup_time\">\r\n            <ngx-mat-timepicker-toggle matSuffix [for]=\"timepicker\">\r\n              <mat-icon ngxMatTimepickerToggleIcon>keyboard_arrow_down</mat-icon>\r\n            </ngx-mat-timepicker-toggle>\r\n            <ngx-mat-timepicker #timepicker></ngx-mat-timepicker>\r\n            <mat-error *ngIf=\"rideForm.get('pickup_time')?.errors?.['required']\">\r\n              Pickup time is required\r\n            </mat-error>\r\n          </mat-form-field>\r\n\r\n          <!-- <mat-form-field appearance=\"outline\">\r\n            <mat-label>Special Notes</mat-label>\r\n            <textarea matInput formControlName=\"notes\" placeholder=\"Any special requirements?\"></textarea>\r\n          </mat-form-field> -->\r\n\r\n          <div class=\"button-container\">\r\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"rideForm.invalid || loading\">\r\n              {{ loading ? 'Requesting...' : 'Request Ride' }}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </mat-card-content>\r\n    </mat-card>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n      margin: 20px;\r\n    }\r\n\r\n    form {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n      max-width: 600px;\r\n      margin: 0 auto;\r\n    }\r\n\r\n    .location-fields {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 16px;\r\n    }\r\n\r\n    .button-container {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-top: 16px;\r\n    }\r\n\r\n    textarea {\r\n      min-height: 100px;\r\n    }\r\n\r\n    .fare-estimate {\r\n      background-color: #f5f5f5;\r\n      padding: 16px;\r\n      border-radius: 4px;\r\n      margin-top: 16px;\r\n      margin-bottom: 16px;\r\n    }\r\n\r\n    .fare-estimate p {\r\n      margin: 8px 0;\r\n    }\r\n  `]\r\n})\r\nexport class RideRequestComponent implements OnInit {\r\n  rideForm: FormGroup;\r\n  loading = false;\r\n  showMap = false;\r\n  estimatedFare: number | null = null;\r\n  estimatedDistance: number | null = null;\r\n  estimatedDuration: number | null = null;\r\n\r\n  private locationCoordinates: {\r\n    pickup?: Coordinates;\r\n    dropoff?: Coordinates;\r\n  } = {};\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private rideService: RideService,\r\n    private authService: AuthService,\r\n    private locationService: LocationService,\r\n    private paymentService: PaymentService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    this.rideForm = this.formBuilder.group({\r\n      pickup_location: ['', Validators.required],\r\n      dropoff_location: ['', Validators.required],\r\n      //passengers: [1, [Validators.required, Validators.min(1), Validators.max(4)]],\r\n      pickup_date: [new Date(), Validators.required],\r\n      pickup_time: ['12:00 PM', Validators.required],\r\n     // notes: ['']\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Listen for changes to pickup and dropoff locations\r\n    this.rideForm.get('pickup_location')?.valueChanges.subscribe(() => {\r\n      this.updateRouteEstimates();\r\n    });\r\n\r\n    this.rideForm.get('dropoff_location')?.valueChanges.subscribe(() => {\r\n      this.updateRouteEstimates();\r\n    });\r\n  }\r\n\r\n  async useCurrentLocation(): Promise<void> {\r\n    try {\r\n      const coords = await this.locationService.getCurrentLocation();\r\n\r\n      // For demo purposes, we'll just set a placeholder address\r\n      // In a real app, you would use reverse geocoding to get the address\r\n      this.rideForm.patchValue({\r\n        pickup_location: `Current Location (${coords.latitude.toFixed(6)}, ${coords.longitude.toFixed(6)})`\r\n      });\r\n\r\n      this.locationCoordinates.pickup = coords;\r\n      this.snackBar.open('Current location detected', 'Close', { duration: 2000 });\r\n    } catch (error: any) {\r\n      this.snackBar.open(error.message || 'Failed to get current location', 'Close', { duration: 3000 });\r\n    }\r\n  }\r\n\r\n  async updateRouteEstimates(): Promise<void> {\r\n    const pickup = this.rideForm.get('pickup_location')?.value;\r\n    const dropoff = this.rideForm.get('dropoff_location')?.value;\r\n\r\n    if (pickup && dropoff) {\r\n      this.showMap = true;\r\n\r\n      try {\r\n        // In a real app, you would use the LocationService to calculate the route\r\n        // For demo purposes, we'll use the PaymentService's mock implementation\r\n       const {fare, routeInfo} = await this.paymentService.estimateFare(pickup, dropoff);\r\n          this.estimatedFare = fare;\r\n          this.estimatedDistance = routeInfo.distance;\r\n          this.estimatedDuration = routeInfo.duration;\r\n        // Mock distance and duration values\r\n       // this.estimatedDistance = Math.floor(Math.random() * 18) + 2; // 2-20 miles\r\n        //this.estimatedDuration = Math.floor(Math.random() * 55) + 5; // 5-60 minutes\r\n      } catch (error) {\r\n        console.error('Error calculating route:', error);\r\n      }\r\n    } else {\r\n      this.showMap = false;\r\n      this.estimatedFare = null;\r\n      this.estimatedDistance = null;\r\n      this.estimatedDuration = null;\r\n    }\r\n  }\r\n\r\n  async onSubmit() {\r\n    if (this.rideForm.invalid) return;\r\n\r\n    this.loading = true;\r\n\r\n    try {\r\n      const user = await this.authService.getCurrentUser();\r\n      if (!user) throw new Error('User not found');\r\n\r\n      // Get coordinates for pickup and dropoff locations if not already set\r\n      if (!this.locationCoordinates.pickup) {\r\n        this.locationCoordinates.pickup = await this.locationService.geocodeAddress(this.rideForm.value.pickup_location);\r\n      }\r\n\r\n      if (!this.locationCoordinates.dropoff) {\r\n        this.locationCoordinates.dropoff = await this.locationService.geocodeAddress(this.rideForm.value.dropoff_location);\r\n      }\r\n\r\n      // Calculate route information\r\n      const routeInfo = await this.locationService.calculateRoute(\r\n        this.locationCoordinates.pickup,\r\n        this.locationCoordinates.dropoff\r\n      );\r\n      console.log(routeInfo)\r\n\r\n      // Combine date and time\r\n      const pickupDate = this.rideForm.value.pickup_date;\r\n      const pickupTime = this.rideForm.value.pickup_time;\r\n\r\n      // Create a combined date-time object\r\n      const combinedDateTime = new Date(pickupDate);\r\n\r\n      // Parse the time string (assuming format like \"12:00 PM\")\r\n      const timeParts = pickupTime.match(/(\\d+):(\\d+)\\s?(AM|PM)?/i);\r\n      if (timeParts) {\r\n        let hours = parseInt(timeParts[1], 10);\r\n        const minutes = parseInt(timeParts[2], 10);\r\n        const period = timeParts[3] ? timeParts[3].toUpperCase() : null;\r\n\r\n        // Convert to 24-hour format if needed\r\n        if (period === 'PM' && hours < 12) {\r\n          hours += 12;\r\n        } else if (period === 'AM' && hours === 12) {\r\n          hours = 0;\r\n        }\r\n\r\n        combinedDateTime.setHours(hours, minutes, 0, 0);\r\n      }\r\n\r\n      const ride = {\r\n        ...this.rideForm.value,\r\n        rider_id: user.id,\r\n        status: 'requested',\r\n        pickup_time: combinedDateTime.toISOString(),\r\n        // Add location coordinates\r\n        pickup_latitude: this.locationCoordinates.pickup?.latitude,\r\n        pickup_longitude: this.locationCoordinates.pickup?.longitude,\r\n        dropoff_latitude: this.locationCoordinates.dropoff?.latitude,\r\n        dropoff_longitude: this.locationCoordinates.dropoff?.longitude,\r\n        // Add route information\r\n        distance_miles: routeInfo.distance,\r\n        duration_minutes: routeInfo.duration,\r\n        // Add fare\r\n        fare: this.estimatedFare || await this.paymentService.estimateFare(\r\n          this.rideForm.value.pickup_location,\r\n          this.rideForm.value.dropoff_location\r\n        )\r\n      };\r\n\r\n      await this.rideService.createRide(ride);\r\n\r\n      this.snackBar.open('Ride requested successfully!', 'Close', { duration: 3000 });\r\n      this.rideForm.reset({\r\n        passengers: 1,\r\n        pickup_date: new Date(),\r\n        pickup_time: '12:00 PM'\r\n      });\r\n\r\n      // Reset state\r\n      this.showMap = false;\r\n      this.estimatedFare = null;\r\n      this.estimatedDistance = null;\r\n      this.estimatedDuration = null;\r\n      this.locationCoordinates = {};\r\n    } catch (error: any) {\r\n      this.snackBar.open(error.message || 'Failed to request ride', 'Close', { duration: 3000 });\r\n    } finally {\r\n      this.loading = false;\r\n    }\r\n  }\r\n}", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatListModule } from '@angular/material/list';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { AuthService } from '../../../../core/services/auth.service';\n\ninterface PaymentMethod {\n  id: string;\n  brand: string;\n  last4: string;\n  expMonth: number;\n  expYear: number;\n  isDefault: boolean;\n}\n\n@Component({\n  selector: 'app-payment-methods',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatDividerModule,\n    MatListModule,\n    MatProgressSpinnerModule\n  ],\n  template: `\n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>Payment Methods</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div *ngIf=\"loading\" class=\"loading-container\">\n          <mat-spinner diameter=\"40\"></mat-spinner>\n          <p>Loading payment methods...</p>\n        </div>\n\n        <div *ngIf=\"!loading\">\n          <div *ngIf=\"paymentMethods.length === 0\" class=\"no-methods\">\n            <p>You don't have any payment methods yet.</p>\n          </div>\n\n          <mat-list *ngIf=\"paymentMethods.length > 0\">\n            <mat-list-item *ngFor=\"let method of paymentMethods\">\n              <div class=\"payment-method-item\">\n                <div class=\"payment-method-info\">\n                  <mat-icon>credit_card</mat-icon>\n                  <span>{{ method.brand }} •••• {{ method.last4 }}</span>\n                  <span class=\"expiry\">Expires {{ method.expMonth }}/{{ method.expYear }}</span>\n                  <span *ngIf=\"method.isDefault\" class=\"default-badge\">Default</span>\n                </div>\n                <div class=\"payment-method-actions\">\n                  <button mat-icon-button color=\"primary\" *ngIf=\"!method.isDefault\"\n                          (click)=\"setDefaultPaymentMethod(method.id)\" matTooltip=\"Set as default\">\n                    <mat-icon>star_outline</mat-icon>\n                  </button>\n                  <button mat-icon-button color=\"warn\" (click)=\"removePaymentMethod(method.id)\" matTooltip=\"Remove\">\n                    <mat-icon>delete</mat-icon>\n                  </button>\n                </div>\n              </div>\n            </mat-list-item>\n          </mat-list>\n\n          <mat-divider *ngIf=\"paymentMethods.length > 0\" class=\"divider\"></mat-divider>\n\n          <div class=\"add-payment-section\">\n            <h3>Add New Payment Method</h3>\n            <form [formGroup]=\"paymentForm\" (ngSubmit)=\"addPaymentMethod()\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Card Number</mat-label>\n                <input matInput formControlName=\"cardNumber\" placeholder=\"1234 5678 9012 3456\">\n                <mat-error *ngIf=\"paymentForm.get('cardNumber')?.errors?.['required']\">\n                  Card number is required\n                </mat-error>\n                <mat-error *ngIf=\"paymentForm.get('cardNumber')?.errors?.['pattern']\">\n                  Invalid card number\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Expiration Month</mat-label>\n                  <input matInput type=\"number\" formControlName=\"expMonth\" placeholder=\"MM\" min=\"1\" max=\"12\">\n                  <mat-error *ngIf=\"paymentForm.get('expMonth')?.errors?.['required']\">\n                    Expiration month is required\n                  </mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('expMonth')?.errors?.['min'] || paymentForm.get('expMonth')?.errors?.['max']\">\n                    Month must be between 1 and 12\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\">\n                  <mat-label>Expiration Year</mat-label>\n                  <input matInput type=\"number\" formControlName=\"expYear\" placeholder=\"YYYY\" min=\"2023\">\n                  <mat-error *ngIf=\"paymentForm.get('expYear')?.errors?.['required']\">\n                    Expiration year is required\n                  </mat-error>\n                  <mat-error *ngIf=\"paymentForm.get('expYear')?.errors?.['min']\">\n                    Year must be {{ currentYear }} or later\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>CVV</mat-label>\n                <input matInput formControlName=\"cvv\" placeholder=\"123\">\n                <mat-error *ngIf=\"paymentForm.get('cvv')?.errors?.['required']\">\n                  CVV is required\n                </mat-error>\n                <mat-error *ngIf=\"paymentForm.get('cvv')?.errors?.['pattern']\">\n                  CVV must be 3 or 4 digits\n                </mat-error>\n              </mat-form-field>\n\n              <div class=\"button-container\">\n                <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"paymentForm.invalid || submitting\">\n                  {{ submitting ? 'Adding...' : 'Add Payment Method' }}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [`\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .no-methods {\n      text-align: center;\n      padding: 20px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .payment-method-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      width: 100%;\n      padding: 8px 0;\n    }\n\n    .payment-method-info {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .expiry {\n      color: rgba(0, 0, 0, 0.6);\n      font-size: 0.9em;\n      margin-left: 8px;\n    }\n\n    .default-badge {\n      background-color: #4caf50;\n      color: white;\n      padding: 2px 8px;\n      border-radius: 12px;\n      font-size: 0.8em;\n      margin-left: 8px;\n    }\n\n    .divider {\n      margin: 20px 0;\n    }\n\n    .add-payment-section {\n      margin-top: 20px;\n    }\n\n    form {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      max-width: 500px;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 16px;\n    }\n\n    .form-row mat-form-field {\n      flex: 1;\n    }\n\n    .button-container {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 16px;\n    }\n  `]\n})\nexport class PaymentMethodsComponent implements OnInit {\n  paymentMethods: PaymentMethod[] = [];\n  paymentForm: FormGroup;\n  loading = false;\n  submitting = false;\n  currentYear = new Date().getFullYear();\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.paymentForm = this.formBuilder.group({\n      cardNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{16}$/)]],\n      expMonth: ['', [Validators.required, Validators.min(1), Validators.max(12)]],\n      expYear: ['', [Validators.required, Validators.min(this.currentYear)]],\n      cvv: ['', [Validators.required, Validators.pattern(/^[0-9]{3,4}$/)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadPaymentMethods();\n  }\n\n  loadPaymentMethods(): void {\n    this.loading = true;\n    \n    // Mock implementation - in a real app, you would fetch from your backend\n    setTimeout(() => {\n      // For demo purposes, we'll create some mock payment methods\n      this.paymentMethods = [\n        {\n          id: 'pm_1',\n          brand: 'Visa',\n          last4: '4242',\n          expMonth: 12,\n          expYear: 2025,\n          isDefault: true\n        },\n        {\n          id: 'pm_2',\n          brand: 'Mastercard',\n          last4: '5555',\n          expMonth: 10,\n          expYear: 2024,\n          isDefault: false\n        }\n      ];\n      this.loading = false;\n    }, 1000);\n  }\n\n  addPaymentMethod(): void {\n    if (this.paymentForm.invalid) return;\n\n    this.submitting = true;\n\n    // Mock implementation - in a real app, you would call Square API\n    setTimeout(() => {\n      const formValue = this.paymentForm.value;\n      \n      // Create a new payment method\n      const newMethod: PaymentMethod = {\n        id: `pm_${Math.random().toString(36).substring(2, 9)}`,\n        brand: this.getCardBrand(formValue.cardNumber),\n        last4: formValue.cardNumber.slice(-4),\n        expMonth: formValue.expMonth,\n        expYear: formValue.expYear,\n        isDefault: this.paymentMethods.length === 0 // Make default if it's the first one\n      };\n\n      this.paymentMethods.push(newMethod);\n      this.paymentForm.reset();\n      this.submitting = false;\n      this.snackBar.open('Payment method added successfully', 'Close', { duration: 3000 });\n    }, 1500);\n  }\n\n  setDefaultPaymentMethod(id: string): void {\n    // Update default status\n    this.paymentMethods = this.paymentMethods.map(method => ({\n      ...method,\n      isDefault: method.id === id\n    }));\n\n    this.snackBar.open('Default payment method updated', 'Close', { duration: 3000 });\n  }\n\n  removePaymentMethod(id: string): void {\n    const isDefault = this.paymentMethods.find(m => m.id === id)?.isDefault;\n    \n    // Remove the payment method\n    this.paymentMethods = this.paymentMethods.filter(method => method.id !== id);\n\n    // If we removed the default method and there are other methods, make the first one default\n    if (isDefault && this.paymentMethods.length > 0) {\n      this.paymentMethods[0].isDefault = true;\n    }\n\n    this.snackBar.open('Payment method removed', 'Close', { duration: 3000 });\n  }\n\n  private getCardBrand(cardNumber: string): string {\n    // Simple logic to determine card brand based on first digit\n    const firstDigit = cardNumber.charAt(0);\n    \n    switch (firstDigit) {\n      case '4':\n        return 'Visa';\n      case '5':\n        return 'Mastercard';\n      case '3':\n        return 'Amex';\n      case '6':\n        return 'Discover';\n      default:\n        return 'Card';\n    }\n  }\n}\n", "import { Component, Input, OnInit, AfterViewInit, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { Ride } from '../../../../core/models/ride.model';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { environment } from '../../../../../environments/environment';\nimport { loadStripe, StripeConstructor } from '@stripe/stripe-js';\nimport { AuthService } from '../../../../core/services/auth.service';\n\ndeclare global {\n  interface Window {\n    Stripe?: StripeConstructor;\n  }\n}\n\n@Component({\n  selector: 'app-ride-payment',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatDividerModule\n  ],\n  template: `\n    <mat-card *ngIf=\"ride\">\n      <mat-card-header>\n        <mat-card-title>Ride Payment</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"payment-details\">\n          <div class=\"detail-row\">\n            <span class=\"label\">Pickup:</span>\n            <span class=\"value\">{{ ride.pickup_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Destination:</span>\n            <span class=\"value\">{{ ride.dropoff_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Date:</span>\n            <span class=\"value\">{{ ride.pickup_time | date:'medium' }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'status-' + ride.status\">{{ ride.status }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Payment Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'payment-' + (ride.payment_status || 'pending')\">\n              {{ ride.payment_status || 'pending' }}\n            </span>\n          </div>\n          <div class=\"detail-row amount\">\n            <span class=\"label\">Amount:</span>\n            <span class=\"value\">{{ '$' + getDisplayAmount() }}</span>\n          </div>\n        </div>\n\n        <mat-divider *ngIf=\"canPay()\" class=\"section-divider\"></mat-divider>\n\n        <div *ngIf=\"canPay() && !sdkLoaded\" class=\"sdk-status\">\n          <p>Loading payment form...</p>\n          <mat-spinner diameter=\"30\"></mat-spinner>\n        </div>\n\n        <div *ngIf=\"canPay() && sdkLoaded\" class=\"stripe-payment-form\">\n          <h3>Payment Information</h3>\n          <p class=\"payment-instruction\">Please enter your card details to complete the payment.</p>\n\n          <div #cardElement class=\"card-element\"></div>\n          <div class=\"card-errors\" *ngIf=\"cardError\">{{ cardError }}</div>\n\n          <div class=\"payment-actions\">\n            <button mat-raised-button color=\"primary\"\n                    [disabled]=\"processing || !cardComplete\"\n                    (click)=\"processPayment()\">\n              <mat-icon>payment</mat-icon>\n              {{ processing ? 'Processing...' : 'Pay Now' }}\n            </button>\n\n            <div *ngIf=\"processing\" class=\"processing-indicator\">\n              <mat-spinner diameter=\"24\"></mat-spinner>\n              <span>Processing your payment...</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"payment-actions\" *ngIf=\"canRequestRefund()\">\n          <button mat-raised-button color=\"warn\"\n                  [disabled]=\"processing\"\n                  (click)=\"requestRefund()\">\n            <mat-icon>money_off</mat-icon>\n            {{ processing ? 'Processing...' : 'Request Refund' }}\n          </button>\n\n          <div *ngIf=\"processing\" class=\"processing-indicator\">\n            <mat-spinner diameter=\"24\"></mat-spinner>\n            <span>Processing your request...</span>\n          </div>\n        </div>\n\n        <div class=\"payment-result\" *ngIf=\"paymentResult\">\n          <h3>Payment Result</h3>\n          <div [ngClass]=\"paymentResult.success ? 'success-message' : 'error-message'\">\n            {{ paymentResult.success ? 'Payment successful!' : 'Payment failed: ' + paymentResult.error?.message }}\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [`\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    .payment-details {\n      margin-bottom: 20px;\n    }\n\n    .detail-row {\n      display: flex;\n      margin-bottom: 8px;\n      align-items: center;\n    }\n\n    .label {\n      font-weight: 500;\n      width: 120px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .value {\n      flex: 1;\n    }\n\n    .amount {\n      font-size: 1.2em;\n      font-weight: 500;\n      margin-top: 16px;\n    }\n\n    .amount .value {\n      color: #3f51b5;\n    }\n\n    .status-badge {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 4px;\n      text-transform: capitalize;\n      font-size: 0.9em;\n    }\n\n    .status-requested {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n      color: white;\n    }\n\n    .status-in-progress {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .payment-paid, .payment-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .payment-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-refunded {\n      background-color: #9e9e9e;\n      color: white;\n    }\n\n    .payment-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      align-items: flex-start;\n      margin-top: 16px;\n    }\n\n    .processing-indicator {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 8px;\n    }\n\n    .section-divider {\n      margin: 24px 0;\n    }\n\n    .stripe-payment-form {\n      margin-top: 24px;\n    }\n\n    .payment-instruction {\n      margin-bottom: 16px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .card-element {\n      border: 1px solid #e0e0e0;\n      border-radius: 4px;\n      padding: 12px;\n      background-color: white;\n      margin-bottom: 16px;\n    }\n\n    .card-errors {\n      color: #f44336;\n      font-size: 0.9em;\n      margin-bottom: 16px;\n    }\n\n    .sdk-status {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 16px;\n      margin: 24px 0;\n    }\n\n    .payment-result {\n      margin-top: 24px;\n      padding: 16px;\n      border-radius: 4px;\n      background-color: #f5f5f5;\n    }\n\n    .success-message {\n      color: #4caf50;\n      font-weight: 500;\n    }\n\n    .error-message {\n      color: #f44336;\n      font-weight: 500;\n    }\n  `]\n})\nexport class RidePaymentComponent implements OnInit, AfterViewInit {\n  @Input() ride!: Ride;\n  @ViewChild('cardElement') cardElement!: ElementRef;\n  @Output() paymentCompleted = new EventEmitter<void>();\n\n  processing = false;\n  stripe: any;\n  card: any;\n  sdkLoaded = false;\n  cardError = '';\n  cardComplete = false;\n  paymentResult: any = null;\n\n  constructor(\n    private paymentService: PaymentService,\n    private rideService: RideService,\n    private snackBar: MatSnackBar,\n    private authService: AuthService\n  ) {\n\n  }\n\n\n  async ngOnInit() {\n    // If the ride doesn't have an amount, calculate it\n    if (this.ride && !this.ride.amount && !this.ride.fare) {\n      this.estimateFare();\n\n    }\n\n    // Load Stripe SDK if the ride can be paid\n\n      this.loadStripeScript();\n     const stripe = await this._loadStripe(environment.stripePublishableKey);\n  }\n\n  ngAfterViewInit(): void {\n    // Card element will be initialized after Stripe script is loaded\n    // and the view is initialized\n\n      this.initializeCard();\n\n  }\n  async _loadStripe(key:string){\n\n    const stripe = await loadStripe(key);\n    this.sdkLoaded = true;\n  }\n  loadStripeScript(): void {\n    if (window.Stripe) {\n      this.initializeStripe();\n\n      return;\n    }\n\n    const script = document.createElement('script');\n    script.src = 'https://js.stripe.com/v3/';\n    script.async = true;\n    script.onload = () => {\n      this.initializeStripe();\n    };\n    document.body.appendChild(script);\n  }\n\n  initializeStripe(): void {\n    if (!window.Stripe) {\n      this.snackBar.open('Stripe SDK not available', 'Close', { duration: 3000 });\n      return;\n    }\n\n    try {\n      // Initialize Stripe with the publishable key from environment\n      this.stripe = window.Stripe(environment.stripePublishableKey);\n\n      // Initialize card element after a short delay to ensure DOM is ready\n      setTimeout(() => this.initializeCard(), 100);\n    } catch (error) {\n      console.error('Error initializing Stripe:', error);\n      this.snackBar.open('Error initializing Stripe payments. Check your credentials.', 'Close', { duration: 5000 });\n    }\n  }\n\n  initializeCard(): void {\n    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {\n      setTimeout(() => this.initializeCard(), 100);\n      return;\n    }\n\n    try {\n      const elements = this.stripe.elements();\n\n      // Create card element\n      this.card = elements.create('card', {\n        style: {\n          base: {\n            iconColor: '#666EE8',\n            color: '#31325F',\n            fontWeight: 400,\n            fontFamily: '\"Helvetica Neue\", Helvetica, sans-serif',\n            fontSize: '16px',\n            '::placeholder': {\n              color: '#CFD7E0'\n            }\n          }\n        }\n      });\n\n      // Mount the card element\n      this.card.mount(this.cardElement.nativeElement);\n\n      // Handle card element errors\n      this.card.on('change', (event: any) => {\n        this.cardError = event.error ? event.error.message : '';\n        this.cardComplete = event.complete;\n      });\n\n      this.sdkLoaded = true;\n    } catch (error) {\n      console.error('Error initializing Stripe card:', error);\n      this.snackBar.open('Error initializing Stripe card form', 'Close', { duration: 5000 });\n    }\n  }\n\n  calculateAmount(): number {\n    // Default amount if no calculation is available\n    return 15.00;\n  }\n\n  async estimateFare(): Promise<void> {\n    if (!this.ride) return;\n\n    try {\n      const {fare} = await this.paymentService.estimateFare(\n        this.ride.pickup_location,\n        this.ride.dropoff_location\n      );\n\n      // Update the ride with the estimated fare\n      await this.rideService.updateRide(this.ride.id, { fare });\n    } catch (error) {\n      console.error('Error estimating fare:', error);\n    }\n  }\n\n  canPay(): boolean {\n    if (!this.ride) return false;\n\n    // Can pay if the ride is completed and payment is pending\n    return (\n      this.ride.status === 'completed' &&\n      (!this.ride.payment_status || this.ride.payment_status === 'pending' || this.ride.payment_status === 'failed')\n    );\n  }\n\n  canRequestRefund(): boolean {\n    if (!this.ride) return false;\n\n    // Can request refund if the payment status is paid or completed\n    return this.ride.payment_status === 'paid' || this.ride.payment_status === 'completed';\n  }\n\n  async processPayment(): Promise<void> {\n    if (!this.ride || !this.canPay() || !this.stripe || !this.card) return;\n\n    this.processing = true;\n    this.paymentResult = null;\n\n    try {\n      const amount = this.ride.amount || this.ride.fare || this.calculateAmount();\n\n      // Create a payment intent\n      // const { clientSecret } = await this.paymentService.createPaymentIntent(\n      //   this.ride.id,\n      //   amount\n      // );\n\n      // Create a payment method with the card details\n      const { paymentMethod, error: paymentMethodError } = await this.stripe.createPaymentMethod({\n        type: 'card',\n        card: this.card,\n      });\n\n      if (paymentMethodError) {\n        throw paymentMethodError;\n      }\n    if (paymentMethodError) {\n        throw paymentMethodError;\n      }\n        let payment = {\n        amount: amount * 100, // Stripe uses cents\n        currency: 'usd',\n        description: \"Customer pamyment for ride\",\n        payment_method: paymentMethod.id\n      };\n        console.log(payment)\n      // Step 2: Create a payment intent using Supabase Stripe edge function\n      const { data, error } = await this.authService.supabase.functions.invoke('stripe', {\n        body:payment });\n\n      if (error) {\n        console.error('Error creating payment intent:', error);\n        throw new Error(`Failed to create payment intent: ${error.message}`);\n      }\n        console.log('Payment intent created:', data);\n\n      if (!data || !data.client_secret) {\n        throw new Error('No client secret returned from payment intent creation');\n      }\n\n      const clientSecret = data.client_secret;\n\n      // Step 3: Confirm the payment with the client secret\n      const { error: confirmError, paymentIntent } = await this.stripe.confirmCardPayment(clientSecret, {\n        payment_method: paymentMethod.id\n      });\n\n      if (confirmError) {\n        throw confirmError;\n      }\n\n      // Payment succeeded\n      this.paymentResult = {\n        success: true,\n        paymentIntent: paymentIntent\n      };\n\n      // Update the ride with payment details\n      await this.rideService.updateRide(this.ride.id, {\n        payment_status: 'paid',\n        payment_id: paymentIntent.id,\n        amount: amount\n      });\n\n      this.snackBar.open('Payment processed successfully!', 'Close', { duration: 3000 });\n\n      // Emit event to notify parent component that payment is completed\n      this.paymentCompleted.emit();\n\n      // Realtime subscription will handle the ride update automatically\n      // const updatedRide = await this.rideService.getRide(this.ride.id);\n      // if (updatedRide) {\n      //   this.ride = updatedRide;\n      // }\n\n\n    } catch (error: any) {\n      console.error('Error processing payment:', error);\n\n      this.paymentResult = {\n        success: false,\n        error: {\n          message: error.message || 'An unknown error occurred'\n        }\n      };\n\n      this.snackBar.open(`Payment error: ${error.message}`, 'Close', { duration: 5000 });\n    } finally {\n      this.processing = false;\n    }\n  }\n\n  getDisplayAmount(): string {\n    if (!this.ride) return '0';\n    return (this.ride.amount || this.ride.fare || this.calculateAmount()).toString();\n  }\n\n  async requestRefund(): Promise<void> {\n    if (!this.ride || !this.canRequestRefund()) return;\n\n    this.processing = true;\n    this.paymentResult = null;\n\n    try {\n      // Process the refund\n      const success = await this.paymentService.processRefund(this.ride.id);\n\n      if (success) {\n        this.paymentResult = {\n          success: true,\n          refund: true\n        };\n\n        this.snackBar.open('Refund processed successfully!', 'Close', { duration: 3000 });\n\n        // Emit event to notify parent component that refund is completed\n        this.paymentCompleted.emit();\n\n        // Realtime subscription will handle the ride update automatically\n        // const updatedRide = await this.rideService.getRide(this.ride.id);\n        // if (updatedRide) {\n        //   this.ride = updatedRide;\n        // }\n      } else {\n        this.paymentResult = {\n          success: false,\n          refund: true,\n          error: {\n            message: 'Failed to process refund'\n          }\n        };\n\n        this.snackBar.open('Refund request failed. Please try again.', 'Close', { duration: 3000 });\n      }\n    } catch (error: any) {\n      console.error('Error processing refund:', error);\n\n      this.paymentResult = {\n        success: false,\n        refund: true,\n        error: {\n          message: error.message || 'An unknown error occurred'\n        }\n      };\n\n      this.snackBar.open(`Refund error: ${error.message}`, 'Close', { duration: 5000 });\n    } finally {\n      this.processing = false;\n    }\n  }\n}\n", "import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { RideRequestComponent } from './ride-request/ride-request.component';\nimport { PaymentMethodsComponent } from './payment-methods/payment-methods.component';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { RideService } from '../../../core/services/ride.service';\nimport { PaymentService } from '../../../core/services/payment.service';\n\nimport { Ride, RideStatus } from '../../../core/models/ride.model';\nimport { User } from '../../../core/models/user.model';\nimport { Subscription } from 'rxjs';\nimport { MessageService } from '../../../core/services/message.service';\nimport { RidePaymentComponent } from './ride-payment/ride-payment.component';\nimport { RideDetailComponent } from '../../../shared/components/ride-detail/ride-detail.component';\n\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatDialogModule, MatDialog } from '@angular/material/dialog';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-rider',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatTabsModule,\n    MatTableModule,\n    MatChipsModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSnackBarModule,\n    MatTooltipModule,\n    MatDialogModule,\n    MatExpansionModule,\n    RideRequestComponent,\n    PaymentMethodsComponent,\n    RidePaymentComponent,\n    RideDetailComponent,\n  ],\n  template: `\n    <div class=\"dashboard-container\">\n      <mat-tab-group>\n        <mat-tab label=\"Request Ride\">\n          <app-ride-request></app-ride-request>\n        </mat-tab>\n\n        <!-- <mat-tab label=\"Payment Methods\">\n          <app-payment-methods></app-payment-methods>\n        </mat-tab> -->\n\n        <mat-tab label=\"Ride History\">\n          <div class=\"table-container\">\n            <!-- Desktop View -->\n            <div class=\"desktop-view\">\n              <table mat-table [dataSource]=\"rides\" class=\"ride-table\">\n                <ng-container matColumnDef=\"pickup_location\">\n                  <th mat-header-cell *matHeaderCellDef>Pickup</th>\n                  <td mat-cell *matCellDef=\"let ride\">{{ride.pickup_location}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"dropoff_location\">\n                  <th mat-header-cell *matHeaderCellDef>Dropoff</th>\n                  <td mat-cell *matCellDef=\"let ride\">{{ride.dropoff_location}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"pickup_time\">\n                  <th mat-header-cell *matHeaderCellDef>Time</th>\n                  <td mat-cell *matCellDef=\"let ride\">{{formatDate(ride.pickup_time)}}</td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"status\">\n                  <th mat-header-cell *matHeaderCellDef>Status</th>\n                  <td mat-cell *matCellDef=\"let ride\">\n                    <mat-chip-listbox>\n                      <mat-chip [class]=\"getStatusClass(ride.status)\">\n                        {{formatStatus(ride.status)}}\n                      </mat-chip>\n                    </mat-chip-listbox>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"payment_status\">\n                  <th mat-header-cell *matHeaderCellDef>Payment</th>\n                  <td mat-cell *matCellDef=\"let ride\">\n                    <mat-chip-listbox *ngIf=\"ride.payment_status\">\n                      <mat-chip [class]=\"'payment-status-' + ride.payment_status\">\n                        {{ride.payment_status}}\n                      </mat-chip>\n                    </mat-chip-listbox>\n                    <span *ngIf=\"!ride.payment_status\">-</span>\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"fare\">\n                  <th mat-header-cell *matHeaderCellDef>Fare</th>\n                  <td mat-cell *matCellDef=\"let ride\">\n                    {{(ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare).toFixed(2) : '-'}}\n                  </td>\n                </ng-container>\n\n                <ng-container matColumnDef=\"actions\">\n                  <th mat-header-cell *matHeaderCellDef>Actions</th>\n                  <td mat-cell *matCellDef=\"let ride\">\n                    <button mat-icon-button color=\"warn\" *ngIf=\"ride.status === 'requested'\"\n                            (click)=\"cancelRide(ride.id)\" matTooltip=\"Cancel Ride\">\n                      <mat-icon>cancel</mat-icon>\n                    </button>\n                    <!-- <button mat-icon-button color=\"primary\" *ngIf=\"ride.driver_id\"\n                            (click)=\"openChat(ride.id)\" matTooltip=\"Message Driver\">\n                      <mat-icon>chat</mat-icon>\n                    </button> -->\n                    <button mat-icon-button color=\"accent\" *ngIf=\"ride.status === 'completed'\"\n                            (click)=\"viewPayment(ride)\" matTooltip=\"View Payment\">\n                      <mat-icon>payment</mat-icon>\n                    </button>\n                    <button mat-icon-button color=\"primary\"\n                            (click)=\"viewRideDetails(ride.id)\" matTooltip=\"View Details\">\n                      <mat-icon>visibility</mat-icon>\n                    </button>\n                  </td>\n                </ng-container>\n\n                <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n                <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\n              </table>\n            </div>\n\n            <!-- Mobile View -->\n            <div class=\"mobile-view\">\n              <mat-accordion multi>\n                <mat-expansion-panel *ngFor=\"let ride of rides; trackBy: trackByRideId\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      {{ ride.pickup_location }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      <div class=\"ride-actions-header\">\n                        <span class=\"ride-time\">{{ ride.pickup_time | date:'shortTime' }}</span>\n                        <button mat-icon-button color=\"warn\" *ngIf=\"ride.status === 'requested'\"\n                                (click)=\"cancelRide(ride.id); $event.stopPropagation()\" matTooltip=\"Cancel Ride\">\n                          <mat-icon>cancel</mat-icon>\n                        </button>\n                        <button mat-icon-button color=\"accent\" *ngIf=\"ride.status === 'completed'\"\n                                (click)=\"viewPayment(ride); $event.stopPropagation()\" matTooltip=\"View Payment\">\n                          <mat-icon>payment</mat-icon>\n                        </button>\n                      </div>\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n                  <div class=\"ride-details\">\n                    <p><strong>To:</strong> {{ ride.dropoff_location }}</p>\n                    <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>\n                    <p><strong>Status:</strong> {{ formatStatus(ride.status) }}</p>\n                    <p><strong>Payment:</strong>\n                      <span *ngIf=\"ride.payment_status\">{{ ride.payment_status }}</span>\n                      <span *ngIf=\"!ride.payment_status\">-</span>\n                    </p>\n                    <p><strong>Fare:</strong> {{ (ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare)!.toFixed(2) : '-' }}</p>\n                  </div>\n                  <mat-action-row>\n                    <button mat-icon-button color=\"primary\" (click)=\"viewRideDetails(ride.id)\" matTooltip=\"View Details\">\n                      <mat-icon>visibility</mat-icon>\n                    </button>\n                  </mat-action-row>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n\n    <div *ngIf=\"selectedRide\" class=\"payment-overlay\">\n      <app-ride-payment\n        [ride]=\"selectedRide\"\n        (paymentCompleted)=\"closePayment()\">\n      </app-ride-payment>\n      <button mat-icon-button class=\"close-payment-button\" (click)=\"closePayment()\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n\n    <div *ngIf=\"selectedRideId\" class=\"ride-detail-overlay\">\n      <app-ride-detail\n        [rideId]=\"selectedRideId\"\n        [onClose]=\"closeRideDetails.bind(this)\"\n        (paymentRequested)=\"viewPayment($event)\"\n        (rideUpdated)=\"onRideUpdated($event)\">\n      </app-ride-detail>\n    </div>\n  `,\n  styles: [`\n    .dashboard-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n        background-color: #f5f5f5;\n    }\n\n    .table-container {\n      margin: 20px;\n    }\n\n    .ride-table {\n      width: 100%;\n    }\n\n    .status-chip {\n      border-radius: 16px;\n      padding: 4px 12px;\n      color: white;\n      font-weight: 500;\n    }\n\n    .status-requested {\n      background-color: #ff9800;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n    }\n\n    .status-in-progress {\n      background-color: #673ab7;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n    }\n\n    .payment-status-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .payment-status-paid {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .payment-status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-status-refunded {\n      background-color: #9e9e9e;\n      color: white;\n    }\n\n    .payment-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .close-payment-button {\n      position: absolute;\n      top: 20px;\n      right: 20px;\n      background-color: white;\n    }\n\n    .ride-detail-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    /* Mobile View Styles */\n    .mobile-view {\n      display: none;\n    }\n\n    .desktop-view {\n      display: block;\n    }\n\n    .ride-actions-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .ride-time {\n      font-size: 12px;\n      color: #666;\n    }\n\n    @media (max-width: 600px) {\n      .desktop-view {\n        display: none;\n      }\n      .mobile-view {\n        display: block;\n      }\n      .dashboard-container {\n        padding: 0;\n      }\n      .table-container {\n        margin: 0;\n      }\n      .mat-tab-body-content {\n        overflow: hidden;\n      }\n    }\n\n    .mobile-view .mat-expansion-panel {\n      margin: 8px 0;\n    }\n    .mobile-view .mat-expansion-panel-header {\n      font-size: 14px;\n    }\n    .mobile-view .mat-panel-title {\n      font-weight: 500;\n    }\n    .mobile-view .mat-panel-description {\n      justify-content: flex-end;\n      align-items: center;\n    }\n    .mobile-view .ride-details {\n      padding: 0 24px 16px;\n      font-size: 14px;\n    }\n    .mobile-view .ride-details p {\n      margin: 4px 0;\n    }\n    .mobile-view .mat-action-row {\n      justify-content: flex-end;\n      padding: 8px 12px 8px 24px;\n    }\n  `]\n})\nexport class RiderComponent implements OnInit, OnDestroy {\n  rides: Ride[] = [];\n  displayedColumns = ['pickup_location', 'dropoff_location', 'pickup_time', 'status', 'payment_status', 'fare', 'actions'];\n  currentUser: User | null = null;\n  selectedRide: Ride | null = null;\n  selectedRideId: string | null = null;\n  private ridesSubscription: Subscription | null = null;\n\n  constructor(\n    private authService: AuthService,\n    private rideService: RideService,\n    private messageService: MessageService,\n    private paymentService: PaymentService,\n    private router: Router,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  async ngOnInit() {\n    this.currentUser = await this.authService.getCurrentUser();\n    if (this.currentUser) {\n      // Initial load of rides\n      await this.loadUserRides();\n\n      // Subscribe to ride updates\n      this.ridesSubscription = this.rideService.rides$.subscribe((rides: any[]) => {\n        if (this.currentUser) {\n          this.rides = rides.filter(ride => ride.rider_id === this.currentUser!.id);\n        }\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up subscription when component is destroyed\n    if (this.ridesSubscription) {\n      this.ridesSubscription.unsubscribe();\n    }\n  }\n\n  async loadUserRides() {\n    if (this.currentUser) {\n      try {\n        this.rides = await this.rideService.getUserRides(this.currentUser.id);\n      } catch (error) {\n        console.error('Error loading user rides:', error);\n        this.snackBar.open('Failed to load rides', 'Close', { duration: 3000 });\n      }\n    }\n  }\n\n  async cancelRide(rideId: string) {\n    try {\n      const success = await this.rideService.cancelRide(rideId);\n      if (success) {\n        this.snackBar.open('Ride canceled successfully', 'Close', { duration: 3000 });\n        await this.loadUserRides();\n      } else {\n        throw new Error('Failed to cancel ride');\n      }\n    } catch (error) {\n      console.error('Error canceling ride:', error);\n      this.snackBar.open('Failed to cancel ride', 'Close', { duration: 3000 });\n    }\n  }\n\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleString();\n  }\n\n  formatStatus(status: RideStatus): string {\n    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n  }\n\n  getStatusClass(status: RideStatus): string {\n    return `status-chip status-${status}`;\n  }\n\n  async openChat(rideId: string) {\n    try {\n      // Navigate to the messages page with this ride's thread\n      const thread = await this.messageService.getOrCreateThreadForRide(rideId);\n      this.router.navigate(['/dashboard', 'rider', 'messages', thread.id]);\n    } catch (error) {\n      console.error('Error opening chat:', error);\n      this.snackBar.open('Failed to open chat', 'Close', { duration: 3000 });\n    }\n  }\n\n  viewPayment(ride: Ride) {\n    this.selectedRide = ride;\n  }\n\n  closePayment() {\n    this.selectedRide = null;\n    // Refresh rides to get updated payment status\n    this.loadUserRides();\n  }\n\n  viewRideDetails(rideId: string) {\n    this.selectedRideId = rideId;\n  }\n\n  closeRideDetails() {\n    this.selectedRideId = null;\n    // Refresh rides to get updated ratings\n    this.loadUserRides();\n  }\n\n  onRideUpdated(_ride: Ride) {\n    // Reload rides to reflect the updated data\n    this.loadUserRides();\n  }\n\n  trackByRideId(_index: number, item: Ride): string {\n    return item.id;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDc,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,+BAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,gCAAA;AACF,IAAA,uBAAA;;;;;AAYA,IAAA,yBAAA,GAAA,GAAA;AAA6B,IAAA,iBAAA,CAAA;AAAqC,IAAA,uBAAA;;;;AAArC,IAAA,oBAAA;AAAA,IAAA,6BAAA,cAAA,OAAA,mBAAA,QAAA;;;;;AAC7B,IAAA,yBAAA,GAAA,GAAA;AAA6B,IAAA,iBAAA,CAAA;AAAuC,IAAA,uBAAA;;;;AAAvC,IAAA,oBAAA;AAAA,IAAA,6BAAA,cAAA,OAAA,mBAAA,UAAA;;;;;AAH/B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,GAAA;AAC5C,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,yBAAA,GAAA,QAAA;AAAQ,IAAA,iBAAA,CAAA;AAAuD,IAAA,uBAAA,EAAS;AAC3F,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,KAAA,CAAA,EAA6B,GAAA,gDAAA,GAAA,GAAA,KAAA,CAAA;AAE/B,IAAA,uBAAA;;;;AAH6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,MAAA,OAAA,cAAA,QAAA,CAAA,IAAA,EAAA;AACvB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA;;;;;AATR,IAAA,yBAAA,GAAA,KAAA;AACE,IAAA,oBAAA,GAAA,mBAAA,EAAA;AAKA,IAAA,qBAAA,GAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA;;;;;;AATI,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,UAAA,OAAA,SAAA,IAAA,iBAAA,MAAA,OAAA,OAAA,QAAA,KAAA,EAAiD,gBAAA,UAAA,OAAA,SAAA,IAAA,kBAAA,MAAA,OAAA,OAAA,QAAA,KAAA;AAI7C,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA;;;;;AA0BN,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;;;AAUA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;AA4DN,IAAO,uBAAP,MAAO,sBAAoB;EAcrB;EACA;EACA;EACA;EACA;EACA;EAlBV;EACA,UAAU;EACV,UAAU;EACV,gBAA+B;EAC/B,oBAAmC;EACnC,oBAAmC;EAE3B,sBAGJ,CAAA;EAEJ,YACU,aACA,aACA,aACA,iBACA,gBACA,UAAqB;AALrB,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,kBAAA;AACA,SAAA,iBAAA;AACA,SAAA,WAAA;AAER,SAAK,WAAW,KAAK,YAAY,MAAM;MACrC,iBAAiB,CAAC,IAAI,WAAW,QAAQ;MACzC,kBAAkB,CAAC,IAAI,WAAW,QAAQ;;MAE1C,aAAa,CAAC,oBAAI,KAAI,GAAI,WAAW,QAAQ;MAC7C,aAAa,CAAC,YAAY,WAAW,QAAQ;;KAE9C;EACH;EAEA,WAAQ;AAEN,SAAK,SAAS,IAAI,iBAAiB,GAAG,aAAa,UAAU,MAAK;AAChE,WAAK,qBAAoB;IAC3B,CAAC;AAED,SAAK,SAAS,IAAI,kBAAkB,GAAG,aAAa,UAAU,MAAK;AACjE,WAAK,qBAAoB;IAC3B,CAAC;EACH;EAEM,qBAAkB;;AACtB,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,gBAAgB,mBAAkB;AAI5D,aAAK,SAAS,WAAW;UACvB,iBAAiB,qBAAqB,OAAO,SAAS,QAAQ,CAAC,CAAC,KAAK,OAAO,UAAU,QAAQ,CAAC,CAAC;SACjG;AAED,aAAK,oBAAoB,SAAS;AAClC,aAAK,SAAS,KAAK,6BAA6B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC7E,SAAS,OAAY;AACnB,aAAK,SAAS,KAAK,MAAM,WAAW,kCAAkC,SAAS,EAAE,UAAU,IAAI,CAAE;MACnG;IACF;;EAEM,uBAAoB;;AACxB,YAAM,SAAS,KAAK,SAAS,IAAI,iBAAiB,GAAG;AACrD,YAAM,UAAU,KAAK,SAAS,IAAI,kBAAkB,GAAG;AAEvD,UAAI,UAAU,SAAS;AACrB,aAAK,UAAU;AAEf,YAAI;AAGH,gBAAM,EAAC,MAAM,UAAS,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,OAAO;AAC7E,eAAK,gBAAgB;AACrB,eAAK,oBAAoB,UAAU;AACnC,eAAK,oBAAoB,UAAU;QAIvC,SAAS,OAAO;AACd,kBAAQ,MAAM,4BAA4B,KAAK;QACjD;MACF,OAAO;AACL,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AACzB,aAAK,oBAAoB;MAC3B;IACF;;EAEM,WAAQ;;AACZ,UAAI,KAAK,SAAS;AAAS;AAE3B,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,YAAY,eAAc;AAClD,YAAI,CAAC;AAAM,gBAAM,IAAI,MAAM,gBAAgB;AAG3C,YAAI,CAAC,KAAK,oBAAoB,QAAQ;AACpC,eAAK,oBAAoB,SAAS,MAAM,KAAK,gBAAgB,eAAe,KAAK,SAAS,MAAM,eAAe;QACjH;AAEA,YAAI,CAAC,KAAK,oBAAoB,SAAS;AACrC,eAAK,oBAAoB,UAAU,MAAM,KAAK,gBAAgB,eAAe,KAAK,SAAS,MAAM,gBAAgB;QACnH;AAGA,cAAM,YAAY,MAAM,KAAK,gBAAgB,eAC3C,KAAK,oBAAoB,QACzB,KAAK,oBAAoB,OAAO;AAElC,gBAAQ,IAAI,SAAS;AAGrB,cAAM,aAAa,KAAK,SAAS,MAAM;AACvC,cAAM,aAAa,KAAK,SAAS,MAAM;AAGvC,cAAM,mBAAmB,IAAI,KAAK,UAAU;AAG5C,cAAM,YAAY,WAAW,MAAM,yBAAyB;AAC5D,YAAI,WAAW;AACb,cAAI,QAAQ,SAAS,UAAU,CAAC,GAAG,EAAE;AACrC,gBAAM,UAAU,SAAS,UAAU,CAAC,GAAG,EAAE;AACzC,gBAAM,SAAS,UAAU,CAAC,IAAI,UAAU,CAAC,EAAE,YAAW,IAAK;AAG3D,cAAI,WAAW,QAAQ,QAAQ,IAAI;AACjC,qBAAS;UACX,WAAW,WAAW,QAAQ,UAAU,IAAI;AAC1C,oBAAQ;UACV;AAEA,2BAAiB,SAAS,OAAO,SAAS,GAAG,CAAC;QAChD;AAEA,cAAM,OAAO,iCACR,KAAK,SAAS,QADN;UAEX,UAAU,KAAK;UACf,QAAQ;UACR,aAAa,iBAAiB,YAAW;;UAEzC,iBAAiB,KAAK,oBAAoB,QAAQ;UAClD,kBAAkB,KAAK,oBAAoB,QAAQ;UACnD,kBAAkB,KAAK,oBAAoB,SAAS;UACpD,mBAAmB,KAAK,oBAAoB,SAAS;;UAErD,gBAAgB,UAAU;UAC1B,kBAAkB,UAAU;;UAE5B,MAAM,KAAK,kBAAiB,MAAM,KAAK,eAAe,aACpD,KAAK,SAAS,MAAM,iBACpB,KAAK,SAAS,MAAM,gBAAgB;;AAIxC,cAAM,KAAK,YAAY,WAAW,IAAI;AAEtC,aAAK,SAAS,KAAK,gCAAgC,SAAS,EAAE,UAAU,IAAI,CAAE;AAC9E,aAAK,SAAS,MAAM;UAClB,YAAY;UACZ,aAAa,oBAAI,KAAI;UACrB,aAAa;SACd;AAGD,aAAK,UAAU;AACf,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AACzB,aAAK,oBAAoB;AACzB,aAAK,sBAAsB,CAAA;MAC7B,SAAS,OAAY;AACnB,aAAK,SAAS,KAAK,MAAM,WAAW,0BAA0B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC3F;AACE,aAAK,UAAU;MACjB;IACF;;;qCAhLW,uBAAoB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,eAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,UAAA,EAAA,GAAA,CAAA,cAAA,EAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,mBAAA,eAAA,uBAAA,GAAA,CAAA,mBAAA,IAAA,aAAA,IAAA,QAAA,UAAA,SAAA,wBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,oBAAA,eAAA,wBAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,eAAA,GAAA,eAAA,GAAA,CAAA,aAAA,IAAA,GAAA,KAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,eAAA,GAAA,kBAAA,GAAA,CAAA,8BAAA,EAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,GAAA,CAAA,GAAA,UAAA,aAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AArI7B,MAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA,EAAiB;AAEjD,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,QAAA,CAAA;AACa,MAAA,qBAAA,YAAA,SAAA,yDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAY,IAAA,SAAA,CAAU;MAAA,CAAA;AACjD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,kBAAA,CAAA,EACU,GAAA,WAAA;AACxB,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA;AAC1B,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAgD,MAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,mBAAA,CAAoB;MAAA,CAAA;AAC3E,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAW;AAElC,MAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC3B,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA,EAAiB;AAGnB,MAAA,qBAAA,IAAA,sCAAA,GAAA,GAAA,OAAA,CAAA;AA2BA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACtB,MAAA,oBAAA,IAAA,SAAA,CAAA,EAAuE,IAAA,yBAAA,EAAA,EACC,IAAA,kBAAA,MAAA,CAAA;AAExE,MAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACtB,MAAA,oBAAA,IAAA,SAAA,EAAA;AACA,MAAA,yBAAA,IAAA,6BAAA,EAAA,EAAwD,IAAA,YAAA,EAAA;AACjB,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA,EAAW;AAErE,MAAA,oBAAA,IAAA,sBAAA,MAAA,CAAA;AACA,MAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA;AAOA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA8B,IAAA,UAAA,EAAA;AAE1B,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD,EACU;;;;;;;;;;AAlFX,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,QAAA;AAQY,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,SAAA,IAAA,iBAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,SAAA,IAAA,kBAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAMV,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,UAAA,IAAA,SAAA,IAAA,iBAAA,MAAA,OAAA,OAAA,QAAA,YAAA,UAAA,IAAA,SAAA,IAAA,kBAAA,MAAA,OAAA,OAAA,QAAA,MAAA;AA6BY,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,iBAAA,SAAA;AACiB,MAAA,oBAAA;AAAA,MAAA,qBAAA,OAAA,SAAA;AAErB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,SAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAOI,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,oBAAA,aAAA;AACqB,MAAA,oBAAA;AAAA,MAAA,qBAAA,OAAA,aAAA;AAIzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,WAAA,IAAA,SAAA,IAAA,aAAA,MAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,SAAA,OAAA,UAAA,CAAA;AAW4C,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,SAAA,WAAA,IAAA,OAAA;AACtD,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,UAAA,kBAAA,gBAAA,GAAA;;;IAhGV;IAAY;IACZ;IAAmB;IAAA;IAAA;IAAA;IAAA;IAAA;IACnB;IAAa;IAAA;IAAA;IAAA;IACb;IAAkB;IAAA;IAAA;IAAA;IAClB;IAAc;IACd;IAAe;IAAA;IACf;IAAmB;IAAA;IAAA;IACnB;IACA;IAAa;IACb;IACA;IAAsB;IAAA;IAAA;IAAA;EAAA,GAAA,QAAA,CAAA,gvBAAA,GAAA,iBAAA,EAAA,CAAA;;;sEAwIb,sBAAoB,CAAA;UAvJhC;uBACW,oBAAkB,YAChB,MAAI,iBACC,wBAAwB,QAAM,SACtC;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0FT,QAAA,CAAA,ozBAAA,EAAA,CAAA;;;;6EA4CU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,2EAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;AC7HzB,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,eAAA,CAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA,EAAI;;;;;AAIjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,GAAA;AACvD,IAAA,iBAAA,GAAA,yCAAA;AAAuC,IAAA,uBAAA,EAAI;;;;;AAUxC,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAqD,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAG5D,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,sGAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,YAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,wBAAA,UAAA,EAAA,CAAkC;IAAA,CAAA;AACjD,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA,EAAW;;;;;;AAXzC,IAAA,yBAAA,GAAA,eAAA,EAAqD,GAAA,OAAA,EAAA,EAClB,GAAA,OAAA,EAAA,EACE,GAAA,UAAA;AACrB,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAA0C,IAAA,uBAAA;AAChD,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAqB,IAAA,iBAAA,CAAA;AAAkD,IAAA,uBAAA;AACvE,IAAA,qBAAA,GAAA,0EAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,6EAAA,GAAA,GAAA,UAAA,EAAA;AAIA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAqC,IAAA,qBAAA,SAAA,SAAA,6FAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,UAAA,EAAA,CAA8B;IAAA,CAAA;AAC1E,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA,EAAW,EACpB,EACL,EACF;;;;AAbI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,UAAA,OAAA,8BAAA,UAAA,OAAA,EAAA;AACe,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,YAAA,UAAA,UAAA,KAAA,UAAA,SAAA,EAAA;AACd,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA;AAGkC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,UAAA,SAAA;;;;;AAVjD,IAAA,yBAAA,GAAA,UAAA;AACE,IAAA,qBAAA,GAAA,mEAAA,IAAA,GAAA,iBAAA,EAAA;AAmBF,IAAA,uBAAA;;;;AAnBoC,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA;;;;;AAqBpC,IAAA,oBAAA,GAAA,eAAA,EAAA;;;;;AAQM,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,uBAAA;AACF,IAAA,uBAAA;;;;;AAOE,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,gCAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,kCAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,+BAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,kBAAA,OAAA,aAAA,YAAA;;;;;AAQJ,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,mBAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;;AA3ER,IAAA,yBAAA,GAAA,KAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,OAAA,CAAA,EAA4D,GAAA,mDAAA,GAAA,GAAA,YAAA,CAAA,EAIhB,GAAA,sDAAA,GAAA,GAAA,eAAA,CAAA;AAwB5C,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,IAAA;AAC3B,IAAA,iBAAA,GAAA,wBAAA;AAAsB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,QAAA,CAAA;AAAgC,IAAA,qBAAA,YAAA,SAAA,kEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAC5D,IAAA,yBAAA,GAAA,kBAAA,CAAA,EAAqC,GAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACtB,IAAA,oBAAA,IAAA,SAAA,CAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA,EAAuE,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA;AAMzE,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,kBAAA,CAAA,EACiB,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAC3B,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA,EAAqE,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA;AAMvE,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AAC1B,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA,EAAoE,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA;AAMtE,IAAA,uBAAA,EAAiB;AAGnB,IAAA,yBAAA,IAAA,kBAAA,CAAA,EAAqC,IAAA,WAAA;AACxB,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AACd,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA,EAAgE,IAAA,qDAAA,GAAA,GAAA,aAAA,CAAA;AAMlE,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA8B,IAAA,UAAA,EAAA;AAE1B,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD,EACH;;;;;;;;;;;;AAnFA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,WAAA,CAAA;AAIK,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,SAAA,CAAA;AAsBG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,SAAA,CAAA;AAIN,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA;AAIU,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,YAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,SAAA,CAAA;AASE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,KAAA,QAAA,UAAA,OAAA,YAAA,IAAA,UAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,KAAA,EAAA;AAQA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,OAAA,YAAA,IAAA,SAAA,MAAA,OAAA,OAAA,QAAA,UAAA,OAAA,OAAA,QAAA,OAAA,UAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,WAAA,OAAA,YAAA,IAAA,SAAA,MAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,SAAA,OAAA,KAAA,CAAA;AASF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,WAAA,OAAA,YAAA,IAAA,KAAA,MAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,SAAA,OAAA,UAAA,CAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,WAAA,OAAA,YAAA,IAAA,KAAA,MAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,SAAA,OAAA,SAAA,CAAA;AAM4C,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,WAAA,OAAA,UAAA;AACtD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,cAAA,sBAAA,GAAA;;;AAyFZ,IAAO,0BAAP,MAAO,yBAAuB;EAQxB;EACA;EACA;EATV,iBAAkC,CAAA;EAClC;EACA,UAAU;EACV,aAAa;EACb,eAAc,oBAAI,KAAI,GAAG,YAAW;EAEpC,YACU,aACA,aACA,UAAqB;AAFrB,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,WAAA;AAER,SAAK,cAAc,KAAK,YAAY,MAAM;MACxC,YAAY,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,aAAa,CAAC,CAAC;MACzE,UAAU,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC;MAC3E,SAAS,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,IAAI,KAAK,WAAW,CAAC,CAAC;MACrE,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,cAAc,CAAC,CAAC;KACpE;EACH;EAEA,WAAQ;AACN,SAAK,mBAAkB;EACzB;EAEA,qBAAkB;AAChB,SAAK,UAAU;AAGf,eAAW,MAAK;AAEd,WAAK,iBAAiB;QACpB;UACE,IAAI;UACJ,OAAO;UACP,OAAO;UACP,UAAU;UACV,SAAS;UACT,WAAW;;QAEb;UACE,IAAI;UACJ,OAAO;UACP,OAAO;UACP,UAAU;UACV,SAAS;UACT,WAAW;;;AAGf,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEA,mBAAgB;AACd,QAAI,KAAK,YAAY;AAAS;AAE9B,SAAK,aAAa;AAGlB,eAAW,MAAK;AACd,YAAM,YAAY,KAAK,YAAY;AAGnC,YAAM,YAA2B;QAC/B,IAAI,MAAM,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC;QACpD,OAAO,KAAK,aAAa,UAAU,UAAU;QAC7C,OAAO,UAAU,WAAW,MAAM,EAAE;QACpC,UAAU,UAAU;QACpB,SAAS,UAAU;QACnB,WAAW,KAAK,eAAe,WAAW;;;AAG5C,WAAK,eAAe,KAAK,SAAS;AAClC,WAAK,YAAY,MAAK;AACtB,WAAK,aAAa;AAClB,WAAK,SAAS,KAAK,qCAAqC,SAAS,EAAE,UAAU,IAAI,CAAE;IACrF,GAAG,IAAI;EACT;EAEA,wBAAwB,IAAU;AAEhC,SAAK,iBAAiB,KAAK,eAAe,IAAI,YAAW,iCACpD,SADoD;MAEvD,WAAW,OAAO,OAAO;MACzB;AAEF,SAAK,SAAS,KAAK,kCAAkC,SAAS,EAAE,UAAU,IAAI,CAAE;EAClF;EAEA,oBAAoB,IAAU;AAC5B,UAAM,YAAY,KAAK,eAAe,KAAK,OAAK,EAAE,OAAO,EAAE,GAAG;AAG9D,SAAK,iBAAiB,KAAK,eAAe,OAAO,YAAU,OAAO,OAAO,EAAE;AAG3E,QAAI,aAAa,KAAK,eAAe,SAAS,GAAG;AAC/C,WAAK,eAAe,CAAC,EAAE,YAAY;IACrC;AAEA,SAAK,SAAS,KAAK,0BAA0B,SAAS,EAAE,UAAU,IAAI,CAAE;EAC1E;EAEQ,aAAa,YAAkB;AAErC,UAAM,aAAa,WAAW,OAAO,CAAC;AAEtC,YAAQ,YAAY;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;;qCAtHW,0BAAuB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,cAAA,SAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,cAAA,eAAA,qBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,YAAA,eAAA,MAAA,OAAA,KAAA,OAAA,IAAA,GAAA,CAAA,YAAA,IAAA,QAAA,UAAA,mBAAA,WAAA,eAAA,QAAA,OAAA,MAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,OAAA,eAAA,KAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,WAAA,cAAA,kBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,QAAA,cAAA,UAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,WAAA,cAAA,kBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAnLhC,MAAA,yBAAA,GAAA,UAAA,EAAU,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA,EAAiB;AAElD,MAAA,yBAAA,GAAA,kBAAA;AACE,MAAA,qBAAA,GAAA,wCAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,GAAA,wCAAA,IAAA,IAAA,OAAA,CAAA;AA2FjD,MAAA,uBAAA,EAAmB;;;AA3FX,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,OAAA;;oBAtBV,cAAY,SAAA,MACZ,qBAAmB,oBAAA,sBAAA,qBAAA,iBAAA,sBAAA,cAAA,cAAA,oBAAA,iBACnB,eAAa,SAAA,gBAAA,eAAA,cACb,oBAAkB,cAAA,UAAA,UAClB,gBAAc,UACd,iBAAe,WAAA,eACf,eAAa,SACb,kBAAgB,YAChB,eAAa,SAAA,aACb,0BAAwB,kBAAA,GAAA,QAAA,CAAA,24CAAA,EAAA,CAAA;;;sEAsLf,yBAAuB,CAAA;UAnMnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmGT,QAAA,CAAA,m2CAAA,EAAA,CAAA;;;;6EAiFU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,iFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;;ACpJ5B,IAAA,oBAAA,GAAA,eAAA,EAAA;;;;;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuD,GAAA,GAAA;AAClD,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA;AAC1B,IAAA,oBAAA,GAAA,eAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAOE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2C,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA;;;;AAAf,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,SAAA;;;;;AAUzC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,eAAA,EAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA,EAAO;;;;;;AAjB7C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+D,GAAA,IAAA;AACzD,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA+B,IAAA,iBAAA,GAAA,yDAAA;AAAuD,IAAA,uBAAA;AAEtF,IAAA,oBAAA,GAAA,OAAA,IAAA,CAAA;AACA,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,OAAA,EAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,UAAA,EAAA;AAGnB,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,CAAgB;IAAA,CAAA;AAC/B,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACjB,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,OAAA,EAAA;AAIF,IAAA,uBAAA,EAAM;;;;AAdoB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA;AAIhB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,cAAA,CAAA,OAAA,YAAA;AAGN,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,kBAAA,WAAA,GAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA;;;;;AAeR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,eAAA,EAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA,EAAO;;;;;;AAV3C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,UAAA,EAAA;AAG9C,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAC9B,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;AACnB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,OAAA,EAAA;AAIF,IAAA,uBAAA;;;;AAVU,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,UAAA;AAGN,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,kBAAA,kBAAA,GAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA;;;;;AAMR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkD,GAAA,IAAA;AAC5C,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA;AAClB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM;;;;AAFD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA,UAAA,oBAAA,eAAA;AACH,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,UAAA,wBAAA,sBAAA,OAAA,cAAA,SAAA,OAAA,OAAA,OAAA,cAAA,MAAA,UAAA,GAAA;;;;;AAhFR,IAAA,yBAAA,GAAA,UAAA,EAAuB,GAAA,iBAAA,EACJ,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA,EAAiB;AAE/C,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACa,GAAA,OAAA,CAAA,EACH,GAAA,QAAA,CAAA;AACF,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,QAAA,CAAA;AAAoB,IAAA,iBAAA,EAAA;AAA0B,IAAA,uBAAA,EAAO;AAEvD,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,QAAA,CAAA;AACF,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAChC,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAoB,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA,EAAO;AAExD,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,QAAA,CAAA;AACF,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAoB,IAAA,iBAAA,EAAA;;AAAsC,IAAA,uBAAA,EAAO;AAEnE,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,QAAA,CAAA;AACF,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAqE,IAAA,iBAAA,EAAA;AAAiB,IAAA,uBAAA,EAAO;AAE/F,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,QAAA,CAAA;AACF,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AACnC,IAAA,yBAAA,IAAA,QAAA,CAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAET,IAAA,yBAAA,IAAA,OAAA,CAAA,EAA+B,IAAA,QAAA,CAAA;AACT,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,QAAA,CAAA;AAAoB,IAAA,iBAAA,EAAA;AAA8B,IAAA,uBAAA,EAAO,EACrD;AAGR,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,eAAA,CAAA,EAAsD,IAAA,iDAAA,GAAA,GAAA,OAAA,CAAA,EAEC,IAAA,iDAAA,IAAA,GAAA,OAAA,EAAA,EAKQ,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA,EAsBP,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA;AAoB1D,IAAA,uBAAA,EAAmB;;;;AA3EO,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,eAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,gBAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,IAAA,OAAA,KAAA,aAAA,QAAA,CAAA;AAIa,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,OAAA,KAAA,MAAA;AAAoC,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,MAAA;AAIpC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,cAAA,OAAA,KAAA,kBAAA,UAAA;AAC/B,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,KAAA,kBAAA,WAAA,GAAA;AAKkB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,MAAA,OAAA,iBAAA,CAAA;AAIV,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,CAAA;AAER,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,KAAA,CAAA,OAAA,SAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,KAAA,OAAA,SAAA;AAsBwB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,CAAA;AAcD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA;;;AAsK/B,IAAO,uBAAP,MAAO,sBAAoB;EAcrB;EACA;EACA;EACA;EAhBD;EACiB;EAChB,mBAAmB,IAAI,aAAY;EAE7C,aAAa;EACb;EACA;EACA,YAAY;EACZ,YAAY;EACZ,eAAe;EACf,gBAAqB;EAErB,YACU,gBACA,aACA,UACA,aAAwB;AAHxB,SAAA,iBAAA;AACA,SAAA,cAAA;AACA,SAAA,WAAA;AACA,SAAA,cAAA;EAGV;EAGM,WAAQ;;AAEZ,UAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,KAAK,MAAM;AACrD,aAAK,aAAY;MAEnB;AAIE,WAAK,iBAAgB;AACtB,YAAM,SAAS,MAAM,KAAK,YAAY,YAAY,oBAAoB;IACzE;;EAEA,kBAAe;AAIX,SAAK,eAAc;EAEvB;EACM,YAAY,KAAU;;AAE1B,YAAM,SAAS,MAAM,WAAW,GAAG;AACnC,WAAK,YAAY;IACnB;;EACA,mBAAgB;AACd,QAAI,OAAO,QAAQ;AACjB,WAAK,iBAAgB;AAErB;IACF;AAEA,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,WAAO,MAAM;AACb,WAAO,QAAQ;AACf,WAAO,SAAS,MAAK;AACnB,WAAK,iBAAgB;IACvB;AACA,aAAS,KAAK,YAAY,MAAM;EAClC;EAEA,mBAAgB;AACd,QAAI,CAAC,OAAO,QAAQ;AAClB,WAAK,SAAS,KAAK,4BAA4B,SAAS,EAAE,UAAU,IAAI,CAAE;AAC1E;IACF;AAEA,QAAI;AAEF,WAAK,SAAS,OAAO,OAAO,YAAY,oBAAoB;AAG5D,iBAAW,MAAM,KAAK,eAAc,GAAI,GAAG;IAC7C,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,KAAK;AACjD,WAAK,SAAS,KAAK,+DAA+D,SAAS,EAAE,UAAU,IAAI,CAAE;IAC/G;EACF;EAEA,iBAAc;AACZ,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY,iBAAiB,CAAC,KAAK,QAAQ;AACxE,iBAAW,MAAM,KAAK,eAAc,GAAI,GAAG;AAC3C;IACF;AAEA,QAAI;AACF,YAAM,WAAW,KAAK,OAAO,SAAQ;AAGrC,WAAK,OAAO,SAAS,OAAO,QAAQ;QAClC,OAAO;UACL,MAAM;YACJ,WAAW;YACX,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,iBAAiB;cACf,OAAO;;;;OAId;AAGD,WAAK,KAAK,MAAM,KAAK,YAAY,aAAa;AAG9C,WAAK,KAAK,GAAG,UAAU,CAAC,UAAc;AACpC,aAAK,YAAY,MAAM,QAAQ,MAAM,MAAM,UAAU;AACrD,aAAK,eAAe,MAAM;MAC5B,CAAC;AAED,WAAK,YAAY;IACnB,SAAS,OAAO;AACd,cAAQ,MAAM,mCAAmC,KAAK;AACtD,WAAK,SAAS,KAAK,uCAAuC,SAAS,EAAE,UAAU,IAAI,CAAE;IACvF;EACF;EAEA,kBAAe;AAEb,WAAO;EACT;EAEM,eAAY;;AAChB,UAAI,CAAC,KAAK;AAAM;AAEhB,UAAI;AACF,cAAM,EAAC,KAAI,IAAI,MAAM,KAAK,eAAe,aACvC,KAAK,KAAK,iBACV,KAAK,KAAK,gBAAgB;AAI5B,cAAM,KAAK,YAAY,WAAW,KAAK,KAAK,IAAI,EAAE,KAAI,CAAE;MAC1D,SAAS,OAAO;AACd,gBAAQ,MAAM,0BAA0B,KAAK;MAC/C;IACF;;EAEA,SAAM;AACJ,QAAI,CAAC,KAAK;AAAM,aAAO;AAGvB,WACE,KAAK,KAAK,WAAW,gBACpB,CAAC,KAAK,KAAK,kBAAkB,KAAK,KAAK,mBAAmB,aAAa,KAAK,KAAK,mBAAmB;EAEzG;EAEA,mBAAgB;AACd,QAAI,CAAC,KAAK;AAAM,aAAO;AAGvB,WAAO,KAAK,KAAK,mBAAmB,UAAU,KAAK,KAAK,mBAAmB;EAC7E;EAEM,iBAAc;;AAClB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,OAAM,KAAM,CAAC,KAAK,UAAU,CAAC,KAAK;AAAM;AAEhE,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAErB,UAAI;AACF,cAAM,SAAS,KAAK,KAAK,UAAU,KAAK,KAAK,QAAQ,KAAK,gBAAe;AASzE,cAAM,EAAE,eAAe,OAAO,mBAAkB,IAAK,MAAM,KAAK,OAAO,oBAAoB;UACzF,MAAM;UACN,MAAM,KAAK;SACZ;AAED,YAAI,oBAAoB;AACtB,gBAAM;QACR;AACF,YAAI,oBAAoB;AACpB,gBAAM;QACR;AACE,YAAI,UAAU;UACd,QAAQ,SAAS;;UACjB,UAAU;UACV,aAAa;UACb,gBAAgB,cAAc;;AAE9B,gBAAQ,IAAI,OAAO;AAErB,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,YAAY,SAAS,UAAU,OAAO,UAAU;UACjF,MAAK;SAAS;AAEhB,YAAI,OAAO;AACT,kBAAQ,MAAM,kCAAkC,KAAK;AACrD,gBAAM,IAAI,MAAM,oCAAoC,MAAM,OAAO,EAAE;QACrE;AACE,gBAAQ,IAAI,2BAA2B,IAAI;AAE7C,YAAI,CAAC,QAAQ,CAAC,KAAK,eAAe;AAChC,gBAAM,IAAI,MAAM,wDAAwD;QAC1E;AAEA,cAAM,eAAe,KAAK;AAG1B,cAAM,EAAE,OAAO,cAAc,cAAa,IAAK,MAAM,KAAK,OAAO,mBAAmB,cAAc;UAChG,gBAAgB,cAAc;SAC/B;AAED,YAAI,cAAc;AAChB,gBAAM;QACR;AAGA,aAAK,gBAAgB;UACnB,SAAS;UACT;;AAIF,cAAM,KAAK,YAAY,WAAW,KAAK,KAAK,IAAI;UAC9C,gBAAgB;UAChB,YAAY,cAAc;UAC1B;SACD;AAED,aAAK,SAAS,KAAK,mCAAmC,SAAS,EAAE,UAAU,IAAI,CAAE;AAGjF,aAAK,iBAAiB,KAAI;MAS5B,SAAS,OAAY;AACnB,gBAAQ,MAAM,6BAA6B,KAAK;AAEhD,aAAK,gBAAgB;UACnB,SAAS;UACT,OAAO;YACL,SAAS,MAAM,WAAW;;;AAI9B,aAAK,SAAS,KAAK,kBAAkB,MAAM,OAAO,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;MACnF;AACE,aAAK,aAAa;MACpB;IACF;;EAEA,mBAAgB;AACd,QAAI,CAAC,KAAK;AAAM,aAAO;AACvB,YAAQ,KAAK,KAAK,UAAU,KAAK,KAAK,QAAQ,KAAK,gBAAe,GAAI,SAAQ;EAChF;EAEM,gBAAa;;AACjB,UAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,iBAAgB;AAAI;AAE5C,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAErB,UAAI;AAEF,cAAM,UAAU,MAAM,KAAK,eAAe,cAAc,KAAK,KAAK,EAAE;AAEpE,YAAI,SAAS;AACX,eAAK,gBAAgB;YACnB,SAAS;YACT,QAAQ;;AAGV,eAAK,SAAS,KAAK,kCAAkC,SAAS,EAAE,UAAU,IAAI,CAAE;AAGhF,eAAK,iBAAiB,KAAI;QAO5B,OAAO;AACL,eAAK,gBAAgB;YACnB,SAAS;YACT,QAAQ;YACR,OAAO;cACL,SAAS;;;AAIb,eAAK,SAAS,KAAK,4CAA4C,SAAS,EAAE,UAAU,IAAI,CAAE;QAC5F;MACF,SAAS,OAAY;AACnB,gBAAQ,MAAM,4BAA4B,KAAK;AAE/C,aAAK,gBAAgB;UACnB,SAAS;UACT,QAAQ;UACR,OAAO;YACL,SAAS,MAAM,WAAW;;;AAI9B,aAAK,SAAS,KAAK,iBAAiB,MAAM,OAAO,IAAI,SAAS,EAAE,UAAU,IAAI,CAAE;MAClF;AACE,aAAK,aAAa;MACpB;IACF;;;qCA9TW,uBAAoB,4BAAA,cAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,WAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;;;;;;;;AAnP7B,MAAA,qBAAA,GAAA,0CAAA,IAAA,IAAA,YAAA,CAAA;;;AAAW,MAAA,qBAAA,QAAA,IAAA,IAAA;;;IAVX;IAAY;IAAA;IAAA;IACZ;IAAa;IAAA;IAAA;IAAA;IACb;IAAe;IACf;IAAa;IACb;IAAwB;IACxB;IACA;IACA;IAAgB;EAAA,GAAA,QAAA,CAAA,wvFAAA,EAAA,CAAA;;;sEAsPP,sBAAoB,CAAA;UAjQhC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsFT,QAAA,CAAA,y5EAAA,EAAA,CAAA;+GA+JQ,MAAI,CAAA;UAAZ;MACyB,aAAW,CAAA;UAApC;WAAU,aAAa;MACd,kBAAgB,CAAA;UAAzB;;;;6EAHU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,2EAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;ACtNf,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC5C,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;;;;AAAxB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,eAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;;;;AAAzB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,gBAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1C,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAAgC,IAAA,uBAAA;;;;;AAAhC,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,QAAA,WAAA,CAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC5C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,kBAAA,EAChB,GAAA,UAAA;AAEd,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAW,EACM;;;;;AAHP,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,eAAA,QAAA,MAAA,CAAA;AACR,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,QAAA,MAAA,GAAA,GAAA;;;;;AAON,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;AAE3C,IAAA,yBAAA,GAAA,kBAAA,EAA8C,GAAA,UAAA;AAE1C,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAW;;;;AAFD,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,QAAA,cAAA;AACR,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,gBAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,MAAA;AAAmC,IAAA,iBAAA,GAAA,GAAA;AAAC,IAAA,uBAAA;;;;;AANtC,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,kDAAA,GAAA,GAAA,oBAAA,EAAA,EAA8C,GAAA,sCAAA,GAAA,GAAA,QAAA,EAAA;AAMhD,IAAA,uBAAA;;;;AANqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,cAAA;AAKZ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,QAAA,cAAA;;;;;AAKT,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1C,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,UAAA,QAAA,OAAA,OAAA,QAAA,UAAA,QAAA,MAAA,QAAA,CAAA,IAAA,KAAA,GAAA;;;;;AAKF,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAE3C,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,SAAA,EAAA,CAAmB;IAAA,CAAA;AAClC,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA,EAAW;;;;;;AAM7B,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,QAAA,CAAiB;IAAA,CAAA;AAChC,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA,EAAW;;;;;;AAXhC,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wCAAA,GAAA,GAAA,UAAA,EAAA,EAC+D,GAAA,wCAAA,GAAA,GAAA,UAAA,EAAA;AAW/D,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,YAAA,WAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,SAAA,EAAA,CAAwB;IAAA,CAAA;AACvC,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW,EACxB;;;;AAf6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;AAQE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;;;;;AAW5C,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;;AAeQ,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,gFAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAS,aAAA,WAAA,SAAA,EAAA;AAAmB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC5D,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA,EAAW;;;;;;AAE7B,IAAA,yBAAA,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,iFAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAS,aAAA,YAAA,QAAA;AAAiB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC1D,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA,EAAW;;;;;AAUhC,IAAA,yBAAA,GAAA,MAAA;AAAkC,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;;;;AAAzB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,cAAA;;;;;AAClC,IAAA,yBAAA,GAAA,MAAA;AAAmC,IAAA,iBAAA,GAAA,GAAA;AAAC,IAAA,uBAAA;;;;;;AAzB1C,IAAA,yBAAA,GAAA,qBAAA,EAAwE,GAAA,4BAAA,EAC1C,GAAA,iBAAA;AAExB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,uBAAA,EAAuB,GAAA,OAAA,EAAA,EACY,GAAA,QAAA,EAAA;AACP,IAAA,iBAAA,CAAA;;AAAyC,IAAA,uBAAA;AACjE,IAAA,qBAAA,GAAA,yDAAA,GAAA,GAAA,UAAA,EAAA,EACyF,IAAA,0DAAA,GAAA,GAAA,UAAA,EAAA;AAO3F,IAAA,uBAAA,EAAM,EACgB;AAE1B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,GAAA,EACrB,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAAqC,IAAA,uBAAA;AAC/D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA+B,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACjB,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA,EAAkC,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA;AAEpC,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAqF,IAAA,uBAAA,EAAI;AAErH,IAAA,yBAAA,IAAA,gBAAA,EAAgB,IAAA,UAAA,EAAA;AAC0B,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,WAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,SAAA,EAAA,CAAwB;IAAA,CAAA;AACvE,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA,EAAW,EACxB,EACM;;;;;AA9Bb,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,iBAAA,GAAA;AAI0B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,IAAA,SAAA,aAAA,WAAA,CAAA;AACc,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;AAIE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;AAQpB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,kBAAA,EAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,IAAA,SAAA,aAAA,OAAA,GAAA,EAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,SAAA,MAAA,GAAA,EAAA;AAEnB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,cAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,SAAA,cAAA;AAEiB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,UAAA,SAAA,OAAA,OAAA,SAAA,UAAA,SAAA,MAAA,QAAA,CAAA,IAAA,KAAA,EAAA;;;;;;AAe1C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkD,GAAA,oBAAA,EAAA;AAG9C,IAAA,qBAAA,oBAAA,SAAA,8EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAoB,OAAA,aAAA,CAAc;IAAA,CAAA;AACpC,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAqD,IAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AAC1E,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA,EAAW,EACnB;;;;AALP,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA;;;;;;AAQJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,mBAAA,EAAA;AAIpD,IAAA,qBAAA,oBAAA,SAAA,2EAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAoB,OAAA,YAAA,MAAA,CAAmB;IAAA,CAAA,EAAC,eAAA,SAAA,sEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBACzB,OAAA,cAAA,MAAA,CAAqB;IAAA,CAAA;AACtC,IAAA,uBAAA,EAAkB;;;;AAJhB,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,OAAA,cAAA,EAAyB,WAAA,OAAA,iBAAA,KAAA,MAAA,CAAA;;;AAuK3B,IAAO,iBAAP,MAAO,gBAAc;EASf;EACA;EACA;EACA;EACA;EACA;EACA;EAdV,QAAgB,CAAA;EAChB,mBAAmB,CAAC,mBAAmB,oBAAoB,eAAe,UAAU,kBAAkB,QAAQ,SAAS;EACvH,cAA2B;EAC3B,eAA4B;EAC5B,iBAAgC;EACxB,oBAAyC;EAEjD,YACU,aACA,aACA,gBACA,gBACA,QACA,QACA,UAAqB;AANrB,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,iBAAA;AACA,SAAA,iBAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,WAAA;EACP;EAEG,WAAQ;;AACZ,WAAK,cAAc,MAAM,KAAK,YAAY,eAAc;AACxD,UAAI,KAAK,aAAa;AAEpB,cAAM,KAAK,cAAa;AAGxB,aAAK,oBAAoB,KAAK,YAAY,OAAO,UAAU,CAAC,UAAgB;AAC1E,cAAI,KAAK,aAAa;AACpB,iBAAK,QAAQ,MAAM,OAAO,UAAQ,KAAK,aAAa,KAAK,YAAa,EAAE;UAC1E;QACF,CAAC;MACH;IACF;;EAEA,cAAW;AAET,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAW;IACpC;EACF;EAEM,gBAAa;;AACjB,UAAI,KAAK,aAAa;AACpB,YAAI;AACF,eAAK,QAAQ,MAAM,KAAK,YAAY,aAAa,KAAK,YAAY,EAAE;QACtE,SAAS,OAAO;AACd,kBAAQ,MAAM,6BAA6B,KAAK;AAChD,eAAK,SAAS,KAAK,wBAAwB,SAAS,EAAE,UAAU,IAAI,CAAE;QACxE;MACF;IACF;;EAEM,WAAW,QAAc;;AAC7B,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,YAAY,WAAW,MAAM;AACxD,YAAI,SAAS;AACX,eAAK,SAAS,KAAK,8BAA8B,SAAS,EAAE,UAAU,IAAI,CAAE;AAC5E,gBAAM,KAAK,cAAa;QAC1B,OAAO;AACL,gBAAM,IAAI,MAAM,uBAAuB;QACzC;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAK,SAAS,KAAK,yBAAyB,SAAS,EAAE,UAAU,IAAI,CAAE;MACzE;IACF;;EAEA,WAAW,YAAkB;AAC3B,WAAO,IAAI,KAAK,UAAU,EAAE,eAAc;EAC5C;EAEA,aAAa,QAAkB;AAC7B,WAAO,OAAO,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,OAAO,CAAC,EAAE,YAAW,IAAK,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG;EAC7F;EAEA,eAAe,QAAkB;AAC/B,WAAO,sBAAsB,MAAM;EACrC;EAEM,SAAS,QAAc;;AAC3B,UAAI;AAEF,cAAM,SAAS,MAAM,KAAK,eAAe,yBAAyB,MAAM;AACxE,aAAK,OAAO,SAAS,CAAC,cAAc,SAAS,YAAY,OAAO,EAAE,CAAC;MACrE,SAAS,OAAO;AACd,gBAAQ,MAAM,uBAAuB,KAAK;AAC1C,aAAK,SAAS,KAAK,uBAAuB,SAAS,EAAE,UAAU,IAAI,CAAE;MACvE;IACF;;EAEA,YAAY,MAAU;AACpB,SAAK,eAAe;EACtB;EAEA,eAAY;AACV,SAAK,eAAe;AAEpB,SAAK,cAAa;EACpB;EAEA,gBAAgB,QAAc;AAC5B,SAAK,iBAAiB;EACxB;EAEA,mBAAgB;AACd,SAAK,iBAAiB;AAEtB,SAAK,cAAa;EACpB;EAEA,cAAc,OAAW;AAEvB,SAAK,cAAa;EACpB;EAEA,cAAc,QAAgB,MAAU;AACtC,WAAO,KAAK;EACd;;qCApHW,iBAAc,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,SAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,SAAA,cAAA,GAAA,CAAA,SAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,aAAA,IAAA,GAAA,cAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,iBAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,kBAAA,GAAA,CAAA,gBAAA,aAAA,GAAA,CAAA,gBAAA,QAAA,GAAA,CAAA,gBAAA,gBAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,WAAA,cAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,QAAA,cAAA,eAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,UAAA,cAAA,gBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,WAAA,cAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,QAAA,cAAA,eAAA,GAAA,OAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,UAAA,cAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,EAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,oBAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,wBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,oBAAA,eAAA,UAAA,SAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAvTvB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,eAAA,EAChB,GAAA,WAAA,CAAA;AAEX,MAAA,oBAAA,GAAA,kBAAA;AACF,MAAA,uBAAA;AAMA,MAAA,yBAAA,GAAA,WAAA,CAAA,EAA8B,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EAED,GAAA,SAAA,CAAA;AAEtB,MAAA,kCAAA,GAAA,CAAA;AACE,MAAA,qBAAA,GAAA,8BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAIxC,MAAA,kCAAA,IAAA,CAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAIxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAIxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAUxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAWxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAMxC,MAAA,kCAAA,IAAA,EAAA;AACE,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA,EAAsC,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;;AAqBxC,MAAA,qBAAA,IAAA,+BAAA,GAAA,GAAA,MAAA,EAAA,EAAuD,IAAA,+BAAA,GAAA,GAAA,MAAA,EAAA;AAEzD,MAAA,uBAAA,EAAQ;AAIV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,iBAAA,EAAA;AAErB,MAAA,qBAAA,IAAA,gDAAA,IAAA,IAAA,uBAAA,EAAA;AAmCF,MAAA,uBAAA,EAAgB,EACZ,EACF,EACE,EACI;AAGlB,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA,EAAkD,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA;;;AAtHvB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,cAAA,IAAA,KAAA;AAoEK,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,mBAAA,IAAA,gBAAA;AACa,MAAA,oBAAA;AAAA,MAAA,qBAAA,oBAAA,IAAA,gBAAA;AAOK,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,KAAA,EAAU,gBAAA,IAAA,aAAA;AA0CtD,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA;AAUA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;;;IA/JN;IAAY;IAAA;IAAA;IACZ;IACA;IAAa;IAAA;IACb;IAAc;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACd;IAAc;IAAA;IACd;IAAe;IACf;IAAa;IACb;IACA;IAAgB;IAChB;IACA;IAAkB;IAAA;IAAA;IAAA;IAAA;IAAA;IAClB;IAEA;IACA;EAAmB,GAAA,QAAA,CAAA,mqGAAA,EAAA,CAAA;;;sEA0TV,gBAAc,CAAA;UA5U1B;uBACW,aAAW,YACT,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuJT,QAAA,CAAA,6lFAAA,EAAA,CAAA;;;;6EAiKU,gBAAc,EAAA,WAAA,kBAAA,UAAA,uDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}