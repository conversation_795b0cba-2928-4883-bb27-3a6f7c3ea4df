{"version": 3, "sources": ["src/app/features/dashboard/shared/messages/messages.component.ts"], "sourcesContent": ["\n    .messages-container {\n      display: flex;\n      height: calc(100vh - 150px);\n      padding: 20px;\n      gap: 20px;\n    }\n    \n    .threads-card {\n      width: 300px;\n      overflow-y: auto;\n    }\n    \n    .chat-container {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .loading-container {\n      display: flex;\n      justify-content: center;\n      padding: 20px;\n    }\n    \n    .no-threads {\n      padding: 20px;\n      text-align: center;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .thread-item {\n      display: flex;\n      align-items: center;\n      width: 100%;\n      padding: 8px 0;\n    }\n    \n    .thread-icon {\n      margin-right: 16px;\n      color: #3f51b5;\n    }\n    \n    .thread-details {\n      flex: 1;\n    }\n    \n    .thread-title {\n      font-weight: 500;\n    }\n    \n    .thread-date {\n      font-size: 0.8em;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .active-thread {\n      background-color: rgba(63, 81, 181, 0.1);\n    }\n    \n    .empty-chat {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .empty-icon {\n      font-size: 64px;\n      height: 64px;\n      width: 64px;\n      margin-bottom: 16px;\n    }\n    \n    @media (max-width: 768px) {\n      .messages-container {\n        flex-direction: column;\n      }\n      \n      .threads-card {\n        width: 100%;\n        max-height: 200px;\n      }\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,UAAA,KAAA,MAAA,EAAA;AACA,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,QAAA;AACA,WAAA;AACA,kBAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,cAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,SAAA;AACA,WAAA,IAAA;;AAGF,CAAA;AACE,gBAAA;AACA,SAAA;;AAGF,CAAA;AACE,QAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,oBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,GAAA,EAAA;;AAGF,CAAA;AACE,QAAA;AACA,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,aAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;;AAGF,OAAA,CAAA,SAAA,EAAA;AACE,GA5EF;AA6EI,oBAAA;;AAGF,GAzEF;AA0EI,WAAA;AACA,gBAAA;;;", "names": []}