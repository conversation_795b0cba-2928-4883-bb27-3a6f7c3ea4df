{"version": 3, "sources": ["src/app/features/dashboard/admin/square-sandbox/square-sandbox.component.ts"], "sourcesContent": ["\n    .container {\n      max-width: 800px;\n      margin: 20px auto;\n    }\n    .full-width {\n      width: 100%;\n      margin-bottom: 15px;\n    }\n    .card-container {\n      margin: 20px 0;\n      min-height: 140px;\n      border: 1px solid #e0e0e0;\n      border-radius: 4px;\n      padding: 12px;\n    }\n    .button-container {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 20px;\n    }\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 40px 0;\n    }\n    .divider {\n      margin: 30px 0;\n    }\n    .payment-results {\n      margin-top: 20px;\n    }\n    pre {\n      background-color: #f5f5f5;\n      padding: 15px;\n      border-radius: 4px;\n      overflow: auto;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,aAAA;AACA,UAAA,KAAA;;AAEF,CAAA;AACE,SAAA;AACA,iBAAA;;AAEF,CAAA;AACE,UAAA,KAAA;AACA,cAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,WAAA;;AAEF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;AAEF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,KAAA;;AAEF,CAAA;AACE,UAAA,KAAA;;AAEF,CAAA;AACE,cAAA;;AAEF;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,YAAA;;", "names": []}