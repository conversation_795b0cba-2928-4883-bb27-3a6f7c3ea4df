{"version": 3, "sources": ["src/app/shared/components/ride-chat/ride-chat.component.ts"], "sourcesContent": ["\n    .chat-card {\n      margin: 16px;\n      max-width: 800px;\n    }\n    \n    .messages-container {\n      height: 300px;\n      overflow-y: auto;\n      padding: 16px;\n      background-color: #f5f5f5;\n      border-radius: 4px;\n    }\n    \n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n    }\n    \n    .no-messages {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      height: 100%;\n      color: rgba(0, 0, 0, 0.5);\n    }\n    \n    .messages-list {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n    }\n    \n    .message-bubble {\n      max-width: 80%;\n      padding: 8px 12px;\n      border-radius: 16px;\n      position: relative;\n    }\n    \n    .sent {\n      align-self: flex-end;\n      background-color: #2196f3;\n      color: white;\n      border-bottom-right-radius: 4px;\n    }\n    \n    .received {\n      align-self: flex-start;\n      background-color: white;\n      border-bottom-left-radius: 4px;\n    }\n    \n    .message-content {\n      word-break: break-word;\n    }\n    \n    .message-time {\n      font-size: 0.7em;\n      opacity: 0.7;\n      text-align: right;\n      margin-top: 4px;\n    }\n    \n    .message-form {\n      display: flex;\n      gap: 8px;\n      width: 100%;\n      padding: 0 16px 16px;\n    }\n    \n    .message-input {\n      flex: 1;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,UAAA;AACA,aAAA;;AAGF,CAAA;AACE,UAAA;AACA,cAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,UAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,aAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,YAAA;;AAGF,CAAA;AACE,cAAA;AACA,oBAAA;AACA,SAAA;AACA,8BAAA;;AAGF,CAAA;AACE,cAAA;AACA,oBAAA;AACA,6BAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,aAAA;AACA,WAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA,EAAA,KAAA;;AAGF,CAAA;AACE,QAAA;;", "names": []}