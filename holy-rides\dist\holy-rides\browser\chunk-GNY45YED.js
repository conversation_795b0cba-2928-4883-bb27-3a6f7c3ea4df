import {
  Activated<PERSON><PERSON><PERSON>,
  Router,
  RouterLink
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  DefaultV<PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MatError,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  MatLabel,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  CommonModule,
  Component,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  NgIf,
  filter,
  firstValueFrom,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import {
  __async
} from "./chunk-S35DAJRX.js";

// src/app/features/auth/login/login.component.ts
function LoginComponent_mat_error_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_error_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Please enter a valid email");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_error_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Password is required");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_mat_error_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Password must be at least 6 characters");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
var LoginComponent = class _LoginComponent {
  formBuilder;
  router;
  route;
  authService;
  loginForm;
  error = "";
  loading = false;
  MAX_ROLE_CHECK_ATTEMPTS = 3;
  RETRY_DELAY = 1e3;
  constructor(formBuilder, router, route, authService) {
    this.formBuilder = formBuilder;
    this.router = router;
    this.route = route;
    this.authService = authService;
    this.loginForm = this.formBuilder.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(6)]]
    });
  }
  onSubmit() {
    return __async(this, null, function* () {
      if (this.loginForm.invalid) {
        return;
      }
      this.loading = true;
      this.error = "";
      try {
        const { error } = yield this.authService.login(this.loginForm.value.email, this.loginForm.value.password);
        if (error) {
          this.error = error.message;
          return;
        }
        const user = yield firstValueFrom(this.authService.user$.pipe(filter((user2) => user2 !== null)));
        if (user && !user.is_approved) {
          const message = user.role === "admin" ? "Your account has been deactivated. Please contact support." : "Your account is pending approval. Please wait for administrator approval.";
          this.error = message;
          yield this.authService.logout();
          return;
        }
        let role = null;
        let attempts = 0;
        while (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS) {
          console.log("Attempting to get user role...");
          role = yield this.authService.getUserRole();
          if (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS - 1) {
            yield new Promise((resolve) => setTimeout(resolve, this.RETRY_DELAY));
          }
          attempts++;
        }
        if (role) {
          const dashboardRoute = this.authService.getDashboardRouteForRole(role);
          yield this.router.navigate([dashboardRoute]);
        } else {
          this.error = "Unable to determine user role. Please try logging in again.";
          yield this.authService.logout();
        }
      } catch (error) {
        console.error("Login error:", error);
        this.error = "An error occurred during login. Please try again.";
      } finally {
        this.loading = false;
      }
    });
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 26, vars: 8, consts: [[1, "login-container"], [3, "ngSubmit", "formGroup"], ["appearance", "outline"], ["matInput", "", "type", "email", "formControlName", "email", "placeholder", "Enter your email"], [4, "ngIf"], ["matInput", "", "type", "password", "formControlName", "password", "placeholder", "Enter your password"], ["class", "error-message", 4, "ngIf"], [1, "button-container"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], [1, "links"], ["routerLink", "/auth/register"], [1, "error-message"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Login");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "form", 1);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_6_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(7, "mat-form-field", 2)(8, "mat-label");
      \u0275\u0275text(9, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(10, "input", 3);
      \u0275\u0275template(11, LoginComponent_mat_error_11_Template, 2, 0, "mat-error", 4)(12, LoginComponent_mat_error_12_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "mat-form-field", 2)(14, "mat-label");
      \u0275\u0275text(15, "Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 5);
      \u0275\u0275template(17, LoginComponent_mat_error_17_Template, 2, 0, "mat-error", 4)(18, LoginComponent_mat_error_18_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275template(19, LoginComponent_div_19_Template, 2, 1, "div", 6);
      \u0275\u0275elementStart(20, "div", 7)(21, "button", 8);
      \u0275\u0275text(22);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(23, "div", 9)(24, "a", 10);
      \u0275\u0275text(25, "Don't have an account? Register");
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_2_0;
      let tmp_3_0;
      let tmp_4_0;
      \u0275\u0275advance(6);
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.loginForm.get("email")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_2_0 = ctx.loginForm.get("email")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors["email"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_3_0 = ctx.loginForm.get("password")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_4_0 = ctx.loginForm.get("password")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors["minlength"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.loginForm.invalid || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.loading ? "Logging in..." : "Login", " ");
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, MatFormFieldModule, MatFormField, MatLabel, MatError, MatInputModule, MatInput, MatButtonModule, MatButton, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, RouterLink], styles: ["\n\n.login-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 20px;\n}\n.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n}\n.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\n  width: 100px;\n  margin-bottom: 10px;\n}\n.login-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 500;\n  color: #3f51b5;\n  margin: 0;\n}\n.login-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.login-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.login-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.login-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 8px;\n}\n.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  text-align: center;\n}\n.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #3f51b5;\n  text-decoration: none;\n}\n.login-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\n  text-decoration: underline;\n}\n.login-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\n  color: #f44336;\n  text-align: center;\n  margin: 8px 0;\n}\n/*# sourceMappingURL=login.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{ selector: "app-login", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatCardModule,
      RouterLink
    ], template: `<div class="login-container">\r
  <!-- <div class="logo-container">\r
    <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">\r
\r
  </div> -->\r
  <mat-card>\r
    <mat-card-header>\r
      <mat-card-title>Login</mat-card-title>\r
    </mat-card-header>\r
    <mat-card-content>\r
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">\r
        <mat-form-field appearance="outline">\r
          <mat-label>Email</mat-label>\r
          <input matInput type="email" formControlName="email" placeholder="Enter your email">\r
          <mat-error *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</mat-error>\r
          <mat-error *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Password</mat-label>\r
          <input matInput type="password" formControlName="password" placeholder="Enter your password">\r
          <mat-error *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</mat-error>\r
          <mat-error *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</mat-error>\r
        </mat-form-field>\r
\r
        <div class="error-message" *ngIf="error">{{ error }}</div>\r
\r
        <div class="button-container">\r
          <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || loading">\r
            {{ loading ? 'Logging in...' : 'Login' }}\r
          </button>\r
        </div>\r
\r
        <div class="links">\r
          <a routerLink="/auth/register">Don't have an account? Register</a>\r
        </div>\r
      </form>\r
    </mat-card-content>\r
  </mat-card>\r
</div>\r
`, styles: ["/* src/app/features/auth/login/login.component.scss */\n.login-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 20px;\n}\n.login-container .logo-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n}\n.login-container .logo-container .logo {\n  width: 100px;\n  margin-bottom: 10px;\n}\n.login-container .logo-container .app-name {\n  font-size: 24px;\n  font-weight: 500;\n  color: #3f51b5;\n  margin: 0;\n}\n.login-container mat-card {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.login-container mat-form-field {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.login-container .button-container {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.login-container .button-container button {\n  width: 100%;\n  padding: 8px;\n}\n.login-container .links {\n  margin-top: 16px;\n  text-align: center;\n}\n.login-container .links a {\n  color: #3f51b5;\n  text-decoration: none;\n}\n.login-container .links a:hover {\n  text-decoration: underline;\n}\n.login-container .error-message {\n  color: #f44336;\n  text-align: center;\n  margin: 8px 0;\n}\n/*# sourceMappingURL=login.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: Router }, { type: ActivatedRoute }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/features/auth/login/login.component.ts", lineNumber: 28 });
})();
export {
  LoginComponent
};
//# sourceMappingURL=chunk-GNY45YED.js.map
