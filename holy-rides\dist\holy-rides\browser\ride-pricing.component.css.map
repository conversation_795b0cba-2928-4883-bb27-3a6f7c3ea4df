{"version": 3, "sources": ["src/app/features/dashboard/admin/ride-pricing/ride-pricing.component.scss"], "sourcesContent": [".pricing-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.form-card, .table-card {\n  width: 100%;\n}\n\n.form-row {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 16px;\n  align-items: center;\n}\n\n.full-width {\n  width: 100%;\n}\n\nmat-form-field {\n  flex: 1;\n  min-width: 150px;\n}\n\n.active-toggle {\n  margin: 16px 0;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n  margin-top: 16px;\n}\n\n.table-container {\n  overflow-x: auto;\n}\n\ntable {\n  width: 100%;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n}\n\n.loading-container p {\n  margin-top: 16px;\n  color: rgba(0, 0, 0, 0.54);\n}\n\n.no-data {\n  text-align: center;\n  padding: 20px;\n  color: rgba(0, 0, 0, 0.54);\n}\n\n.status-badge {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n  background-color: #e0e0e0;\n  color: #757575;\n}\n\n.status-badge.active {\n  background-color: #c8e6c9;\n  color: #2e7d32;\n}\n\n.sample-calculation {\n  margin: 20px 0;\n  padding: 10px 0;\n}\n\n.sample-calculation h3 {\n  margin: 10px 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.sample-calculation p {\n  margin: 10px 0;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .form-row {\n    flex-direction: column;\n  }\n\n  mat-form-field {\n    width: 100%;\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AAAA,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;;AAGF;AACE,QAAA;AACA,aAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,cAAA;;AAGF;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CARA,kBAQA;AACE,cAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,oBAAA;AACA,SAAA;;AAGF,CATA,YASA,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA,KAAA;AACA,WAAA,KAAA;;AAGF,CALA,mBAKA;AACE,UAAA,KAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAXA,mBAWA;AACE,UAAA,KAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAtFF;AAuFI,oBAAA;;AAGF;AACE,WAAA;;;", "names": []}