{"version": 3, "sources": ["src/app/core/services/google-maps-loader.service.ts", "src/app/core/services/location.service.ts", "src/app/shared/components/map-display/map-display.component.ts"], "sourcesContent": ["import { Injectable, Inject, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GoogleMapsLoaderService {\n  private isLoaded = false;\n  private loadingPromise: Promise<void> | null = null;\n\n  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}\n\n  /**\n   * Load the Google Maps API script\n   */\n  loadGoogleMapsApi(): Promise<void> {\n    // Return if not in browser environment\n    if (!isPlatformBrowser(this.platformId)) {\n      return Promise.resolve();\n    }\n\n    // Return existing promise if already loading\n    if (this.loadingPromise) {\n      return this.loadingPromise;\n    }\n\n    // Return resolved promise if already loaded\n    if (this.isLoaded) {\n      return Promise.resolve();\n    }\n\n    // Create a new loading promise\n    this.loadingPromise = new Promise<void>((resolve, reject) => {\n      // Check if the API is already loaded\n      if (window.google && window.google.maps) {\n        this.isLoaded = true;\n        resolve();\n        return;\n      }\n\n      // Create a callback function name\n      const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1000000)}`;\n\n      // Add the callback to the window object\n      (window as any)[callbackName] = () => {\n        this.isLoaded = true;\n        resolve();\n        delete (window as any)[callbackName];\n      };\n\n      // Create the script element\n      const script = document.createElement('script');\n      script.src = `https://maps.googleapis.com/maps/api/js?key=${environment.googleMapsApiKey}&libraries=places&callback=${callbackName}`;\n      script.async = true;\n      script.defer = true;\n      script.onerror = (error) => {\n        reject(new Error(`Failed to load Google Maps API: ${error}`));\n        delete (window as any)[callbackName];\n      };\n\n      // Append the script to the document\n      document.head.appendChild(script);\n    });\n\n    return this.loadingPromise;\n  }\n\n  /**\n   * Check if the Google Maps API is loaded\n   */\n  isGoogleMapsLoaded(): boolean {\n    return this.isLoaded;\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport { GoogleMapsLoaderService } from './google-maps-loader.service';\n\nexport interface Coordinates {\n  latitude: number;\n  longitude: number;\n}\n\nexport interface RouteInfo {\n  distance: number; // in miles\n  duration: number; // in minutes\n  polyline?: string; // encoded polyline for the route\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class LocationService {\n  private currentLocationSubject = new BehaviorSubject<Coordinates | null>(null);\n  currentLocation$ = this.currentLocationSubject.asObservable();\n\n  constructor(private googleMapsLoader: GoogleMapsLoaderService) {}\n\n  /**\n   * Get the user's current location using the Geolocation API\n   */\n  getCurrentLocation(): Promise<Coordinates> {\n    return new Promise((resolve, reject) => {\n      if (!navigator.geolocation) {\n        reject(new Error('Geolocation is not supported by your browser'));\n        return;\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          const coords: Coordinates = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude\n          };\n          this.currentLocationSubject.next(coords);\n          resolve(coords);\n        },\n        (error) => {\n          reject(error);\n        },\n        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }\n      );\n    });\n  }\n\n  /**\n   * Convert an address to coordinates (geocoding)\n   * Uses the Google Maps Geocoding API\n   */\n  async geocodeAddress(address: string): Promise<Coordinates> {\n    try {\n      await this.googleMapsLoader.loadGoogleMapsApi();\n      \n      return new Promise((resolve, reject) => {\n        if (!google || !google.maps || !google.maps.Geocoder) {\n          console.warn('Google Maps Geocoder not available, using mock data');\n          // Generate random coordinates (this is just for demo)\n          const latitude = 40 + (Math.random() * 10 - 5);\n          const longitude = -74 + (Math.random() * 10 - 5);\n\n          resolve({ latitude, longitude });\n          return;\n        }\n\n        const geocoder = new google.maps.Geocoder();\n\n        geocoder.geocode({ address }, (results, status) => {\n          if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {\n            const location = results[0].geometry.location;\n            resolve({\n              latitude: location.lat(),\n              longitude: location.lng()\n            });\n          } else {\n            console.warn(`Geocoding failed for address: ${address}. Status: ${status}`);\n            // Fall back to mock data if geocoding fails\n            const latitude = 40 + (Math.random() * 10 - 5);\n            const longitude = -74 + (Math.random() * 10 - 5);\n\n            resolve({ latitude, longitude });\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Failed to load Google Maps API:', error);\n      // Fall back to mock data\n      return {\n        latitude: 40 + (Math.random() * 10 - 5),\n        longitude: -74 + (Math.random() * 10 - 5)\n      };\n    }\n  }\n\n  /**\n   * Calculate route between two points\n   * Uses the Google Maps Directions API\n   */\n  async calculateRoute(origin: Coordinates | string, destination: Coordinates | string): Promise<RouteInfo> {\n    try {\n      await this.googleMapsLoader.loadGoogleMapsApi();\n      \n      return new Promise((resolve, reject) => {\n        // If the DirectionsService is not available, fall back to mock data\n        if (!google || !google.maps || !google.maps.DirectionsService) {\n          console.warn('Google Maps DirectionsService not available, using mock data');\n          // Random distance between 2 and 20 miles\n          const distance = Math.floor(Math.random() * 18) + 2;\n          // Random duration between 5 and 60 minutes\n          const duration = Math.floor(Math.random() * 55) + 5;\n\n          resolve({\n            distance,\n            duration,\n            polyline: 'mock_polyline_string'\n          });\n          return;\n        }\n\n        const directionsService = new google.maps.DirectionsService();\n\n        // Convert origin and destination to string format if they are coordinates\n        const originStr = typeof origin === 'string' ? origin : `${origin.latitude},${origin.longitude}`;\n        const destinationStr = typeof destination === 'string' ? destination : `${destination.latitude},${destination.longitude}`;\n\n        const request: google.maps.DirectionsRequest = {\n          origin: originStr,\n          destination: destinationStr,\n          travelMode: google.maps.TravelMode.DRIVING\n        };\n\n        directionsService.route(request, (result, status) => {\n          if (status === google.maps.DirectionsStatus.OK && result) {\n            const route = result.routes[0];\n            if (route && route.legs && route.legs.length > 0) {\n              const leg = route.legs[0];\n\n              // Convert distance from meters to miles\n              const distanceInMiles = leg.distance ? leg.distance.value / 1609.34 : 0;\n\n              // Convert duration from seconds to minutes\n              const durationInMinutes = leg.duration ? Math.ceil(leg.duration.value / 60) : 0;\n\n              // Get encoded polyline\n              const polyline = route.overview_polyline ? route.overview_polyline : '';\n\n              resolve({\n                distance: parseFloat(distanceInMiles.toFixed(2)),\n                duration: durationInMinutes,\n                polyline: polyline\n              });\n            } else {\n              console.warn('No route found');\n              // Fall back to mock data\n              resolve({\n                distance: Math.floor(Math.random() * 18) + 2,\n                duration: Math.floor(Math.random() * 55) + 5,\n                polyline: 'mock_polyline_string'\n              });\n            }\n          } else {\n            console.warn(`Directions request failed. Status: ${status}`);\n            // Fall back to mock data\n            resolve({\n              distance: Math.floor(Math.random() * 18) + 2,\n              duration: Math.floor(Math.random() * 55) + 5,\n              polyline: 'mock_polyline_string'\n            });\n          }\n        });\n      });\n    } catch (error) {\n      console.error('Failed to load Google Maps API:', error);\n      // Fall back to mock data\n      return {\n        distance: Math.floor(Math.random() * 18) + 2,\n        duration: Math.floor(Math.random() * 55) + 5,\n        polyline: 'mock_polyline_string'\n      };\n    }\n  }\n\n  /**\n   * Generate a Google Maps URL for navigation\n   */\n  getGoogleMapsUrl(address: string): string {\n    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;\n  }\n\n  /**\n   * Generate a Google Maps Directions URL\n   */\n  getGoogleMapsDirectionsUrl(origin: string, destination: string): string {\n    return `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&travelmode=driving`;\n  }\n}\n", "import { Component, Input, OnInit, OnChanges, SimpleChanges, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { environment } from '../../../../environments/environment';\nimport { Coordinates, LocationService } from '../../../core/services/location.service';\nimport { GoogleMapsModule, MapDirectionsService } from '@angular/google-maps';\nimport { map, Observable, of } from 'rxjs';\nimport { GoogleMapsLoaderService } from '../../../core/services/google-maps-loader.service';\n\n@Component({\n  selector: 'app-map-display',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    GoogleMapsModule\n  ],\n  template: `\n    <mat-card class=\"map-card\">\n      <mat-card-content>\n        <div *ngIf=\"!apiLoaded\" class=\"map-placeholder\">\n          <p>Loading map...</p>\n        </div>\n        <div class=\"map-container\" [style.display]=\"apiLoaded ? 'block' : 'none'\">\n          <google-map\n            *ngIf=\"apiLoaded\"\n            height=\"300px\"\n            width=\"100%\"\n            [center]=\"center\"\n            [zoom]=\"zoom\"\n            [options]=\"options\">\n            <map-marker\n              *ngIf=\"originMarker\"\n              [position]=\"originMarker\"\n              [title]=\"'Origin'\"\n              [options]=\"markerOptions\">\n            </map-marker>\n            <map-marker\n              *ngIf=\"destinationMarker\"\n              [position]=\"destinationMarker\"\n              [title]=\"'Destination'\"\n              [options]=\"markerOptions\">\n            </map-marker>\n            <map-directions-renderer\n              *ngIf=\"directionsResults$ | async as directionsResults\"\n              [directions]=\"directionsResults\">\n            </map-directions-renderer>\n          </google-map>\n        </div>\n\n        <div class=\"map-actions\" *ngIf=\"showDirectionsLink && origin && destination\">\n          <a [href]=\"directionsUrl\" target=\"_blank\">\n            <button mat-raised-button color=\"primary\">\n              <mat-icon>directions</mat-icon>\n              Get Directions\n            </button>\n          </a>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [`\n    .map-card {\n      margin-bottom: 16px;\n    }\n\n    .map-container {\n      height: 300px;\n      width: 100%;\n    }\n\n    .map-placeholder {\n      height: 300px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      background-color: #f5f5f5;\n      color: #666;\n    }\n\n    .map-actions {\n      margin-top: 16px;\n      display: flex;\n      justify-content: center;\n    }\n  `]\n})\nexport class MapDisplayComponent implements OnInit, OnChanges, AfterViewInit {\n  @Input() origin?: string | Coordinates;\n  @Input() destination?: string | Coordinates;\n  @Input() showDirectionsLink: boolean = true;\n\n  // Google Maps properties\n  apiLoaded: boolean = false;\n  directionsUrl: string = '';\n  center: google.maps.LatLngLiteral = { lat: 40.7128, lng: -74.0060 }; // Default to NYC\n  zoom = 12;\n  options: google.maps.MapOptions = {\n    mapTypeId: 'roadmap',\n    zoomControl: true,\n    scrollwheel: true,\n    disableDoubleClickZoom: false,\n    maxZoom: 20,\n    minZoom: 4,\n  };\n  originMarker?: google.maps.LatLngLiteral;\n  destinationMarker?: google.maps.LatLngLiteral;\n  markerAnimation: typeof google.maps.Animation | null = null;\n  markerOptions: google.maps.MarkerOptions = {};\n  directionsResults$: Observable<google.maps.DirectionsResult | undefined> | undefined;\n\n  constructor(\n    private locationService: LocationService,\n    private mapDirectionsService: MapDirectionsService,\n    private googleMapsLoader: GoogleMapsLoaderService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadGoogleMapsApi();\n    this.updateDirectionsUrl();\n  }\n\n  ngOnChanges(changes: SimpleChanges): void {\n    if ((changes['origin'] || changes['destination']) && this.apiLoaded) {\n      this.updateMap();\n      this.updateDirectionsUrl();\n    }\n  }\n\n  ngAfterViewInit(): void {\n    if (this.apiLoaded) {\n      this.initMap();\n    }\n  }\n\n  private async loadGoogleMapsApi(): Promise<void> {\n    try {\n      await this.googleMapsLoader.loadGoogleMapsApi();\n      this.apiLoaded = true;\n      \n      if (typeof google !== 'undefined' && google.maps) {\n        this.markerAnimation = google.maps.Animation;\n        this.markerOptions = {\n          animation: google.maps.Animation.DROP\n        };\n      }\n      \n      this.initMap();\n    } catch (error) {\n      console.error('Failed to load Google Maps API:', error);\n      this.apiLoaded = false;\n    }\n  }\n\n  private initMap(): void {\n    if (this.origin && this.destination) {\n      this.updateMap();\n    }\n  }\n\n  private async updateMap(): Promise<void> {\n    if (!this.origin || !this.destination) return;\n\n    try {\n      // Convert origin and destination to coordinates if they are strings\n      const originCoords = typeof this.origin === 'string'\n        ? await this.locationService.geocodeAddress(this.origin)\n        : this.origin;\n\n      const destCoords = typeof this.destination === 'string'\n        ? await this.locationService.geocodeAddress(this.destination)\n        : this.destination;\n\n      // Set markers\n      this.originMarker = {\n        lat: originCoords.latitude,\n        lng: originCoords.longitude\n      };\n\n      this.destinationMarker = {\n        lat: destCoords.latitude,\n        lng: destCoords.longitude\n      };\n\n      // Center the map between the two points\n      this.center = {\n        lat: (originCoords.latitude + destCoords.latitude) / 2,\n        lng: (originCoords.longitude + destCoords.longitude) / 2\n      };\n\n      // Calculate zoom level based on distance\n      const distance = this.calculateDistance(\n        originCoords.latitude, originCoords.longitude,\n        destCoords.latitude, destCoords.longitude\n      );\n\n      this.zoom = this.calculateZoomLevel(distance);\n\n      // Get directions\n      this.getDirections(this.originMarker, this.destinationMarker);\n\n    } catch (error) {\n      console.error('Error updating map:', error);\n    }\n  }\n\n  private getDirections(origin: google.maps.LatLngLiteral, destination: google.maps.LatLngLiteral): void {\n    const request: google.maps.DirectionsRequest = {\n      origin: origin,\n      destination: destination,\n      travelMode: google.maps.TravelMode.DRIVING\n    };\n\n    this.directionsResults$ = this.mapDirectionsService.route(request).pipe(\n      map(response => {\n        return response.result;\n      })\n    );\n  }\n\n  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {\n    // Haversine formula to calculate distance between two points\n    const R = 6371; // Radius of the earth in km\n    const dLat = this.deg2rad(lat2 - lat1);\n    const dLon = this.deg2rad(lon2 - lon1);\n    const a =\n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *\n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    const distance = R * c; // Distance in km\n    return distance;\n  }\n\n  private deg2rad(deg: number): number {\n    return deg * (Math.PI/180);\n  }\n\n  private calculateZoomLevel(distance: number): number {\n    // Adjust zoom level based on distance\n    if (distance > 1000) return 4;\n    if (distance > 500) return 5;\n    if (distance > 200) return 6;\n    if (distance > 100) return 7;\n    if (distance > 50) return 8;\n    if (distance > 20) return 9;\n    if (distance > 10) return 10;\n    if (distance > 5) return 11;\n    if (distance > 2) return 12;\n    if (distance > 1) return 13;\n    if (distance > 0.5) return 14;\n    return 15;\n  }\n\n  private updateDirectionsUrl(): void {\n    if (!this.origin || !this.destination) return;\n\n    let originStr = typeof this.origin === 'string' ? this.origin : `${this.origin.latitude},${this.origin.longitude}`;\n    let destinationStr = typeof this.destination === 'string' ? this.destination : `${this.destination.latitude},${this.destination.longitude}`;\n\n    this.directionsUrl = this.locationService.getGoogleMapsDirectionsUrl(originStr, destinationStr);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOM,IAAO,0BAAP,MAAO,yBAAuB;EAIO;EAHjC,WAAW;EACX,iBAAuC;EAE/C,YAAyC,YAAkB;AAAlB,SAAA,aAAA;EAAqB;;;;EAK9D,oBAAiB;AAEf,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC,aAAO,QAAQ,QAAO;IACxB;AAGA,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK;IACd;AAGA,QAAI,KAAK,UAAU;AACjB,aAAO,QAAQ,QAAO;IACxB;AAGA,SAAK,iBAAiB,IAAI,QAAc,CAAC,SAAS,WAAU;AAE1D,UAAI,OAAO,UAAU,OAAO,OAAO,MAAM;AACvC,aAAK,WAAW;AAChB,gBAAO;AACP;MACF;AAGA,YAAM,eAAe,yBAAyB,KAAK,MAAM,KAAK,OAAM,IAAK,GAAO,CAAC;AAGhF,aAAe,YAAY,IAAI,MAAK;AACnC,aAAK,WAAW;AAChB,gBAAO;AACP,eAAQ,OAAe,YAAY;MACrC;AAGA,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,aAAO,MAAM,+CAA+C,YAAY,gBAAgB,8BAA8B,YAAY;AAClI,aAAO,QAAQ;AACf,aAAO,QAAQ;AACf,aAAO,UAAU,CAAC,UAAS;AACzB,eAAO,IAAI,MAAM,mCAAmC,KAAK,EAAE,CAAC;AAC5D,eAAQ,OAAe,YAAY;MACrC;AAGA,eAAS,KAAK,YAAY,MAAM;IAClC,CAAC;AAED,WAAO,KAAK;EACd;;;;EAKA,qBAAkB;AAChB,WAAO,KAAK;EACd;;qCAlEW,0BAAuB,mBAId,WAAW,CAAA;EAAA;4EAJpB,0BAAuB,SAAvB,yBAAuB,WAAA,YAFtB,OAAM,CAAA;;;sEAEP,yBAAuB,CAAA;UAHnC;WAAW;MACV,YAAY;KACb;;UAKc;WAAO,WAAW;;;;;ACQ3B,IAAO,kBAAP,MAAO,iBAAe;EAIN;EAHZ,yBAAyB,IAAI,gBAAoC,IAAI;EAC7E,mBAAmB,KAAK,uBAAuB,aAAY;EAE3D,YAAoB,kBAAyC;AAAzC,SAAA,mBAAA;EAA4C;;;;EAKhE,qBAAkB;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,UAAI,CAAC,UAAU,aAAa;AAC1B,eAAO,IAAI,MAAM,8CAA8C,CAAC;AAChE;MACF;AAEA,gBAAU,YAAY,mBACpB,CAAC,aAAY;AACX,cAAM,SAAsB;UAC1B,UAAU,SAAS,OAAO;UAC1B,WAAW,SAAS,OAAO;;AAE7B,aAAK,uBAAuB,KAAK,MAAM;AACvC,gBAAQ,MAAM;MAChB,GACA,CAAC,UAAS;AACR,eAAO,KAAK;MACd,GACA,EAAE,oBAAoB,MAAM,SAAS,KAAO,YAAY,EAAC,CAAE;IAE/D,CAAC;EACH;;;;;EAMM,eAAe,SAAe;;AAClC,UAAI;AACF,cAAM,KAAK,iBAAiB,kBAAiB;AAE7C,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,cAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,CAAC,OAAO,KAAK,UAAU;AACpD,oBAAQ,KAAK,qDAAqD;AAElE,kBAAM,WAAW,MAAM,KAAK,OAAM,IAAK,KAAK;AAC5C,kBAAM,YAAY,OAAO,KAAK,OAAM,IAAK,KAAK;AAE9C,oBAAQ,EAAE,UAAU,UAAS,CAAE;AAC/B;UACF;AAEA,gBAAM,WAAW,IAAI,OAAO,KAAK,SAAQ;AAEzC,mBAAS,QAAQ,EAAE,QAAO,GAAI,CAAC,SAAS,WAAU;AAChD,gBAAI,WAAW,OAAO,KAAK,eAAe,MAAM,WAAW,QAAQ,SAAS,GAAG;AAC7E,oBAAM,WAAW,QAAQ,CAAC,EAAE,SAAS;AACrC,sBAAQ;gBACN,UAAU,SAAS,IAAG;gBACtB,WAAW,SAAS,IAAG;eACxB;YACH,OAAO;AACL,sBAAQ,KAAK,iCAAiC,OAAO,aAAa,MAAM,EAAE;AAE1E,oBAAM,WAAW,MAAM,KAAK,OAAM,IAAK,KAAK;AAC5C,oBAAM,YAAY,OAAO,KAAK,OAAM,IAAK,KAAK;AAE9C,sBAAQ,EAAE,UAAU,UAAS,CAAE;YACjC;UACF,CAAC;QACH,CAAC;MACH,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AAEtD,eAAO;UACL,UAAU,MAAM,KAAK,OAAM,IAAK,KAAK;UACrC,WAAW,OAAO,KAAK,OAAM,IAAK,KAAK;;MAE3C;IACF;;;;;;EAMM,eAAe,QAA8B,aAAiC;;AAClF,UAAI;AACF,cAAM,KAAK,iBAAiB,kBAAiB;AAE7C,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AAErC,cAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,CAAC,OAAO,KAAK,mBAAmB;AAC7D,oBAAQ,KAAK,8DAA8D;AAE3E,kBAAM,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;AAElD,kBAAM,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;AAElD,oBAAQ;cACN;cACA;cACA,UAAU;aACX;AACD;UACF;AAEA,gBAAM,oBAAoB,IAAI,OAAO,KAAK,kBAAiB;AAG3D,gBAAM,YAAY,OAAO,WAAW,WAAW,SAAS,GAAG,OAAO,QAAQ,IAAI,OAAO,SAAS;AAC9F,gBAAM,iBAAiB,OAAO,gBAAgB,WAAW,cAAc,GAAG,YAAY,QAAQ,IAAI,YAAY,SAAS;AAEvH,gBAAM,UAAyC;YAC7C,QAAQ;YACR,aAAa;YACb,YAAY,OAAO,KAAK,WAAW;;AAGrC,4BAAkB,MAAM,SAAS,CAAC,QAAQ,WAAU;AAClD,gBAAI,WAAW,OAAO,KAAK,iBAAiB,MAAM,QAAQ;AACxD,oBAAM,QAAQ,OAAO,OAAO,CAAC;AAC7B,kBAAI,SAAS,MAAM,QAAQ,MAAM,KAAK,SAAS,GAAG;AAChD,sBAAM,MAAM,MAAM,KAAK,CAAC;AAGxB,sBAAM,kBAAkB,IAAI,WAAW,IAAI,SAAS,QAAQ,UAAU;AAGtE,sBAAM,oBAAoB,IAAI,WAAW,KAAK,KAAK,IAAI,SAAS,QAAQ,EAAE,IAAI;AAG9E,sBAAM,WAAW,MAAM,oBAAoB,MAAM,oBAAoB;AAErE,wBAAQ;kBACN,UAAU,WAAW,gBAAgB,QAAQ,CAAC,CAAC;kBAC/C,UAAU;kBACV;iBACD;cACH,OAAO;AACL,wBAAQ,KAAK,gBAAgB;AAE7B,wBAAQ;kBACN,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;kBAC3C,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;kBAC3C,UAAU;iBACX;cACH;YACF,OAAO;AACL,sBAAQ,KAAK,sCAAsC,MAAM,EAAE;AAE3D,sBAAQ;gBACN,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;gBAC3C,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;gBAC3C,UAAU;eACX;YACH;UACF,CAAC;QACH,CAAC;MACH,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AAEtD,eAAO;UACL,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;UAC3C,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;UAC3C,UAAU;;MAEd;IACF;;;;;EAKA,iBAAiB,SAAe;AAC9B,WAAO,mDAAmD,mBAAmB,OAAO,CAAC;EACvF;;;;EAKA,2BAA2B,QAAgB,aAAmB;AAC5D,WAAO,iDAAiD,mBAAmB,MAAM,CAAC,gBAAgB,mBAAmB,WAAW,CAAC;EACnI;;qCArLW,kBAAe,mBAAA,uBAAA,CAAA;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YAFd,OAAM,CAAA;;;sEAEP,iBAAe,CAAA;UAH3B;WAAW;MACV,YAAY;KACb;;;;;;;ACMO,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAgD,GAAA,GAAA;AAC3C,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA,EAAI;;;;;AAUnB,IAAA,oBAAA,GAAA,cAAA,CAAA;;;;AAEE,IAAA,qBAAA,YAAA,OAAA,YAAA,EAAyB,SAAA,QAAA,EACP,WAAA,OAAA,aAAA;;;;;AAGpB,IAAA,oBAAA,GAAA,cAAA,CAAA;;;;AAEE,IAAA,qBAAA,YAAA,OAAA,iBAAA,EAA8B,SAAA,aAAA,EACP,WAAA,OAAA,aAAA;;;;;AAGzB,IAAA,oBAAA,GAAA,2BAAA,EAAA;;;;AAEE,IAAA,qBAAA,cAAA,oBAAA;;;;;AArBJ,IAAA,yBAAA,GAAA,cAAA,CAAA;AAOE,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,cAAA,CAAA,EAI4B,GAAA,wDAAA,GAAA,GAAA,cAAA,CAAA,EAMA,GAAA,qEAAA,GAAA,GAAA,2BAAA,CAAA;;AAM9B,IAAA,uBAAA;;;;AAnBE,IAAA,qBAAA,UAAA,OAAA,MAAA,EAAiB,QAAA,OAAA,IAAA,EACJ,WAAA,OAAA,OAAA;AAGV,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA;AAMA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA;AAMA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,sBAAA,GAAA,GAAA,OAAA,kBAAA,CAAA;;;;;AAMP,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6E,GAAA,KAAA,EAAA,EACjC,GAAA,UAAA,EAAA,EACE,GAAA,UAAA;AAC9B,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AACpB,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA,EAAS,EACP;;;;AALD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,uBAAA;;;AAoCP,IAAO,sBAAP,MAAO,qBAAmB;EAyBpB;EACA;EACA;EA1BD;EACA;EACA,qBAA8B;;EAGvC,YAAqB;EACrB,gBAAwB;EACxB,SAAoC,EAAE,KAAK,SAAS,KAAK,QAAQ;;EACjE,OAAO;EACP,UAAkC;IAChC,WAAW;IACX,aAAa;IACb,aAAa;IACb,wBAAwB;IACxB,SAAS;IACT,SAAS;;EAEX;EACA;EACA,kBAAuD;EACvD,gBAA2C,CAAA;EAC3C;EAEA,YACU,iBACA,sBACA,kBAAyC;AAFzC,SAAA,kBAAA;AACA,SAAA,uBAAA;AACA,SAAA,mBAAA;EACP;EAEH,WAAQ;AACN,SAAK,kBAAiB;AACtB,SAAK,oBAAmB;EAC1B;EAEA,YAAY,SAAsB;AAChC,SAAK,QAAQ,QAAQ,KAAK,QAAQ,aAAa,MAAM,KAAK,WAAW;AACnE,WAAK,UAAS;AACd,WAAK,oBAAmB;IAC1B;EACF;EAEA,kBAAe;AACb,QAAI,KAAK,WAAW;AAClB,WAAK,QAAO;IACd;EACF;EAEc,oBAAiB;;AAC7B,UAAI;AACF,cAAM,KAAK,iBAAiB,kBAAiB;AAC7C,aAAK,YAAY;AAEjB,YAAI,OAAO,WAAW,eAAe,OAAO,MAAM;AAChD,eAAK,kBAAkB,OAAO,KAAK;AACnC,eAAK,gBAAgB;YACnB,WAAW,OAAO,KAAK,UAAU;;QAErC;AAEA,aAAK,QAAO;MACd,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,aAAK,YAAY;MACnB;IACF;;EAEQ,UAAO;AACb,QAAI,KAAK,UAAU,KAAK,aAAa;AACnC,WAAK,UAAS;IAChB;EACF;EAEc,YAAS;;AACrB,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK;AAAa;AAEvC,UAAI;AAEF,cAAM,eAAe,OAAO,KAAK,WAAW,WACxC,MAAM,KAAK,gBAAgB,eAAe,KAAK,MAAM,IACrD,KAAK;AAET,cAAM,aAAa,OAAO,KAAK,gBAAgB,WAC3C,MAAM,KAAK,gBAAgB,eAAe,KAAK,WAAW,IAC1D,KAAK;AAGT,aAAK,eAAe;UAClB,KAAK,aAAa;UAClB,KAAK,aAAa;;AAGpB,aAAK,oBAAoB;UACvB,KAAK,WAAW;UAChB,KAAK,WAAW;;AAIlB,aAAK,SAAS;UACZ,MAAM,aAAa,WAAW,WAAW,YAAY;UACrD,MAAM,aAAa,YAAY,WAAW,aAAa;;AAIzD,cAAM,WAAW,KAAK,kBACpB,aAAa,UAAU,aAAa,WACpC,WAAW,UAAU,WAAW,SAAS;AAG3C,aAAK,OAAO,KAAK,mBAAmB,QAAQ;AAG5C,aAAK,cAAc,KAAK,cAAc,KAAK,iBAAiB;MAE9D,SAAS,OAAO;AACd,gBAAQ,MAAM,uBAAuB,KAAK;MAC5C;IACF;;EAEQ,cAAc,QAAmC,aAAsC;AAC7F,UAAM,UAAyC;MAC7C;MACA;MACA,YAAY,OAAO,KAAK,WAAW;;AAGrC,SAAK,qBAAqB,KAAK,qBAAqB,MAAM,OAAO,EAAE,KACjE,IAAI,cAAW;AACb,aAAO,SAAS;IAClB,CAAC,CAAC;EAEN;EAEQ,kBAAkB,MAAc,MAAc,MAAc,MAAY;AAE9E,UAAM,IAAI;AACV,UAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AACrC,UAAM,OAAO,KAAK,QAAQ,OAAO,IAAI;AACrC,UAAM,IACJ,KAAK,IAAI,OAAK,CAAC,IAAI,KAAK,IAAI,OAAK,CAAC,IAClC,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC,IAC1D,KAAK,IAAI,OAAK,CAAC,IAAI,KAAK,IAAI,OAAK,CAAC;AACpC,UAAM,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,IAAE,CAAC,CAAC;AACrD,UAAM,WAAW,IAAI;AACrB,WAAO;EACT;EAEQ,QAAQ,KAAW;AACzB,WAAO,OAAO,KAAK,KAAG;EACxB;EAEQ,mBAAmB,UAAgB;AAEzC,QAAI,WAAW;AAAM,aAAO;AAC5B,QAAI,WAAW;AAAK,aAAO;AAC3B,QAAI,WAAW;AAAK,aAAO;AAC3B,QAAI,WAAW;AAAK,aAAO;AAC3B,QAAI,WAAW;AAAI,aAAO;AAC1B,QAAI,WAAW;AAAI,aAAO;AAC1B,QAAI,WAAW;AAAI,aAAO;AAC1B,QAAI,WAAW;AAAG,aAAO;AACzB,QAAI,WAAW;AAAG,aAAO;AACzB,QAAI,WAAW;AAAG,aAAO;AACzB,QAAI,WAAW;AAAK,aAAO;AAC3B,WAAO;EACT;EAEQ,sBAAmB;AACzB,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK;AAAa;AAEvC,QAAI,YAAY,OAAO,KAAK,WAAW,WAAW,KAAK,SAAS,GAAG,KAAK,OAAO,QAAQ,IAAI,KAAK,OAAO,SAAS;AAChH,QAAI,iBAAiB,OAAO,KAAK,gBAAgB,WAAW,KAAK,cAAc,GAAG,KAAK,YAAY,QAAQ,IAAI,KAAK,YAAY,SAAS;AAEzI,SAAK,gBAAgB,KAAK,gBAAgB,2BAA2B,WAAW,cAAc;EAChG;;qCA9KW,sBAAmB,4BAAA,eAAA,GAAA,4BAAA,oBAAA,GAAA,4BAAA,uBAAA,CAAA;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,aAAA,eAAA,oBAAA,qBAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,UAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,UAAA,SAAA,SAAA,QAAA,GAAA,UAAA,QAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,UAAA,SAAA,SAAA,QAAA,GAAA,UAAA,QAAA,SAAA,GAAA,CAAA,GAAA,YAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,UAAA,UAAA,GAAA,MAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,SAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AArE5B,MAAA,yBAAA,GAAA,YAAA,CAAA,EAA2B,GAAA,kBAAA;AAEvB,MAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAGA,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,cAAA,CAAA;AAwBF,MAAA,uBAAA;AAEA,MAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAQF,MAAA,uBAAA,EAAmB;;;AAtCX,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,SAAA;AAGqB,MAAA,oBAAA;AAAA,MAAA,sBAAA,WAAA,IAAA,YAAA,UAAA,MAAA;AAEtB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;AAyBqB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,sBAAA,IAAA,UAAA,IAAA,WAAA;;oBAvC9B,cAAY,MAAA,WACZ,eAAa,SAAA,gBACb,iBAAe,WACf,eAAa,SACb,kBAAgB,WAAA,uBAAA,SAAA,GAAA,QAAA,CAAA,ydAAA,EAAA,CAAA;;;sEAwEP,qBAAmB,CAAA;UAhF/B;uBACW,mBAAiB,YACf,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2CT,QAAA,CAAA,0lBAAA,EAAA,CAAA;8GA4BQ,QAAM,CAAA;UAAd;MACQ,aAAW,CAAA;UAAnB;MACQ,oBAAkB,CAAA;UAA1B;;;;6EAHU,qBAAmB,EAAA,WAAA,uBAAA,UAAA,kEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}