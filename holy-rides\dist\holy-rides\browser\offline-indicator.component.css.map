{"version": 3, "sources": ["src/app/shared/components/offline-indicator/offline-indicator.component.ts"], "sourcesContent": ["\n    .offline-container {\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      padding: 16px;\n    }\n\n    .offline-card {\n      background-color: #f44336;\n      color: white;\n      text-align: center;\n    }\n\n    .offline-subtitle {\n      font-size: 14px;\n      opacity: 0.8;\n    }\n\n    mat-icon {\n      font-size: 48px;\n      height: 48px;\n      width: 48px;\n      margin-bottom: 8px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,WAAA;AACA,WAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,aAAA;AACA,WAAA;;AAGF;AACE,aAAA;AACA,UAAA;AACA,SAAA;AACA,iBAAA;;", "names": []}