{"version": 3, "sources": ["src/app/features/dashboard/driver/driver-earnings/driver-earnings.component.ts"], "sourcesContent": ["\n    .earnings-container {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      padding: 20px;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .summary-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-top: 16px;\n    }\n\n    .summary-item {\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      padding: 16px;\n      text-align: center;\n    }\n\n    .summary-label {\n      font-size: 0.9em;\n      color: rgba(0, 0, 0, 0.6);\n      margin-bottom: 8px;\n    }\n\n    .summary-value {\n      font-size: 1.8em;\n      font-weight: 500;\n      color: #3f51b5;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .no-payouts {\n      text-align: center;\n      padding: 20px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .payouts-table {\n      width: 100%;\n    }\n\n    .status-chip {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 16px;\n      font-size: 0.85em;\n      text-transform: capitalize;\n    }\n\n    .status-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .status-paid {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,WAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,oBAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,iBAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,kBAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;", "names": []}