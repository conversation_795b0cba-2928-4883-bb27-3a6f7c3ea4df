{"version": 3, "sources": ["src/app/shared/components/ride-detail/ride-detail.component.scss"], "sourcesContent": ["mat-card {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n  margin-top: 180px;\n}\n\n.close-button {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n}\n\n.ride-details {\n  margin: 20px 0;\n}\n\n.detail-row {\n  display: flex;\n  margin-bottom: 10px;\n}\n\n.label {\n  font-weight: 500;\n  width: 120px;\n  color: #666;\n}\n\n.value {\n  flex: 1;\n}\n\n.status-badge {\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.9em;\n  font-weight: 500;\n}\n\n.status-requested {\n  background-color: #f0f0f0;\n  color: #666;\n}\n\n.status-assigned {\n  background-color: #e3f2fd;\n  color: #1976d2;\n}\n\n.status-in-progress {\n  background-color: #e8f5e9;\n  color: #388e3c;\n}\n\n.status-completed {\n  background-color: #e8f5e9;\n  color: #388e3c;\n}\n\n.status-canceled {\n  background-color: #ffebee;\n  color: #d32f2f;\n}\n\n// Payment status styles\n.payment-pending {\n  background-color: #fff8e1;\n  color: #f57c00;\n}\n\n.payment-completed {\n  background-color: #e8f5e9;\n  color: #388e3c;\n}\n\n.payment-paid {\n  background-color: #e8f5e9;\n  color: #388e3c;\n}\n\n.payment-failed {\n  background-color: #ffebee;\n  color: #d32f2f;\n}\n\n.payment-refunded {\n  background-color: #e0f7fa;\n  color: #0097a7;\n}\n\n.section-divider {\n  margin: 20px 0;\n}\n\n.map-container {\n  margin-top: 20px;\n}\n\n.rating-container {\n  padding: 20px 0;\n}\n\n.already-rated-message {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  background-color: #f5f5f5;\n  border-radius: 4px;\n}\n\n.already-rated-message mat-icon {\n  margin-right: 10px;\n}\n\n// Admin fare input styles\n.admin-fare-input {\n  margin-top: 20px;\n  display: flex;\n  align-items: start;\n  justify-self: start;\n\n}\n.mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix {\n  padding-top: var(8px);\n  padding-bottom: var(8px);\n}\n.mat-mdc-form-field-infix {\n  min-height: 40px !important;}\n.admin-input-container {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.fare-input {\n  width: 150px;  // Set a fixed width for the input\n}\n\n// Make sure the input is not too wide\nmat-form-field {\n  width: 150px;\n}\n"], "mappings": ";AAAA;AACE,aAAA;AACA,UAAA,EAAA;AACA,WAAA;AACA,cAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,iBAAA;;AAGF,CAAA;AACE,eAAA;AACA,SAAA;AACA,SAAA;;AAGF,CAAA;AACE,QAAA;;AAGF,CAAA;AACE,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAIF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA,KAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA;AACA,oBAAA;AACA,iBAAA;;AAGF,CARA,sBAQA;AACE,gBAAA;;AAIF,CAAA;AACE,cAAA;AACA,WAAA;AACA,eAAA;AACA,gBAAA;;AAGF,CAAA,yBAAA,CAAA;AAAA,CAAA,yBAAA,CAAA;AACE,eAAA,IAAA;AACA,kBAAA,IAAA;;AAEF,CAJA;AAKE,cAAA;;AACF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA;;AAIF;AACE,SAAA;;", "names": []}