import{a as Fe,b as Oe,c as qe,d as Be,e as ze}from"./chunk-HNQXKFUQ.js";import{A as Te,j as le,o as L,v as be,w as we,x as Ee,y as Ie,z as De}from"./chunk-XDJELRYM.js";import"./chunk-MS4AQ6UA.js";import{b as N,c as <PERSON>,d as He,e as Ge,g as <PERSON>,h as We,i as Je,j as Xe,k as Ye,l as Ze,m as et,n as tt,o as it,p as nt,q as rt,r as ot,s as at,t as st,u as ct,w as dt,x as mt}from"./chunk-4GJZFU7Z.js";import{a as V,b as Ae,c as Ue,d as je,e as $e}from"./chunk-4XM5VEPX.js";import{a as Ve,b as Ne}from"./chunk-OFTCLERB.js";import{a as Qe}from"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as F,b as O}from"./chunk-WSXVBUWR.js";import"./chunk-3NZGXQSR.js";import"./chunk-ZN5FMN3P.js";import"./chunk-YTNZ52NK.js";import{a as T,b as z}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{c as me}from"./chunk-3EEDYH74.js";import{C as Me,F as Re,H as ke,I as Pe,J as q,b as pe,d as U,f as ue,g as fe,k as _e,n as ge,o as ye,s as Ce,u as he,w as ve,x as xe,z as Se}from"./chunk-AG3SD6JT.js";import{Bc as de,Bd as j,Eb as ne,Fb as re,Fc as I,Gb as oe,Gd as $,Jb as Y,Jd as D,Kb as a,Kd as Z,La as o,Lb as v,Ld as H,Mb as x,Md as G,Nd as Q,Pa as u,Qd as K,Rd as B,Wa as w,Wb as ae,Yb as se,ab as l,hb as c,kb as X,la as y,ma as C,qb as n,rb as r,sb as f,tb as b,tc as ce,ub as k,va as ie,vc as E,wb as M,yb as h,zb as m}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as ee,b as te,i as g}from"./chunk-ODN5LVDJ.js";function St(i,t){i&1&&(n(0,"mat-error"),a(1," Pickup location is required "),r())}function Mt(i,t){i&1&&(n(0,"mat-error"),a(1," Dropoff location is required "),r())}function Rt(i,t){if(i&1&&(n(0,"p"),a(1),r()),i&2){let e=m(3);o(),x("Distance: ",e.estimatedDistance," miles")}}function bt(i,t){if(i&1&&(n(0,"p"),a(1),r()),i&2){let e=m(3);o(),x("Duration: ",e.estimatedDuration," minutes")}}function kt(i,t){if(i&1&&(n(0,"div",17)(1,"p"),a(2,"Estimated fare: "),n(3,"strong"),a(4),r()(),l(5,Rt,2,1,"p",7)(6,bt,2,1,"p",7),r()),i&2){let e=m(2);o(4),v(e.estimatedFare?"$"+e.estimatedFare.toFixed(2):""),o(),c("ngIf",e.estimatedDistance),o(),c("ngIf",e.estimatedDuration)}}function Pt(i,t){if(i&1&&(n(0,"div"),f(1,"app-map-display",15),l(2,kt,7,3,"div",16),r()),i&2){let e,s,d=m();o(),c("origin",(e=d.rideForm.get("pickup_location"))==null?null:e.value)("destination",(s=d.rideForm.get("dropoff_location"))==null?null:s.value),o(),c("ngIf",d.estimatedFare)}}function wt(i,t){i&1&&(n(0,"mat-error"),a(1," Pickup date is required "),r())}function Et(i,t){i&1&&(n(0,"mat-error"),a(1," Pickup time is required "),r())}var W=class i{constructor(t,e,s,d,p,_){this.formBuilder=t;this.rideService=e;this.authService=s;this.locationService=d;this.paymentService=p;this.snackBar=_;this.rideForm=this.formBuilder.group({pickup_location:["",U.required],dropoff_location:["",U.required],pickup_date:[new Date,U.required],pickup_time:["12:00 PM",U.required]})}rideForm;loading=!1;showMap=!1;estimatedFare=null;estimatedDistance=null;estimatedDuration=null;locationCoordinates={};ngOnInit(){this.rideForm.get("pickup_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()}),this.rideForm.get("dropoff_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()})}useCurrentLocation(){return g(this,null,function*(){try{let t=yield this.locationService.getCurrentLocation();this.rideForm.patchValue({pickup_location:`Current Location (${t.latitude.toFixed(6)}, ${t.longitude.toFixed(6)})`}),this.locationCoordinates.pickup=t,this.snackBar.open("Current location detected","Close",{duration:2e3})}catch(t){this.snackBar.open(t.message||"Failed to get current location","Close",{duration:3e3})}})}updateRouteEstimates(){return g(this,null,function*(){let t=this.rideForm.get("pickup_location")?.value,e=this.rideForm.get("dropoff_location")?.value;if(t&&e){this.showMap=!0;try{let{fare:s,routeInfo:d}=yield this.paymentService.estimateFare(t,e);this.estimatedFare=s,this.estimatedDistance=d.distance,this.estimatedDuration=d.duration}catch(s){console.error("Error calculating route:",s)}}else this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null})}onSubmit(){return g(this,null,function*(){if(!this.rideForm.invalid){this.loading=!0;try{let t=yield this.authService.getCurrentUser();if(!t)throw new Error("User not found");this.locationCoordinates.pickup||(this.locationCoordinates.pickup=yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location)),this.locationCoordinates.dropoff||(this.locationCoordinates.dropoff=yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location));let e=yield this.locationService.calculateRoute(this.locationCoordinates.pickup,this.locationCoordinates.dropoff);console.log(e);let s=this.rideForm.value.pickup_date,d=this.rideForm.value.pickup_time,p=new Date(s),_=d.match(/(\d+):(\d+)\s?(AM|PM)?/i);if(_){let S=parseInt(_[1],10),P=parseInt(_[2],10),A=_[3]?_[3].toUpperCase():null;A==="PM"&&S<12?S+=12:A==="AM"&&S===12&&(S=0),p.setHours(S,P,0,0)}let R=te(ee({},this.rideForm.value),{rider_id:t.id,status:"requested",pickup_time:p.toISOString(),pickup_latitude:this.locationCoordinates.pickup?.latitude,pickup_longitude:this.locationCoordinates.pickup?.longitude,dropoff_latitude:this.locationCoordinates.dropoff?.latitude,dropoff_longitude:this.locationCoordinates.dropoff?.longitude,distance_miles:e.distance,duration_minutes:e.duration,fare:this.estimatedFare||(yield this.paymentService.estimateFare(this.rideForm.value.pickup_location,this.rideForm.value.dropoff_location))});yield this.rideService.createRide(R),this.snackBar.open("Ride requested successfully!","Close",{duration:3e3}),this.rideForm.reset({passengers:1,pickup_date:new Date,pickup_time:"12:00 PM"}),this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null,this.locationCoordinates={}}catch(t){this.snackBar.open(t.message||"Failed to request ride","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(e){return new(e||i)(u(Ce),u(V),u(q),u(Ve),u(N),u(T))};static \u0275cmp=w({type:i,selectors:[["app-ride-request"]],decls:42,vars:12,consts:[["picker",""],["timepicker",""],[3,"ngSubmit","formGroup"],[1,"location-fields"],["appearance","outline"],["matInput","","formControlName","pickup_location","placeholder","Enter pickup location"],["mat-icon-button","","matSuffix","","type","button","title","Use current location",3,"click"],[4,"ngIf"],["matInput","","formControlName","dropoff_location","placeholder","Enter dropoff location"],["matInput","","formControlName","pickup_date",3,"matDatepicker"],["matSuffix","",3,"for"],["matInput","","formControlName","pickup_time",3,"ngxMatTimepicker"],["ngxMatTimepickerToggleIcon",""],[1,"button-container"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[3,"origin","destination"],["class","fare-estimate",4,"ngIf"],[1,"fare-estimate"]],template:function(e,s){if(e&1){let d=M();n(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),a(3,"Request a Ride"),r()(),n(4,"mat-card-content")(5,"form",2),h("ngSubmit",function(){return y(d),C(s.onSubmit())}),n(6,"div",3)(7,"mat-form-field",4)(8,"mat-label"),a(9,"Pickup Location"),r(),f(10,"input",5),n(11,"button",6),h("click",function(){return y(d),C(s.useCurrentLocation())}),n(12,"mat-icon"),a(13,"my_location"),r()(),l(14,St,2,0,"mat-error",7),r(),n(15,"mat-form-field",4)(16,"mat-label"),a(17,"Dropoff Location"),r(),f(18,"input",8),l(19,Mt,2,0,"mat-error",7),r()(),l(20,Pt,3,3,"div",7),n(21,"mat-form-field",4)(22,"mat-label"),a(23,"Pickup Date"),r(),f(24,"input",9)(25,"mat-datepicker-toggle",10)(26,"mat-datepicker",null,0),l(28,wt,2,0,"mat-error",7),r(),n(29,"mat-form-field",4)(30,"mat-label"),a(31,"Pick a time"),r(),f(32,"input",11),n(33,"ngx-mat-timepicker-toggle",10)(34,"mat-icon",12),a(35,"keyboard_arrow_down"),r()(),f(36,"ngx-mat-timepicker",null,1),l(38,Et,2,0,"mat-error",7),r(),n(39,"div",13)(40,"button",14),a(41),r()()()()()}if(e&2){let d,p,_,R,S,P=Y(27),A=Y(37);o(5),c("formGroup",s.rideForm),o(9),c("ngIf",(d=s.rideForm.get("pickup_location"))==null||d.errors==null?null:d.errors.required),o(5),c("ngIf",(p=s.rideForm.get("dropoff_location"))==null||p.errors==null?null:p.errors.required),o(),c("ngIf",s.showMap&&((_=s.rideForm.get("pickup_location"))==null?null:_.value)&&((_=s.rideForm.get("dropoff_location"))==null?null:_.value)),o(4),c("matDatepicker",P),o(),c("for",P),o(3),c("ngIf",(R=s.rideForm.get("pickup_date"))==null||R.errors==null?null:R.errors.required),o(4),c("ngxMatTimepicker",A),o(),c("for",A),o(5),c("ngIf",(S=s.rideForm.get("pickup_time"))==null||S.errors==null?null:S.errors.required),o(2),c("disabled",s.rideForm.invalid||s.loading),o(),x(" ",s.loading?"Requesting...":"Request Ride"," ")}},dependencies:[I,E,he,_e,pe,ue,fe,ge,ye,B,H,Q,K,G,Re,Me,ve,xe,Se,Pe,ke,D,$,j,Be,Fe,Oe,qe,be,O,F,Ne,Te,we,Ie,De,Ee],styles:["[_nghost-%COMP%]{display:block;margin:20px}form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;max-width:600px;margin:0 auto}.location-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:16px}textarea[_ngcontent-%COMP%]{min-height:100px}.fare-estimate[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;margin-top:16px;margin-bottom:16px}.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}"],changeDetection:0})};var It=["cardElement"];function Dt(i,t){i&1&&f(0,"mat-divider",13)}function Tt(i,t){i&1&&(n(0,"div",14)(1,"p"),a(2,"Loading payment form..."),r(),f(3,"mat-spinner",15),r())}function Ft(i,t){if(i&1&&(n(0,"div",23),a(1),r()),i&2){let e=m(3);o(),v(e.cardError)}}function Ot(i,t){i&1&&(n(0,"div",24),f(1,"mat-spinner",25),n(2,"span"),a(3,"Processing your payment..."),r()())}function qt(i,t){if(i&1){let e=M();n(0,"div",16)(1,"h3"),a(2,"Payment Information"),r(),n(3,"p",17),a(4,"Please enter your card details to complete the payment."),r(),f(5,"div",18,0),l(7,Ft,2,1,"div",19),n(8,"div",20)(9,"button",21),h("click",function(){y(e);let d=m(2);return C(d.processPayment())}),n(10,"mat-icon"),a(11,"payment"),r(),a(12),r(),l(13,Ot,4,0,"div",22),r()()}if(i&2){let e=m(2);o(7),c("ngIf",e.cardError),o(2),c("disabled",e.processing||!e.cardComplete),o(3),x(" ",e.processing?"Processing...":"Pay Now"," "),o(),c("ngIf",e.processing)}}function Bt(i,t){i&1&&(n(0,"div",24),f(1,"mat-spinner",25),n(2,"span"),a(3,"Processing your request..."),r()())}function Vt(i,t){if(i&1){let e=M();n(0,"div",20)(1,"button",26),h("click",function(){y(e);let d=m(2);return C(d.requestRefund())}),n(2,"mat-icon"),a(3,"money_off"),r(),a(4),r(),l(5,Bt,4,0,"div",22),r()}if(i&2){let e=m(2);o(),c("disabled",e.processing),o(3),x(" ",e.processing?"Processing...":"Request Refund"," "),o(),c("ngIf",e.processing)}}function Nt(i,t){if(i&1&&(n(0,"div",27)(1,"h3"),a(2,"Payment Result"),r(),n(3,"div",28),a(4),r()()),i&2){let e=m(2);o(3),c("ngClass",e.paymentResult.success?"success-message":"error-message"),o(),x(" ",e.paymentResult.success?"Payment successful!":"Payment failed: "+(e.paymentResult.error==null?null:e.paymentResult.error.message)," ")}}function At(i,t){if(i&1&&(n(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),a(3,"Ride Payment"),r()(),n(4,"mat-card-content")(5,"div",2)(6,"div",3)(7,"span",4),a(8,"Pickup:"),r(),n(9,"span",5),a(10),r()(),n(11,"div",3)(12,"span",4),a(13,"Destination:"),r(),n(14,"span",5),a(15),r()(),n(16,"div",3)(17,"span",4),a(18,"Date:"),r(),n(19,"span",5),a(20),ae(21,"date"),r()(),n(22,"div",3)(23,"span",4),a(24,"Status:"),r(),n(25,"span",6),a(26),r()(),n(27,"div",3)(28,"span",4),a(29,"Payment Status:"),r(),n(30,"span",6),a(31),r()(),n(32,"div",7)(33,"span",4),a(34,"Amount:"),r(),n(35,"span",5),a(36),r()()(),l(37,Dt,1,0,"mat-divider",8)(38,Tt,4,0,"div",9)(39,qt,14,4,"div",10)(40,Vt,6,3,"div",11)(41,Nt,5,2,"div",12),r()()),i&2){let e=m();o(10),v(e.ride.pickup_location),o(5),v(e.ride.dropoff_location),o(5),v(se(21,13,e.ride.pickup_time,"medium")),o(5),c("ngClass","status-"+e.ride.status),o(),v(e.ride.status),o(4),c("ngClass","payment-"+(e.ride.payment_status||"pending")),o(),x(" ",e.ride.payment_status||"pending"," "),o(5),v("$"+e.getDisplayAmount()),o(),c("ngIf",e.canPay()),o(),c("ngIf",e.canPay()&&!e.sdkLoaded),o(),c("ngIf",e.canPay()&&e.sdkLoaded),o(),c("ngIf",e.canRequestRefund()),o(),c("ngIf",e.paymentResult)}}var J=class i{constructor(t,e,s,d){this.paymentService=t;this.rideService=e;this.snackBar=s;this.authService=d}ride;cardElement;paymentCompleted=new ie;processing=!1;stripe;card;sdkLoaded=!1;cardError="";cardComplete=!1;paymentResult=null;ngOnInit(){return g(this,null,function*(){this.ride&&!this.ride.amount&&!this.ride.fare&&this.estimateFare(),this.loadStripeScript();let t=yield this._loadStripe(Z.stripePublishableKey)})}ngAfterViewInit(){this.initializeCard()}_loadStripe(t){return g(this,null,function*(){let e=yield ze(t);this.sdkLoaded=!0})}loadStripeScript(){if(window.Stripe){this.initializeStripe();return}let t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,t.onload=()=>{this.initializeStripe()},document.body.appendChild(t)}initializeStripe(){if(!window.Stripe){this.snackBar.open("Stripe SDK not available","Close",{duration:3e3});return}try{this.stripe=window.Stripe(Z.stripePublishableKey),setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error initializing Stripe:",t),this.snackBar.open("Error initializing Stripe payments. Check your credentials.","Close",{duration:5e3})}}initializeCard(){if(!this.cardElement||!this.cardElement.nativeElement||!this.stripe){setTimeout(()=>this.initializeCard(),100);return}try{let t=this.stripe.elements();this.card=t.create("card",{style:{base:{iconColor:"#666EE8",color:"#31325F",fontWeight:400,fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSize:"16px","::placeholder":{color:"#CFD7E0"}}}}),this.card.mount(this.cardElement.nativeElement),this.card.on("change",e=>{this.cardError=e.error?e.error.message:"",this.cardComplete=e.complete}),this.sdkLoaded=!0}catch(t){console.error("Error initializing Stripe card:",t),this.snackBar.open("Error initializing Stripe card form","Close",{duration:5e3})}}calculateAmount(){return 15}estimateFare(){return g(this,null,function*(){if(this.ride)try{let{fare:t}=yield this.paymentService.estimateFare(this.ride.pickup_location,this.ride.dropoff_location);yield this.rideService.updateRide(this.ride.id,{fare:t})}catch(t){console.error("Error estimating fare:",t)}})}canPay(){return this.ride?this.ride.status==="completed"&&(!this.ride.payment_status||this.ride.payment_status==="pending"||this.ride.payment_status==="failed"):!1}canRequestRefund(){return this.ride?this.ride.payment_status==="paid"||this.ride.payment_status==="completed":!1}processPayment(){return g(this,null,function*(){if(!(!this.ride||!this.canPay()||!this.stripe||!this.card)){this.processing=!0,this.paymentResult=null;try{let t=this.ride.amount||this.ride.fare||this.calculateAmount(),{paymentMethod:e,error:s}=yield this.stripe.createPaymentMethod({type:"card",card:this.card});if(s||s)throw s;let d={amount:t*100,currency:"usd",description:"Customer pamyment for ride",payment_method:e.id};console.log(d);let{data:p,error:_}=yield this.authService.supabase.functions.invoke("stripe",{body:d});if(_)throw console.error("Error creating payment intent:",_),new Error(`Failed to create payment intent: ${_.message}`);if(console.log("Payment intent created:",p),!p||!p.client_secret)throw new Error("No client secret returned from payment intent creation");let R=p.client_secret,{error:S,paymentIntent:P}=yield this.stripe.confirmCardPayment(R,{payment_method:e.id});if(S)throw S;this.paymentResult={success:!0,paymentIntent:P},yield this.rideService.updateRide(this.ride.id,{payment_status:"paid",payment_id:P.id,amount:t}),this.snackBar.open("Payment processed successfully!","Close",{duration:3e3}),this.paymentCompleted.emit()}catch(t){console.error("Error processing payment:",t),this.paymentResult={success:!1,error:{message:t.message||"An unknown error occurred"}},this.snackBar.open(`Payment error: ${t.message}`,"Close",{duration:5e3})}finally{this.processing=!1}}})}getDisplayAmount(){return this.ride?(this.ride.amount||this.ride.fare||this.calculateAmount()).toString():"0"}requestRefund(){return g(this,null,function*(){if(!(!this.ride||!this.canRequestRefund())){this.processing=!0,this.paymentResult=null;try{(yield this.paymentService.processRefund(this.ride.id))?(this.paymentResult={success:!0,refund:!0},this.snackBar.open("Refund processed successfully!","Close",{duration:3e3}),this.paymentCompleted.emit()):(this.paymentResult={success:!1,refund:!0,error:{message:"Failed to process refund"}},this.snackBar.open("Refund request failed. Please try again.","Close",{duration:3e3}))}catch(t){console.error("Error processing refund:",t),this.paymentResult={success:!1,refund:!0,error:{message:t.message||"An unknown error occurred"}},this.snackBar.open(`Refund error: ${t.message}`,"Close",{duration:5e3})}finally{this.processing=!1}}})}static \u0275fac=function(e){return new(e||i)(u(N),u(V),u(T),u(q))};static \u0275cmp=w({type:i,selectors:[["app-ride-payment"]],viewQuery:function(e,s){if(e&1&&ne(It,5),e&2){let d;re(d=oe())&&(s.cardElement=d.first)}},inputs:{ride:"ride"},outputs:{paymentCompleted:"paymentCompleted"},decls:1,vars:1,consts:[["cardElement",""],[4,"ngIf"],[1,"payment-details"],[1,"detail-row"],[1,"label"],[1,"value"],[1,"value","status-badge",3,"ngClass"],[1,"detail-row","amount"],["class","section-divider",4,"ngIf"],["class","sdk-status",4,"ngIf"],["class","stripe-payment-form",4,"ngIf"],["class","payment-actions",4,"ngIf"],["class","payment-result",4,"ngIf"],[1,"section-divider"],[1,"sdk-status"],["diameter","30"],[1,"stripe-payment-form"],[1,"payment-instruction"],[1,"card-element"],["class","card-errors",4,"ngIf"],[1,"payment-actions"],["mat-raised-button","","color","primary",3,"click","disabled"],["class","processing-indicator",4,"ngIf"],[1,"card-errors"],[1,"processing-indicator"],["diameter","24"],["mat-raised-button","","color","warn",3,"click","disabled"],[1,"payment-result"],[3,"ngClass"]],template:function(e,s){e&1&&l(0,At,42,16,"mat-card",1),e&2&&c("ngIf",s.ride)},dependencies:[I,ce,E,de,B,H,Q,K,G,D,$,O,F,Ue,Ae,L,z,$e,je],styles:["[_nghost-%COMP%]{display:block;margin:20px}.payment-details[_ngcontent-%COMP%]{margin-bottom:20px}.detail-row[_ngcontent-%COMP%]{display:flex;margin-bottom:8px;align-items:center}.label[_ngcontent-%COMP%]{font-weight:500;width:120px;color:#0009}.value[_ngcontent-%COMP%]{flex:1}.amount[_ngcontent-%COMP%]{font-size:1.2em;font-weight:500;margin-top:16px}.amount[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#3f51b5}.status-badge[_ngcontent-%COMP%]{display:inline-block;padding:4px 8px;border-radius:4px;text-transform:capitalize;font-size:.9em}.status-requested[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3;color:#fff}.status-in-progress[_ngcontent-%COMP%]{background-color:#ff9800;color:#fff}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-pending[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.payment-paid[_ngcontent-%COMP%], .payment-completed[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.payment-failed[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-refunded[_ngcontent-%COMP%]{background-color:#9e9e9e;color:#fff}.payment-actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;align-items:flex-start;margin-top:16px}.processing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-top:8px}.section-divider[_ngcontent-%COMP%]{margin:24px 0}.stripe-payment-form[_ngcontent-%COMP%]{margin-top:24px}.payment-instruction[_ngcontent-%COMP%]{margin-bottom:16px;color:#0009}.card-element[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:4px;padding:12px;background-color:#fff;margin-bottom:16px}.card-errors[_ngcontent-%COMP%]{color:#f44336;font-size:.9em;margin-bottom:16px}.sdk-status[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px;margin:24px 0}.payment-result[_ngcontent-%COMP%]{margin-top:24px;padding:16px;border-radius:4px;background-color:#f5f5f5}.success-message[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.error-message[_ngcontent-%COMP%]{color:#f44336;font-weight:500}"]})};function Ut(i,t){i&1&&(n(0,"th",18),a(1,"Pickup"),r())}function jt(i,t){if(i&1&&(n(0,"td",19),a(1),r()),i&2){let e=t.$implicit;o(),v(e.pickup_location)}}function $t(i,t){i&1&&(n(0,"th",18),a(1,"Dropoff"),r())}function zt(i,t){if(i&1&&(n(0,"td",19),a(1),r()),i&2){let e=t.$implicit;o(),v(e.dropoff_location)}}function Lt(i,t){i&1&&(n(0,"th",18),a(1,"Time"),r())}function Ht(i,t){if(i&1&&(n(0,"td",19),a(1),r()),i&2){let e=t.$implicit,s=m();o(),v(s.formatDate(e.pickup_time))}}function Gt(i,t){i&1&&(n(0,"th",18),a(1,"Status"),r())}function Qt(i,t){if(i&1&&(n(0,"td",19)(1,"mat-chip-listbox")(2,"mat-chip"),a(3),r()()()),i&2){let e=t.$implicit,s=m();o(2),X(s.getStatusClass(e.status)),o(),x(" ",s.formatStatus(e.status)," ")}}function Kt(i,t){i&1&&(n(0,"th",18),a(1,"Payment"),r())}function Wt(i,t){if(i&1&&(n(0,"mat-chip-listbox")(1,"mat-chip"),a(2),r()()),i&2){let e=m().$implicit;o(),X("payment-status-"+e.payment_status),o(),x(" ",e.payment_status," ")}}function Jt(i,t){i&1&&(n(0,"span"),a(1,"-"),r())}function Xt(i,t){if(i&1&&(n(0,"td",19),l(1,Wt,3,3,"mat-chip-listbox",20)(2,Jt,2,0,"span",20),r()),i&2){let e=t.$implicit;o(),c("ngIf",e.payment_status),o(),c("ngIf",!e.payment_status)}}function Yt(i,t){i&1&&(n(0,"th",18),a(1,"Fare"),r())}function Zt(i,t){if(i&1&&(n(0,"td",19),a(1),r()),i&2){let e=t.$implicit;o(),x(" ",e.amount||e.fare?"$"+(e.amount||e.fare).toFixed(2):"-"," ")}}function ei(i,t){i&1&&(n(0,"th",18),a(1,"Actions"),r())}function ti(i,t){if(i&1){let e=M();n(0,"button",24),h("click",function(){y(e);let d=m().$implicit,p=m();return C(p.cancelRide(d.id))}),n(1,"mat-icon"),a(2,"cancel"),r()()}}function ii(i,t){if(i&1){let e=M();n(0,"button",25),h("click",function(){y(e);let d=m().$implicit,p=m();return C(p.viewPayment(d))}),n(1,"mat-icon"),a(2,"payment"),r()()}}function ni(i,t){if(i&1){let e=M();n(0,"td",19),l(1,ti,3,0,"button",21)(2,ii,3,0,"button",22),n(3,"button",23),h("click",function(){let d=y(e).$implicit,p=m();return C(p.viewRideDetails(d.id))}),n(4,"mat-icon"),a(5,"visibility"),r()()()}if(i&2){let e=t.$implicit;o(),c("ngIf",e.status==="requested"),o(),c("ngIf",e.status==="completed")}}function ri(i,t){i&1&&f(0,"tr",26)}function oi(i,t){i&1&&f(0,"tr",27)}function ai(i,t){if(i&1){let e=M();n(0,"div",28)(1,"app-ride-payment",29),h("paymentCompleted",function(){y(e);let d=m();return C(d.closePayment())}),r(),n(2,"button",30),h("click",function(){y(e);let d=m();return C(d.closePayment())}),n(3,"mat-icon"),a(4,"close"),r()()()}if(i&2){let e=m();o(),c("ride",e.selectedRide)}}function si(i,t){if(i&1){let e=M();n(0,"div",31)(1,"app-ride-detail",32),h("paymentRequested",function(d){y(e);let p=m();return C(p.viewPayment(d))})("rideUpdated",function(d){y(e);let p=m();return C(p.onRideUpdated(d))}),r()()}if(i&2){let e=m();o(),c("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}var vt=class i{constructor(t,e,s,d,p,_,R){this.authService=t;this.rideService=e;this.messageService=s;this.paymentService=d;this.router=p;this.dialog=_;this.snackBar=R}rides=[];displayedColumns=["pickup_location","dropoff_location","pickup_time","status","payment_status","fare","actions"];currentUser=null;selectedRide=null;selectedRideId=null;ridesSubscription=null;ngOnInit(){return g(this,null,function*(){this.currentUser=yield this.authService.getCurrentUser(),this.currentUser&&(yield this.loadUserRides(),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.currentUser&&(this.rides=t.filter(e=>e.rider_id===this.currentUser.id))}))})}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe()}loadUserRides(){return g(this,null,function*(){if(this.currentUser)try{this.rides=yield this.rideService.getUserRides(this.currentUser.id)}catch(t){console.error("Error loading user rides:",t),this.snackBar.open("Failed to load rides","Close",{duration:3e3})}})}cancelRide(t){return g(this,null,function*(){try{if(yield this.rideService.cancelRide(t))this.snackBar.open("Ride canceled successfully","Close",{duration:3e3}),yield this.loadUserRides();else throw new Error("Failed to cancel ride")}catch(e){console.error("Error canceling ride:",e),this.snackBar.open("Failed to cancel ride","Close",{duration:3e3})}})}formatDate(t){return new Date(t).toLocaleString()}formatStatus(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}getStatusClass(t){return`status-chip status-${t}`}openChat(t){return g(this,null,function*(){try{let e=yield this.messageService.getOrCreateThreadForRide(t);this.router.navigate(["/dashboard","rider","messages",e.id])}catch(e){console.error("Error opening chat:",e),this.snackBar.open("Failed to open chat","Close",{duration:3e3})}})}viewPayment(t){this.selectedRide=t}closePayment(){this.selectedRide=null,this.loadUserRides()}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null,this.loadUserRides()}onRideUpdated(t){this.loadUserRides()}static \u0275fac=function(e){return new(e||i)(u(q),u(V),u(Qe),u(N),u(me),u(le),u(T))};static \u0275cmp=w({type:i,selectors:[["app-rider"]],decls:32,vars:5,consts:[[1,"dashboard-container"],["label","Request Ride"],["label","Ride History"],[1,"table-container"],["mat-table","",1,"ride-table",3,"dataSource"],["matColumnDef","pickup_location"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","dropoff_location"],["matColumnDef","pickup_time"],["matColumnDef","status"],["matColumnDef","payment_status"],["matColumnDef","fare"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","payment-overlay",4,"ngIf"],["class","ride-detail-overlay",4,"ngIf"],["mat-header-cell",""],["mat-cell",""],[4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Cancel Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","View Payment",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-icon-button","","color","warn","matTooltip","Cancel Ride",3,"click"],["mat-icon-button","","color","accent","matTooltip","View Payment",3,"click"],["mat-header-row",""],["mat-row",""],[1,"payment-overlay"],[3,"paymentCompleted","ride"],["mat-icon-button","",1,"close-payment-button",3,"click"],[1,"ride-detail-overlay"],[3,"paymentRequested","rideUpdated","rideId","onClose"]],template:function(e,s){e&1&&(n(0,"div",0)(1,"mat-tab-group")(2,"mat-tab",1),f(3,"app-ride-request"),r(),n(4,"mat-tab",2)(5,"div",3)(6,"table",4),b(7,5),l(8,Ut,2,0,"th",6)(9,jt,2,1,"td",7),k(),b(10,8),l(11,$t,2,0,"th",6)(12,zt,2,1,"td",7),k(),b(13,9),l(14,Lt,2,0,"th",6)(15,Ht,2,1,"td",7),k(),b(16,10),l(17,Gt,2,0,"th",6)(18,Qt,4,3,"td",7),k(),b(19,11),l(20,Kt,2,0,"th",6)(21,Xt,3,2,"td",7),k(),b(22,12),l(23,Yt,2,0,"th",6)(24,Zt,2,1,"td",7),k(),b(25,13),l(26,ei,2,0,"th",6)(27,ni,6,2,"td",7),k(),l(28,ri,1,0,"tr",14)(29,oi,1,0,"tr",15),r()()()()(),l(30,ai,5,1,"div",16)(31,si,2,2,"div",17)),e&2&&(o(6),c("dataSource",s.rides),o(22),c("matHeaderRowDef",s.displayedColumns),o(),c("matRowDefColumns",s.displayedColumns),o(),c("ngIf",s.selectedRide),o(),c("ngIf",s.selectedRideId))},dependencies:[I,E,B,Ge,Le,He,ct,Ye,et,rt,tt,Ze,ot,it,nt,at,st,Xe,We,Je,D,j,O,F,z,mt,dt,L,W,J,Ke],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.table-container[_ngcontent-%COMP%]{margin:20px}.ride-table[_ngcontent-%COMP%]{width:100%}.status-chip[_ngcontent-%COMP%]{border-radius:16px;padding:4px 12px;color:#fff;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#ff9800}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3}.status-in-progress[_ngcontent-%COMP%]{background-color:#673ab7}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336}.payment-status-pending[_ngcontent-%COMP%]{background-color:#ffeb3b;color:#000}.payment-status-paid[_ngcontent-%COMP%]{background-color:#4caf50;color:#fff}.payment-status-failed[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.payment-status-refunded[_ngcontent-%COMP%]{background-color:#9e9e9e;color:#fff}.payment-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.close-payment-button[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;background-color:#fff}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}"]})};export{vt as RiderComponent};
