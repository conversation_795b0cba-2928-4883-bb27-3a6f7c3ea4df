{"version": 3, "sources": ["src/app/features/dashboard/admin/stripe-payment/stripe-payment.component.ts"], "sourcesContent": ["\n    .container {\n      padding: 20px;\n    }\n\n    .form-row {\n      margin-bottom: 20px;\n    }\n\n    mat-form-field {\n      width: 100%;\n    }\n\n    .card-element {\n      border: 1px solid #ccc;\n      padding: 10px;\n      border-radius: 4px;\n      height: 40px;\n      background-color: white;\n    }\n\n    .card-errors {\n      color: #f44336;\n      margin-top: 8px;\n      font-size: 14px;\n    }\n\n    .form-actions {\n      margin-top: 20px;\n      margin-bottom: 20px;\n    }\n\n    .divider {\n      margin: 30px 0;\n    }\n\n    .payment-result {\n      margin-top: 20px;\n    }\n\n    .payment-result pre {\n      background-color: #f5f5f5;\n      padding: 10px;\n      border-radius: 4px;\n      overflow-x: auto;\n    }\n\n    .payment-section {\n      margin-bottom: 30px;\n    }\n\n    .payment-tabs {\n      margin-top: 20px;\n    }\n\n    .recent-transactions {\n      margin-top: 30px;\n    }\n\n    table {\n      width: 100%;\n    }\n\n    .status-succeeded, .status-completed {\n      color: #4caf50;\n      font-weight: 500;\n    }\n\n    .status-pending {\n      color: #ff9800;\n      font-weight: 500;\n    }\n\n    .status-failed, .status-refunded {\n      color: #f44336;\n      font-weight: 500;\n    }\n\n    .result-card {\n      display: flex;\n      align-items: center;\n      padding: 15px;\n      border-radius: 4px;\n      margin-top: 10px;\n    }\n\n    .result-card.success {\n      background-color: rgba(76, 175, 80, 0.1);\n    }\n\n    .result-card.error {\n      background-color: rgba(244, 67, 54, 0.1);\n    }\n\n    .result-card mat-icon {\n      margin-right: 15px;\n      font-size: 24px;\n      height: 24px;\n      width: 24px;\n    }\n\n    .result-card.success mat-icon {\n      color: #4caf50;\n    }\n\n    .result-card.error mat-icon {\n      color: #f44336;\n    }\n\n    .result-message h4 {\n      margin: 0 0 5px 0;\n    }\n\n    .result-message p {\n      margin: 0;\n    }\n\n    .sdk-status {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      flex-direction: column;\n      padding: 40px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF;AACE,SAAA;;AAGF,CAAA;AACE,UAAA,IAAA,MAAA;AACA,WAAA;AACA,iBAAA;AACA,UAAA;AACA,oBAAA;;AAGF,CAAA;AACE,SAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAJA,eAIA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,cAAA;;AAGF;AACE,SAAA;;AAGF,CAAA;AAAA,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AAAA,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA;;AAGF,CARA,WAQA,CAAA;AACE,oBAAA,KAAA,EAAA,EAAA,GAAA,EAAA,EAAA,EAAA;;AAGF,CAZA,WAYA,CAAA;AACE,oBAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAGF,CAhBA,YAgBA;AACE,gBAAA;AACA,aAAA;AACA,UAAA;AACA,SAAA;;AAGF,CAvBA,WAuBA,CAfA,QAeA;AACE,SAAA;;AAGF,CA3BA,WA2BA,CAfA,MAeA;AACE,SAAA;;AAGF,CAAA,eAAA;AACE,UAAA,EAAA,EAAA,IAAA;;AAGF,CAJA,eAIA;AACE,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,kBAAA;AACA,WAAA;;", "names": []}