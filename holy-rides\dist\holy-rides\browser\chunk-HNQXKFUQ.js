import{s as C,t as N}from"./chunk-XDJELRYM.js";import{a as He,e as ze,f as kt,j as Mt,l as St,o as Vt,p as ve,s as Et}from"./chunk-3VEHVC57.js";import{B as Rt,G as Tt,a as It,c as xt,d as Ft}from"./chunk-AG3SD6JT.js";import{Ab as Pe,Ad as Ct,B as Je,Ba as ot,Bb as Ye,Bd as Ne,Cd as $,Db as ut,Ea as dt,Eb as V,Ed as wt,Fb as M,Gb as S,Gd as Be,H as et,Ha as Fe,Jd as At,Kb as f,La as d,Lb as Y,Mb as L,Nb as Le,Oa as Re,Ob as ue,Oc as gt,Pb as me,Qa as lt,Qb as _e,Qc as x,Ra as ct,Rc as ft,S as oe,Sb as ge,Wa as A,Xa as pt,Xc as bt,Y as Se,Ya as q,Yc as U,Z as de,Zc as fe,_ as tt,_a as Te,a as w,aa as Ve,ab as T,ca as at,da as o,dc as E,ea as it,ed as vt,f as F,fa as rt,fc as O,gb as v,gd as Dt,hb as u,hd as be,ib as ce,id as P,jb as I,k as ne,ka as R,kb as Oe,la as _,ma as g,mb as Q,mc as mt,na as W,oa as Ee,ob as pe,pa as nt,pb as he,qb as l,ra as Ie,rb as c,sa as st,sb as k,tc as _t,va as p,vd as yt,wa as xe,wb as G,xb as ht,yb as m,z as se,za as le,zb as h}from"./chunk-ST4QC4E3.js";var ra=["mat-calendar-body",""];function na(r,s){return this._trackRow(s)}var zt=(r,s)=>s.id;function sa(r,s){if(r&1&&(l(0,"tr",0)(1,"td",3),f(2),c()()),r&2){let e=h();d(),ce("padding-top",e._cellPadding)("padding-bottom",e._cellPadding),v("colspan",e.numCols),d(),L(" ",e.label," ")}}function oa(r,s){if(r&1&&(l(0,"td",3),f(1),c()),r&2){let e=h(2);ce("padding-top",e._cellPadding)("padding-bottom",e._cellPadding),v("colspan",e._firstRowOffset),d(),L(" ",e._firstRowOffset>=e.labelMinRequiredCells?e.label:""," ")}}function da(r,s){if(r&1){let e=G();l(0,"td",6)(1,"button",7),m("click",function(t){let i=_(e).$implicit,n=h(2);return g(n._cellClicked(i,t))})("focus",function(t){let i=_(e).$implicit,n=h(2);return g(n._emitActiveDateChange(i,t))}),l(2,"span",8),f(3),c(),k(4,"span",9),c()()}if(r&2){let e=s.$implicit,a=s.$index,t=h().$index,i=h();ce("width",i._cellWidth)("padding-top",i._cellPadding)("padding-bottom",i._cellPadding),v("data-mat-row",t)("data-mat-col",a),d(),I("mat-calendar-body-disabled",!e.enabled)("mat-calendar-body-active",i._isActiveCell(t,a))("mat-calendar-body-range-start",i._isRangeStart(e.compareValue))("mat-calendar-body-range-end",i._isRangeEnd(e.compareValue))("mat-calendar-body-in-range",i._isInRange(e.compareValue))("mat-calendar-body-comparison-bridge-start",i._isComparisonBridgeStart(e.compareValue,t,a))("mat-calendar-body-comparison-bridge-end",i._isComparisonBridgeEnd(e.compareValue,t,a))("mat-calendar-body-comparison-start",i._isComparisonStart(e.compareValue))("mat-calendar-body-comparison-end",i._isComparisonEnd(e.compareValue))("mat-calendar-body-in-comparison-range",i._isInComparisonRange(e.compareValue))("mat-calendar-body-preview-start",i._isPreviewStart(e.compareValue))("mat-calendar-body-preview-end",i._isPreviewEnd(e.compareValue))("mat-calendar-body-in-preview",i._isInPreview(e.compareValue)),u("ngClass",e.cssClasses)("tabindex",i._isActiveCell(t,a)?0:-1),v("aria-label",e.ariaLabel)("aria-disabled",!e.enabled||null)("aria-pressed",i._isSelected(e.compareValue))("aria-current",i.todayValue===e.compareValue?"date":null)("aria-describedby",i._getDescribedby(e.compareValue)),d(),I("mat-calendar-body-selected",i._isSelected(e.compareValue))("mat-calendar-body-comparison-identical",i._isComparisonIdentical(e.compareValue))("mat-calendar-body-today",i.todayValue===e.compareValue),d(),L(" ",e.displayValue," ")}}function la(r,s){if(r&1&&(l(0,"tr",1),T(1,oa,2,6,"td",4),pe(2,da,5,48,"td",5,zt),c()),r&2){let e=s.$implicit,a=s.$index,t=h();d(),Q(a===0&&t._firstRowOffset?1:-1),d(),he(e)}}function ca(r,s){if(r&1&&(l(0,"th",2)(1,"span",6),f(2),c(),l(3,"span",3),f(4),c()()),r&2){let e=s.$implicit;d(2),Y(e.long),d(2),Y(e.narrow)}}var pa=["*"];function ha(r,s){}function ua(r,s){if(r&1){let e=G();l(0,"mat-month-view",4),_e("activeDateChange",function(t){_(e);let i=h();return me(i.activeDate,t)||(i.activeDate=t),g(t)}),m("_userSelection",function(t){_(e);let i=h();return g(i._dateSelected(t))})("dragStarted",function(t){_(e);let i=h();return g(i._dragStarted(t))})("dragEnded",function(t){_(e);let i=h();return g(i._dragEnded(t))}),c()}if(r&2){let e=h();ue("activeDate",e.activeDate),u("selected",e.selected)("dateFilter",e.dateFilter)("maxDate",e.maxDate)("minDate",e.minDate)("dateClass",e.dateClass)("comparisonStart",e.comparisonStart)("comparisonEnd",e.comparisonEnd)("startDateAccessibleName",e.startDateAccessibleName)("endDateAccessibleName",e.endDateAccessibleName)("activeDrag",e._activeDrag)}}function ma(r,s){if(r&1){let e=G();l(0,"mat-year-view",5),_e("activeDateChange",function(t){_(e);let i=h();return me(i.activeDate,t)||(i.activeDate=t),g(t)}),m("monthSelected",function(t){_(e);let i=h();return g(i._monthSelectedInYearView(t))})("selectedChange",function(t){_(e);let i=h();return g(i._goToDateInView(t,"month"))}),c()}if(r&2){let e=h();ue("activeDate",e.activeDate),u("selected",e.selected)("dateFilter",e.dateFilter)("maxDate",e.maxDate)("minDate",e.minDate)("dateClass",e.dateClass)}}function _a(r,s){if(r&1){let e=G();l(0,"mat-multi-year-view",6),_e("activeDateChange",function(t){_(e);let i=h();return me(i.activeDate,t)||(i.activeDate=t),g(t)}),m("yearSelected",function(t){_(e);let i=h();return g(i._yearSelectedInMultiYearView(t))})("selectedChange",function(t){_(e);let i=h();return g(i._goToDateInView(t,"year"))}),c()}if(r&2){let e=h();ue("activeDate",e.activeDate),u("selected",e.selected)("dateFilter",e.dateFilter)("maxDate",e.maxDate)("minDate",e.minDate)("dateClass",e.dateClass)}}function ga(r,s){}var fa=["button"],ba=[[["","matDatepickerToggleIcon",""]]],va=["[matDatepickerToggleIcon]"];function Da(r,s){r&1&&(W(),l(0,"svg",2),k(1,"path",3),c())}var K=(()=>{class r{changes=new F;calendarLabel="Calendar";openCalendarLabel="Open calendar";closeCalendarLabel="Close calendar";prevMonthLabel="Previous month";nextMonthLabel="Next month";prevYearLabel="Previous year";nextYearLabel="Next year";prevMultiYearLabel="Previous 24 years";nextMultiYearLabel="Next 24 years";switchToMonthViewLabel="Choose date";switchToMultiYearViewLabel="Choose month and year";startDateLabel="Start date";endDateLabel="End date";comparisonDateLabel="Comparison range";formatYearRange(e,a){return`${e} \u2013 ${a}`}formatYearRangeLabel(e,a){return`${e} to ${a}`}static \u0275fac=function(a){return new(a||r)};static \u0275prov=de({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})(),ya=0,J=class{value;displayValue;ariaLabel;enabled;cssClasses;compareValue;rawValue;id=ya++;constructor(s,e,a,t,i={},n=s,y){this.value=s,this.displayValue=e,this.ariaLabel=a,this.enabled=t,this.cssClasses=i,this.compareValue=n,this.rawValue=y}},Ca={passive:!1,capture:!0},De={passive:!0,capture:!0},Ot={passive:!0},j=(()=>{class r{_elementRef=o(le);_ngZone=o(xe);_platform=o(ft);_intl=o(K);_eventCleanups;_skipNextFocus;_focusActiveCellAfterViewChecked=!1;label;rows;todayValue;startValue;endValue;labelMinRequiredCells;numCols=7;activeCell=0;ngAfterViewChecked(){this._focusActiveCellAfterViewChecked&&(this._focusActiveCell(),this._focusActiveCellAfterViewChecked=!1)}isRange=!1;cellAspectRatio=1;comparisonStart;comparisonEnd;previewStart=null;previewEnd=null;startDateAccessibleName;endDateAccessibleName;selectedValueChange=new p;previewChange=new p;activeDateChange=new p;dragStarted=new p;dragEnded=new p;_firstRowOffset;_cellPadding;_cellWidth;_startDateLabelId;_endDateLabelId;_comparisonStartDateLabelId;_comparisonEndDateLabelId;_didDragSinceMouseDown=!1;_injector=o(Ie);comparisonDateAccessibleName=this._intl.comparisonDateLabel;_trackRow=e=>e;constructor(){let e=o(Re),a=o(be);this._startDateLabelId=a.getId("mat-calendar-body-start-"),this._endDateLabelId=a.getId("mat-calendar-body-end-"),this._comparisonStartDateLabelId=a.getId("mat-calendar-body-comparison-start-"),this._comparisonEndDateLabelId=a.getId("mat-calendar-body-comparison-end-"),o(U).load(Ct),this._ngZone.runOutsideAngular(()=>{let t=this._elementRef.nativeElement,i=[x(e,t,"touchmove",this._touchmoveHandler,Ca),x(e,t,"mouseenter",this._enterHandler,De),x(e,t,"focus",this._enterHandler,De),x(e,t,"mouseleave",this._leaveHandler,De),x(e,t,"blur",this._leaveHandler,De),x(e,t,"mousedown",this._mousedownHandler,Ot),x(e,t,"touchstart",this._mousedownHandler,Ot)];this._platform.isBrowser&&i.push(e.listen("window","mouseup",this._mouseupHandler),e.listen("window","touchend",this._touchendHandler)),this._eventCleanups=i})}_cellClicked(e,a){this._didDragSinceMouseDown||e.enabled&&this.selectedValueChange.emit({value:e.value,event:a})}_emitActiveDateChange(e,a){e.enabled&&this.activeDateChange.emit({value:e.value,event:a})}_isSelected(e){return this.startValue===e||this.endValue===e}ngOnChanges(e){let a=e.numCols,{rows:t,numCols:i}=this;(e.rows||a)&&(this._firstRowOffset=t&&t.length&&t[0].length?i-t[0].length:0),(e.cellAspectRatio||a||!this._cellPadding)&&(this._cellPadding=`${50*this.cellAspectRatio/i}%`),(a||!this._cellWidth)&&(this._cellWidth=`${100/i}%`)}ngOnDestroy(){this._eventCleanups.forEach(e=>e())}_isActiveCell(e,a){let t=e*this.numCols+a;return e&&(t-=this._firstRowOffset),t==this.activeCell}_focusActiveCell(e=!0){Fe(()=>{setTimeout(()=>{let a=this._elementRef.nativeElement.querySelector(".mat-calendar-body-active");a&&(e||(this._skipNextFocus=!0),a.focus())})},{injector:this._injector})}_scheduleFocusActiveCellAfterViewChecked(){this._focusActiveCellAfterViewChecked=!0}_isRangeStart(e){return We(e,this.startValue,this.endValue)}_isRangeEnd(e){return qe(e,this.startValue,this.endValue)}_isInRange(e){return Qe(e,this.startValue,this.endValue,this.isRange)}_isComparisonStart(e){return We(e,this.comparisonStart,this.comparisonEnd)}_isComparisonBridgeStart(e,a,t){if(!this._isComparisonStart(e)||this._isRangeStart(e)||!this._isInRange(e))return!1;let i=this.rows[a][t-1];if(!i){let n=this.rows[a-1];i=n&&n[n.length-1]}return i&&!this._isRangeEnd(i.compareValue)}_isComparisonBridgeEnd(e,a,t){if(!this._isComparisonEnd(e)||this._isRangeEnd(e)||!this._isInRange(e))return!1;let i=this.rows[a][t+1];if(!i){let n=this.rows[a+1];i=n&&n[0]}return i&&!this._isRangeStart(i.compareValue)}_isComparisonEnd(e){return qe(e,this.comparisonStart,this.comparisonEnd)}_isInComparisonRange(e){return Qe(e,this.comparisonStart,this.comparisonEnd,this.isRange)}_isComparisonIdentical(e){return this.comparisonStart===this.comparisonEnd&&e===this.comparisonStart}_isPreviewStart(e){return We(e,this.previewStart,this.previewEnd)}_isPreviewEnd(e){return qe(e,this.previewStart,this.previewEnd)}_isInPreview(e){return Qe(e,this.previewStart,this.previewEnd,this.isRange)}_getDescribedby(e){if(!this.isRange)return null;if(this.startValue===e&&this.endValue===e)return`${this._startDateLabelId} ${this._endDateLabelId}`;if(this.startValue===e)return this._startDateLabelId;if(this.endValue===e)return this._endDateLabelId;if(this.comparisonStart!==null&&this.comparisonEnd!==null){if(e===this.comparisonStart&&e===this.comparisonEnd)return`${this._comparisonStartDateLabelId} ${this._comparisonEndDateLabelId}`;if(e===this.comparisonStart)return this._comparisonStartDateLabelId;if(e===this.comparisonEnd)return this._comparisonEndDateLabelId}return null}_enterHandler=e=>{if(this._skipNextFocus&&e.type==="focus"){this._skipNextFocus=!1;return}if(e.target&&this.isRange){let a=this._getCellFromElement(e.target);a&&this._ngZone.run(()=>this.previewChange.emit({value:a.enabled?a:null,event:e}))}};_touchmoveHandler=e=>{if(!this.isRange)return;let a=Pt(e),t=a?this._getCellFromElement(a):null;a!==e.target&&(this._didDragSinceMouseDown=!0),Ke(e.target)&&e.preventDefault(),this._ngZone.run(()=>this.previewChange.emit({value:t?.enabled?t:null,event:e}))};_leaveHandler=e=>{this.previewEnd!==null&&this.isRange&&(e.type!=="blur"&&(this._didDragSinceMouseDown=!0),e.target&&this._getCellFromElement(e.target)&&!(e.relatedTarget&&this._getCellFromElement(e.relatedTarget))&&this._ngZone.run(()=>this.previewChange.emit({value:null,event:e})))};_mousedownHandler=e=>{if(!this.isRange)return;this._didDragSinceMouseDown=!1;let a=e.target&&this._getCellFromElement(e.target);!a||!this._isInRange(a.compareValue)||this._ngZone.run(()=>{this.dragStarted.emit({value:a.rawValue,event:e})})};_mouseupHandler=e=>{if(!this.isRange)return;let a=Ke(e.target);if(!a){this._ngZone.run(()=>{this.dragEnded.emit({value:null,event:e})});return}a.closest(".mat-calendar-body")===this._elementRef.nativeElement&&this._ngZone.run(()=>{let t=this._getCellFromElement(a);this.dragEnded.emit({value:t?.rawValue??null,event:e})})};_touchendHandler=e=>{let a=Pt(e);a&&this._mouseupHandler({target:a})};_getCellFromElement(e){let a=Ke(e);if(a){let t=a.getAttribute("data-mat-row"),i=a.getAttribute("data-mat-col");if(t&&i)return this.rows[parseInt(t)][parseInt(i)]}return null}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["","mat-calendar-body",""]],hostAttrs:[1,"mat-calendar-body"],inputs:{label:"label",rows:"rows",todayValue:"todayValue",startValue:"startValue",endValue:"endValue",labelMinRequiredCells:"labelMinRequiredCells",numCols:"numCols",activeCell:"activeCell",isRange:"isRange",cellAspectRatio:"cellAspectRatio",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",previewStart:"previewStart",previewEnd:"previewEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedValueChange:"selectedValueChange",previewChange:"previewChange",activeDateChange:"activeDateChange",dragStarted:"dragStarted",dragEnded:"dragEnded"},exportAs:["matCalendarBody"],features:[R],attrs:ra,decls:11,vars:11,consts:[["aria-hidden","true"],["role","row"],[1,"mat-calendar-body-hidden-label",3,"id"],[1,"mat-calendar-body-label"],[1,"mat-calendar-body-label",3,"paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container",3,"width","paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container"],["type","button",1,"mat-calendar-body-cell",3,"click","focus","ngClass","tabindex"],[1,"mat-calendar-body-cell-content","mat-focus-indicator"],["aria-hidden","true",1,"mat-calendar-body-cell-preview"]],template:function(a,t){a&1&&(T(0,sa,3,6,"tr",0),pe(1,la,4,1,"tr",1,na,!0),l(3,"span",2),f(4),c(),l(5,"span",2),f(6),c(),l(7,"span",2),f(8),c(),l(9,"span",2),f(10),c()),a&2&&(Q(t._firstRowOffset<t.labelMinRequiredCells?0:-1),d(),he(t.rows),d(2),u("id",t._startDateLabelId),d(),L(" ",t.startDateAccessibleName,`
`),d(),u("id",t._endDateLabelId),d(),L(" ",t.endDateAccessibleName,`
`),d(),u("id",t._comparisonStartDateLabelId),d(),Le(" ",t.comparisonDateAccessibleName," ",t.startDateAccessibleName,`
`),d(),u("id",t._comparisonEndDateLabelId),d(),Le(" ",t.comparisonDateAccessibleName," ",t.endDateAccessibleName,`
`))},dependencies:[_t],styles:[`.mat-calendar-body{min-width:224px}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-outline-color, var(--mat-sys-primary))}.mat-calendar-body-label{height:0;line-height:0;text-align:start;padding-left:4.7142857143%;padding-right:4.7142857143%;font-size:var(--mat-datepicker-calendar-body-label-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-body-label-text-weight, var(--mat-sys-title-small-weight));color:var(--mat-datepicker-calendar-body-label-text-color, var(--mat-sys-on-surface))}.mat-calendar-body-hidden-label{display:none}.mat-calendar-body-cell-container{position:relative;height:0;line-height:0}.mat-calendar-body-cell{position:absolute;top:0;left:0;width:100%;height:100%;background:none;text-align:center;outline:none;font-family:inherit;margin:0;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size));-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-calendar-body-cell::-moz-focus-inner{border:0}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-cell-preview{content:"";position:absolute;top:5%;left:0;z-index:0;box-sizing:border-box;display:block;height:90%;width:100%}.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-start::after,.mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,.mat-calendar-body-comparison-start::after,.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:5%;width:95%;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,[dir=rtl] .mat-calendar-body-comparison-start::after,[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:0;border-radius:0;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,.mat-calendar-body-comparison-end::after,.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}[dir=rtl] .mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,[dir=rtl] .mat-calendar-body-comparison-end::after,[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{left:5%;border-radius:0;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-comparison-bridge-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-bridge-end.mat-calendar-body-range-start::after{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end.mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-end.mat-calendar-body-range-start::after{width:90%}.mat-calendar-body-in-preview{color:var(--mat-datepicker-calendar-date-preview-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-preview .mat-calendar-body-cell-preview{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:0;border-right:dashed 1px}.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:0;border-left:dashed 1px}.mat-calendar-body-disabled{cursor:default}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:var(--mat-datepicker-calendar-date-disabled-state-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-disabled-state-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mat-calendar-body-disabled{opacity:.5}}.mat-calendar-body-cell-content{top:5%;left:5%;z-index:1;display:flex;align-items:center;justify-content:center;box-sizing:border-box;width:90%;height:90%;line-height:1;border-width:1px;border-style:solid;border-radius:999px;color:var(--mat-datepicker-calendar-date-text-color, var(--mat-sys-on-surface));border-color:var(--mat-datepicker-calendar-date-outline-color, transparent)}.mat-calendar-body-cell-content.mat-focus-indicator{position:absolute}@media(forced-colors: active){.mat-calendar-body-cell-content{border:none}}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-focus-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent))}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-hover-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}}.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-state-background-color, var(--mat-sys-primary));color:var(--mat-datepicker-calendar-date-selected-state-text-color, var(--mat-sys-on-primary))}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-disabled-state-background-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-calendar-body-selected.mat-calendar-body-today{box-shadow:inset 0 0 0 1px var(--mat-datepicker-calendar-date-today-selected-state-outline-color, var(--mat-sys-primary))}.mat-calendar-body-in-range::before{background:var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container))}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-sys-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-sys-tertiary-container)) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:var(--mat-datepicker-calendar-date-in-overlap-range-state-background-color, var(--mat-sys-secondary-container))}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:var(--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color, var(--mat-sys-secondary))}@media(forced-colors: active){.mat-datepicker-popup:not(:empty),.mat-calendar-body-cell:not(.mat-calendar-body-in-range) .mat-calendar-body-selected{outline:solid 1px}.mat-calendar-body-today{outline:dotted 1px}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-selected{background:none}.mat-calendar-body-in-range::before,.mat-calendar-body-comparison-bridge-start::before,.mat-calendar-body-comparison-bridge-end::before{border-top:solid 1px;border-bottom:solid 1px}.mat-calendar-body-range-start::before{border-left:solid 1px}[dir=rtl] .mat-calendar-body-range-start::before{border-left:0;border-right:solid 1px}.mat-calendar-body-range-end::before{border-right:solid 1px}[dir=rtl] .mat-calendar-body-range-end::before{border-right:0;border-left:solid 1px}.mat-calendar-body-in-comparison-range::before{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-comparison-start::before{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-start::before{border-left:0;border-right:dashed 1px}.mat-calendar-body-comparison-end::before{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-comparison-end::before{border-right:0;border-left:dashed 1px}}
`],encapsulation:2,changeDetection:0})}return r})();function je(r){return r?.nodeName==="TD"}function Ke(r){let s;return je(r)?s=r:je(r.parentNode)?s=r.parentNode:je(r.parentNode?.parentNode)&&(s=r.parentNode.parentNode),s?.getAttribute("data-mat-row")!=null?s:null}function We(r,s,e){return e!==null&&s!==e&&r<e&&r===s}function qe(r,s,e){return s!==null&&s!==e&&r>=s&&r===e}function Qe(r,s,e,a){return a&&s!==null&&e!==null&&s!==e&&r>=s&&r<=e}function Pt(r){let s=r.changedTouches[0];return document.elementFromPoint(s.clientX,s.clientY)}var D=class{start;end;_disableStructuralEquivalency;constructor(s,e){this.start=s,this.end=e}},ee=(()=>{class r{selection;_adapter;_selectionChanged=new F;selectionChanged=this._selectionChanged;constructor(e,a){this.selection=e,this._adapter=a,this.selection=e}updateSelection(e,a){let t=this.selection;this.selection=e,this._selectionChanged.next({selection:e,source:a,oldValue:t})}ngOnDestroy(){this._selectionChanged.complete()}_isValidDateInstance(e){return this._adapter.isDateInstance(e)&&this._adapter.isValid(e)}static \u0275fac=function(a){lt()};static \u0275prov=de({token:r,factory:r.\u0275fac})}return r})(),wa=(()=>{class r extends ee{constructor(e){super(null,e)}add(e){super.updateSelection(e,this)}isValid(){return this.selection!=null&&this._isValidDateInstance(this.selection)}isComplete(){return this.selection!=null}clone(){let e=new r(this._adapter);return e.updateSelection(this.selection,this),e}static \u0275fac=function(a){return new(a||r)(at(C))};static \u0275prov=de({token:r,factory:r.\u0275fac})}return r})();function Aa(r,s){return r||new wa(s)}var jt={provide:ee,deps:[[new it,new rt,ee],C],useFactory:Aa};var Kt=new Ve("MAT_DATE_RANGE_SELECTION_STRATEGY");var Ge=7,ka=0,Yt=(()=>{class r{_changeDetectorRef=o(E);_dateFormats=o(N,{optional:!0});_dateAdapter=o(C,{optional:!0});_dir=o($,{optional:!0});_rangeStrategy=o(Kt,{optional:!0});_rerenderSubscription=w.EMPTY;_selectionKeyPressed;get activeDate(){return this._activeDate}set activeDate(e){let a=this._activeDate,t=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(t,this.minDate,this.maxDate),this._hasSameMonthAndYear(a,this._activeDate)||this._init()}_activeDate;get selected(){return this._selected}set selected(e){e instanceof D?this._selected=e:this._selected=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e)),this._setRanges(this._selected)}_selected;get minDate(){return this._minDate}set minDate(e){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_minDate;get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_maxDate;dateFilter;dateClass;comparisonStart;comparisonEnd;startDateAccessibleName;endDateAccessibleName;activeDrag=null;selectedChange=new p;_userSelection=new p;dragStarted=new p;dragEnded=new p;activeDateChange=new p;_matCalendarBody;_monthLabel;_weeks;_firstWeekOffset;_rangeStart;_rangeEnd;_comparisonRangeStart;_comparisonRangeEnd;_previewStart;_previewEnd;_isRange;_todayDate;_weekdays;constructor(){o(U).load(fe),this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe(oe(null)).subscribe(()=>this._init())}ngOnChanges(e){let a=e.comparisonStart||e.comparisonEnd;a&&!a.firstChange&&this._setRanges(this.selected),e.activeDrag&&!this.activeDrag&&this._clearPreview()}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_dateSelected(e){let a=e.value,t=this._getDateFromDayOfMonth(a),i,n;this._selected instanceof D?(i=this._getDateInCurrentMonth(this._selected.start),n=this._getDateInCurrentMonth(this._selected.end)):i=n=this._getDateInCurrentMonth(this._selected),(i!==a||n!==a)&&this.selectedChange.emit(t),this._userSelection.emit({value:t,event:e.event}),this._clearPreview(),this._changeDetectorRef.markForCheck()}_updateActiveDate(e){let a=e.value,t=this._activeDate;this.activeDate=this._getDateFromDayOfMonth(a),this._dateAdapter.compareDate(t,this.activeDate)&&this.activeDateChange.emit(this._activeDate)}_handleCalendarBodyKeydown(e){let a=this._activeDate,t=this._isRtl();switch(e.keyCode){case 37:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,t?1:-1);break;case 39:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,t?-1:1);break;case 38:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,-7);break;case 40:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,7);break;case 36:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,1-this._dateAdapter.getDate(this._activeDate));break;case 35:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,this._dateAdapter.getNumDaysInMonth(this._activeDate)-this._dateAdapter.getDate(this._activeDate));break;case 33:this.activeDate=e.altKey?this._dateAdapter.addCalendarYears(this._activeDate,-1):this._dateAdapter.addCalendarMonths(this._activeDate,-1);break;case 34:this.activeDate=e.altKey?this._dateAdapter.addCalendarYears(this._activeDate,1):this._dateAdapter.addCalendarMonths(this._activeDate,1);break;case 13:case 32:this._selectionKeyPressed=!0,this._canSelect(this._activeDate)&&e.preventDefault();return;case 27:this._previewEnd!=null&&!P(e)&&(this._clearPreview(),this.activeDrag?this.dragEnded.emit({value:null,event:e}):(this.selectedChange.emit(null),this._userSelection.emit({value:null,event:e})),e.preventDefault(),e.stopPropagation());return;default:return}this._dateAdapter.compareDate(a,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),e.preventDefault()}_handleCalendarBodyKeyup(e){(e.keyCode===32||e.keyCode===13)&&(this._selectionKeyPressed&&this._canSelect(this._activeDate)&&this._dateSelected({value:this._dateAdapter.getDate(this._activeDate),event:e}),this._selectionKeyPressed=!1)}_init(){this._setRanges(this.selected),this._todayDate=this._getCellCompareValue(this._dateAdapter.today()),this._monthLabel=this._dateFormats.display.monthLabel?this._dateAdapter.format(this.activeDate,this._dateFormats.display.monthLabel):this._dateAdapter.getMonthNames("short")[this._dateAdapter.getMonth(this.activeDate)].toLocaleUpperCase();let e=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),1);this._firstWeekOffset=(Ge+this._dateAdapter.getDayOfWeek(e)-this._dateAdapter.getFirstDayOfWeek())%Ge,this._initWeekdays(),this._createWeekCells(),this._changeDetectorRef.markForCheck()}_focusActiveCell(e){this._matCalendarBody._focusActiveCell(e)}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_previewChanged({event:e,value:a}){if(this._rangeStrategy){let t=a?a.rawValue:null,i=this._rangeStrategy.createPreview(t,this.selected,e);if(this._previewStart=this._getCellCompareValue(i.start),this._previewEnd=this._getCellCompareValue(i.end),this.activeDrag&&t){let n=this._rangeStrategy.createDrag?.(this.activeDrag.value,this.selected,t,e);n&&(this._previewStart=this._getCellCompareValue(n.start),this._previewEnd=this._getCellCompareValue(n.end))}this._changeDetectorRef.detectChanges()}}_dragEnded(e){if(this.activeDrag)if(e.value){let a=this._rangeStrategy?.createDrag?.(this.activeDrag.value,this.selected,e.value,e.event);this.dragEnded.emit({value:a??null,event:e.event})}else this.dragEnded.emit({value:null,event:e.event})}_getDateFromDayOfMonth(e){return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),e)}_initWeekdays(){let e=this._dateAdapter.getFirstDayOfWeek(),a=this._dateAdapter.getDayOfWeekNames("narrow"),i=this._dateAdapter.getDayOfWeekNames("long").map((n,y)=>({long:n,narrow:a[y],id:ka++}));this._weekdays=i.slice(e).concat(i.slice(0,e))}_createWeekCells(){let e=this._dateAdapter.getNumDaysInMonth(this.activeDate),a=this._dateAdapter.getDateNames();this._weeks=[[]];for(let t=0,i=this._firstWeekOffset;t<e;t++,i++){i==Ge&&(this._weeks.push([]),i=0);let n=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),t+1),y=this._shouldEnableDate(n),re=this._dateAdapter.format(n,this._dateFormats.display.dateA11yLabel),ia=this.dateClass?this.dateClass(n,"month"):void 0;this._weeks[this._weeks.length-1].push(new J(t+1,a[t],re,y,ia,this._getCellCompareValue(n),n))}}_shouldEnableDate(e){return!!e&&(!this.minDate||this._dateAdapter.compareDate(e,this.minDate)>=0)&&(!this.maxDate||this._dateAdapter.compareDate(e,this.maxDate)<=0)&&(!this.dateFilter||this.dateFilter(e))}_getDateInCurrentMonth(e){return e&&this._hasSameMonthAndYear(e,this.activeDate)?this._dateAdapter.getDate(e):null}_hasSameMonthAndYear(e,a){return!!(e&&a&&this._dateAdapter.getMonth(e)==this._dateAdapter.getMonth(a)&&this._dateAdapter.getYear(e)==this._dateAdapter.getYear(a))}_getCellCompareValue(e){if(e){let a=this._dateAdapter.getYear(e),t=this._dateAdapter.getMonth(e),i=this._dateAdapter.getDate(e);return new Date(a,t,i).getTime()}return null}_isRtl(){return this._dir&&this._dir.value==="rtl"}_setRanges(e){e instanceof D?(this._rangeStart=this._getCellCompareValue(e.start),this._rangeEnd=this._getCellCompareValue(e.end),this._isRange=!0):(this._rangeStart=this._rangeEnd=this._getCellCompareValue(e),this._isRange=!1),this._comparisonRangeStart=this._getCellCompareValue(this.comparisonStart),this._comparisonRangeEnd=this._getCellCompareValue(this.comparisonEnd)}_canSelect(e){return!this.dateFilter||this.dateFilter(e)}_clearPreview(){this._previewStart=this._previewEnd=null}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-month-view"]],viewQuery:function(a,t){if(a&1&&V(j,5),a&2){let i;M(i=S())&&(t._matCalendarBody=i.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName",activeDrag:"activeDrag"},outputs:{selectedChange:"selectedChange",_userSelection:"_userSelection",dragStarted:"dragStarted",dragEnded:"dragEnded",activeDateChange:"activeDateChange"},exportAs:["matMonthView"],features:[R],decls:8,vars:14,consts:[["role","grid",1,"mat-calendar-table"],[1,"mat-calendar-table-header"],["scope","col"],["aria-hidden","true"],["colspan","7",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","previewChange","dragStarted","dragEnded","keyup","keydown","label","rows","todayValue","startValue","endValue","comparisonStart","comparisonEnd","previewStart","previewEnd","isRange","labelMinRequiredCells","activeCell","startDateAccessibleName","endDateAccessibleName"],[1,"cdk-visually-hidden"]],template:function(a,t){a&1&&(l(0,"table",0)(1,"thead",1)(2,"tr"),pe(3,ca,5,2,"th",2,zt),c(),l(5,"tr",3),k(6,"th",4),c()(),l(7,"tbody",5),m("selectedValueChange",function(n){return t._dateSelected(n)})("activeDateChange",function(n){return t._updateActiveDate(n)})("previewChange",function(n){return t._previewChanged(n)})("dragStarted",function(n){return t.dragStarted.emit(n)})("dragEnded",function(n){return t._dragEnded(n)})("keyup",function(n){return t._handleCalendarBodyKeyup(n)})("keydown",function(n){return t._handleCalendarBodyKeydown(n)}),c()()),a&2&&(d(3),he(t._weekdays),d(4),u("label",t._monthLabel)("rows",t._weeks)("todayValue",t._todayDate)("startValue",t._rangeStart)("endValue",t._rangeEnd)("comparisonStart",t._comparisonRangeStart)("comparisonEnd",t._comparisonRangeEnd)("previewStart",t._previewStart)("previewEnd",t._previewEnd)("isRange",t._isRange)("labelMinRequiredCells",3)("activeCell",t._dateAdapter.getDate(t.activeDate)-1)("startDateAccessibleName",t.startDateAccessibleName)("endDateAccessibleName",t.endDateAccessibleName))},dependencies:[j],encapsulation:2,changeDetection:0})}return r})(),b=24,Ue=4,Lt=(()=>{class r{_changeDetectorRef=o(E);_dateAdapter=o(C,{optional:!0});_dir=o($,{optional:!0});_rerenderSubscription=w.EMPTY;_selectionKeyPressed;get activeDate(){return this._activeDate}set activeDate(e){let a=this._activeDate,t=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(t,this.minDate,this.maxDate),Wt(this._dateAdapter,a,this._activeDate,this.minDate,this.maxDate)||this._init()}_activeDate;get selected(){return this._selected}set selected(e){e instanceof D?this._selected=e:this._selected=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e)),this._setSelectedYear(e)}_selected;get minDate(){return this._minDate}set minDate(e){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_minDate;get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_maxDate;dateFilter;dateClass;selectedChange=new p;yearSelected=new p;activeDateChange=new p;_matCalendarBody;_years;_todayYear;_selectedYear;constructor(){this._dateAdapter,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe(oe(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_init(){this._todayYear=this._dateAdapter.getYear(this._dateAdapter.today());let a=this._dateAdapter.getYear(this._activeDate)-X(this._dateAdapter,this.activeDate,this.minDate,this.maxDate);this._years=[];for(let t=0,i=[];t<b;t++)i.push(a+t),i.length==Ue&&(this._years.push(i.map(n=>this._createCellForYear(n))),i=[]);this._changeDetectorRef.markForCheck()}_yearSelected(e){let a=e.value,t=this._dateAdapter.createDate(a,0,1),i=this._getDateFromYear(a);this.yearSelected.emit(t),this.selectedChange.emit(i)}_updateActiveDate(e){let a=e.value,t=this._activeDate;this.activeDate=this._getDateFromYear(a),this._dateAdapter.compareDate(t,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(e){let a=this._activeDate,t=this._isRtl();switch(e.keyCode){case 37:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t?1:-1);break;case 39:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t?-1:1);break;case 38:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-Ue);break;case 40:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,Ue);break;case 36:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-X(this._dateAdapter,this.activeDate,this.minDate,this.maxDate));break;case 35:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,b-X(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)-1);break;case 33:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,e.altKey?-b*10:-b);break;case 34:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,e.altKey?b*10:b);break;case 13:case 32:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(a,this.activeDate)&&this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked(),e.preventDefault()}_handleCalendarBodyKeyup(e){(e.keyCode===32||e.keyCode===13)&&(this._selectionKeyPressed&&this._yearSelected({value:this._dateAdapter.getYear(this._activeDate),event:e}),this._selectionKeyPressed=!1)}_getActiveCell(){return X(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getDateFromYear(e){let a=this._dateAdapter.getMonth(this.activeDate),t=this._dateAdapter.getNumDaysInMonth(this._dateAdapter.createDate(e,a,1));return this._dateAdapter.createDate(e,a,Math.min(this._dateAdapter.getDate(this.activeDate),t))}_createCellForYear(e){let a=this._dateAdapter.createDate(e,0,1),t=this._dateAdapter.getYearName(a),i=this.dateClass?this.dateClass(a,"multi-year"):void 0;return new J(e,t,t,this._shouldEnableYear(e),i)}_shouldEnableYear(e){if(e==null||this.maxDate&&e>this._dateAdapter.getYear(this.maxDate)||this.minDate&&e<this._dateAdapter.getYear(this.minDate))return!1;if(!this.dateFilter)return!0;let a=this._dateAdapter.createDate(e,0,1);for(let t=a;this._dateAdapter.getYear(t)==e;t=this._dateAdapter.addCalendarDays(t,1))if(this.dateFilter(t))return!0;return!1}_isRtl(){return this._dir&&this._dir.value==="rtl"}_setSelectedYear(e){if(this._selectedYear=null,e instanceof D){let a=e.start||e.end;a&&(this._selectedYear=this._dateAdapter.getYear(a))}else e&&(this._selectedYear=this._dateAdapter.getYear(e))}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-multi-year-view"]],viewQuery:function(a,t){if(a&1&&V(j,5),a&2){let i;M(i=S())&&(t._matCalendarBody=i.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",activeDateChange:"activeDateChange"},exportAs:["matMultiYearView"],decls:5,vars:7,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","rows","todayValue","startValue","endValue","numCols","cellAspectRatio","activeCell"]],template:function(a,t){a&1&&(l(0,"table",0)(1,"thead",1)(2,"tr"),k(3,"th",2),c()(),l(4,"tbody",3),m("selectedValueChange",function(n){return t._yearSelected(n)})("activeDateChange",function(n){return t._updateActiveDate(n)})("keyup",function(n){return t._handleCalendarBodyKeyup(n)})("keydown",function(n){return t._handleCalendarBodyKeydown(n)}),c()()),a&2&&(d(4),u("rows",t._years)("todayValue",t._todayYear)("startValue",t._selectedYear)("endValue",t._selectedYear)("numCols",4)("cellAspectRatio",4/7)("activeCell",t._getActiveCell()))},dependencies:[j],encapsulation:2,changeDetection:0})}return r})();function Wt(r,s,e,a,t){let i=r.getYear(s),n=r.getYear(e),y=qt(r,a,t);return Math.floor((i-y)/b)===Math.floor((n-y)/b)}function X(r,s,e,a){let t=r.getYear(s);return Ma(t-qt(r,e,a),b)}function qt(r,s,e){let a=0;return e?a=r.getYear(e)-b+1:s&&(a=r.getYear(s)),a}function Ma(r,s){return(r%s+s)%s}var Nt=(()=>{class r{_changeDetectorRef=o(E);_dateFormats=o(N,{optional:!0});_dateAdapter=o(C,{optional:!0});_dir=o($,{optional:!0});_rerenderSubscription=w.EMPTY;_selectionKeyPressed;get activeDate(){return this._activeDate}set activeDate(e){let a=this._activeDate,t=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(t,this.minDate,this.maxDate),this._dateAdapter.getYear(a)!==this._dateAdapter.getYear(this._activeDate)&&this._init()}_activeDate;get selected(){return this._selected}set selected(e){e instanceof D?this._selected=e:this._selected=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e)),this._setSelectedMonth(e)}_selected;get minDate(){return this._minDate}set minDate(e){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_minDate;get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_maxDate;dateFilter;dateClass;selectedChange=new p;monthSelected=new p;activeDateChange=new p;_matCalendarBody;_months;_yearLabel;_todayMonth;_selectedMonth;constructor(){this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe(oe(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_monthSelected(e){let a=e.value,t=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),a,1);this.monthSelected.emit(t);let i=this._getDateFromMonth(a);this.selectedChange.emit(i)}_updateActiveDate(e){let a=e.value,t=this._activeDate;this.activeDate=this._getDateFromMonth(a),this._dateAdapter.compareDate(t,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(e){let a=this._activeDate,t=this._isRtl();switch(e.keyCode){case 37:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,t?1:-1);break;case 39:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,t?-1:1);break;case 38:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-4);break;case 40:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,4);break;case 36:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-this._dateAdapter.getMonth(this._activeDate));break;case 35:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,11-this._dateAdapter.getMonth(this._activeDate));break;case 33:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,e.altKey?-10:-1);break;case 34:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,e.altKey?10:1);break;case 13:case 32:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(a,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),e.preventDefault()}_handleCalendarBodyKeyup(e){(e.keyCode===32||e.keyCode===13)&&(this._selectionKeyPressed&&this._monthSelected({value:this._dateAdapter.getMonth(this._activeDate),event:e}),this._selectionKeyPressed=!1)}_init(){this._setSelectedMonth(this.selected),this._todayMonth=this._getMonthInCurrentYear(this._dateAdapter.today()),this._yearLabel=this._dateAdapter.getYearName(this.activeDate);let e=this._dateAdapter.getMonthNames("short");this._months=[[0,1,2,3],[4,5,6,7],[8,9,10,11]].map(a=>a.map(t=>this._createCellForMonth(t,e[t]))),this._changeDetectorRef.markForCheck()}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getMonthInCurrentYear(e){return e&&this._dateAdapter.getYear(e)==this._dateAdapter.getYear(this.activeDate)?this._dateAdapter.getMonth(e):null}_getDateFromMonth(e){let a=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),e,1),t=this._dateAdapter.getNumDaysInMonth(a);return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),e,Math.min(this._dateAdapter.getDate(this.activeDate),t))}_createCellForMonth(e,a){let t=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),e,1),i=this._dateAdapter.format(t,this._dateFormats.display.monthYearA11yLabel),n=this.dateClass?this.dateClass(t,"year"):void 0;return new J(e,a.toLocaleUpperCase(),i,this._shouldEnableMonth(e),n)}_shouldEnableMonth(e){let a=this._dateAdapter.getYear(this.activeDate);if(e==null||this._isYearAndMonthAfterMaxDate(a,e)||this._isYearAndMonthBeforeMinDate(a,e))return!1;if(!this.dateFilter)return!0;let t=this._dateAdapter.createDate(a,e,1);for(let i=t;this._dateAdapter.getMonth(i)==e;i=this._dateAdapter.addCalendarDays(i,1))if(this.dateFilter(i))return!0;return!1}_isYearAndMonthAfterMaxDate(e,a){if(this.maxDate){let t=this._dateAdapter.getYear(this.maxDate),i=this._dateAdapter.getMonth(this.maxDate);return e>t||e===t&&a>i}return!1}_isYearAndMonthBeforeMinDate(e,a){if(this.minDate){let t=this._dateAdapter.getYear(this.minDate),i=this._dateAdapter.getMonth(this.minDate);return e<t||e===t&&a<i}return!1}_isRtl(){return this._dir&&this._dir.value==="rtl"}_setSelectedMonth(e){e instanceof D?this._selectedMonth=this._getMonthInCurrentYear(e.start)||this._getMonthInCurrentYear(e.end):this._selectedMonth=this._getMonthInCurrentYear(e)}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-year-view"]],viewQuery:function(a,t){if(a&1&&V(j,5),a&2){let i;M(i=S())&&(t._matCalendarBody=i.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",monthSelected:"monthSelected",activeDateChange:"activeDateChange"},exportAs:["matYearView"],decls:5,vars:9,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","label","rows","todayValue","startValue","endValue","labelMinRequiredCells","numCols","cellAspectRatio","activeCell"]],template:function(a,t){a&1&&(l(0,"table",0)(1,"thead",1)(2,"tr"),k(3,"th",2),c()(),l(4,"tbody",3),m("selectedValueChange",function(n){return t._monthSelected(n)})("activeDateChange",function(n){return t._updateActiveDate(n)})("keyup",function(n){return t._handleCalendarBodyKeyup(n)})("keydown",function(n){return t._handleCalendarBodyKeydown(n)}),c()()),a&2&&(d(4),u("label",t._yearLabel)("rows",t._months)("todayValue",t._todayMonth)("startValue",t._selectedMonth)("endValue",t._selectedMonth)("labelMinRequiredCells",2)("numCols",4)("cellAspectRatio",4/7)("activeCell",t._dateAdapter.getMonth(t.activeDate)))},dependencies:[j],encapsulation:2,changeDetection:0})}return r})(),Qt=(()=>{class r{_intl=o(K);calendar=o($e);_dateAdapter=o(C,{optional:!0});_dateFormats=o(N,{optional:!0});constructor(){o(U).load(fe);let e=o(E);this.calendar.stateChanges.subscribe(()=>e.markForCheck())}get periodButtonText(){return this.calendar.currentView=="month"?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():this.calendar.currentView=="year"?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRange(...this._formatMinAndMaxYearLabels())}get periodButtonDescription(){return this.calendar.currentView=="month"?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():this.calendar.currentView=="year"?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRangeLabel(...this._formatMinAndMaxYearLabels())}get periodButtonLabel(){return this.calendar.currentView=="month"?this._intl.switchToMultiYearViewLabel:this._intl.switchToMonthViewLabel}get prevButtonLabel(){return{month:this._intl.prevMonthLabel,year:this._intl.prevYearLabel,"multi-year":this._intl.prevMultiYearLabel}[this.calendar.currentView]}get nextButtonLabel(){return{month:this._intl.nextMonthLabel,year:this._intl.nextYearLabel,"multi-year":this._intl.nextMultiYearLabel}[this.calendar.currentView]}currentPeriodClicked(){this.calendar.currentView=this.calendar.currentView=="month"?"multi-year":"month"}previousClicked(){this.calendar.activeDate=this.calendar.currentView=="month"?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,-1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,this.calendar.currentView=="year"?-1:-b)}nextClicked(){this.calendar.activeDate=this.calendar.currentView=="month"?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,this.calendar.currentView=="year"?1:b)}previousEnabled(){return this.calendar.minDate?!this.calendar.minDate||!this._isSameView(this.calendar.activeDate,this.calendar.minDate):!0}nextEnabled(){return!this.calendar.maxDate||!this._isSameView(this.calendar.activeDate,this.calendar.maxDate)}_isSameView(e,a){return this.calendar.currentView=="month"?this._dateAdapter.getYear(e)==this._dateAdapter.getYear(a)&&this._dateAdapter.getMonth(e)==this._dateAdapter.getMonth(a):this.calendar.currentView=="year"?this._dateAdapter.getYear(e)==this._dateAdapter.getYear(a):Wt(this._dateAdapter,e,a,this.calendar.minDate,this.calendar.maxDate)}_formatMinAndMaxYearLabels(){let a=this._dateAdapter.getYear(this.calendar.activeDate)-X(this._dateAdapter,this.calendar.activeDate,this.calendar.minDate,this.calendar.maxDate),t=a+b-1,i=this._dateAdapter.getYearName(this._dateAdapter.createDate(a,0,1)),n=this._dateAdapter.getYearName(this._dateAdapter.createDate(t,0,1));return[i,n]}_periodButtonLabelId=o(be).getId("mat-calendar-period-label-");static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-calendar-header"]],exportAs:["matCalendarHeader"],ngContentSelectors:pa,decls:17,vars:11,consts:[[1,"mat-calendar-header"],[1,"mat-calendar-controls"],["aria-live","polite",1,"cdk-visually-hidden",3,"id"],["mat-button","","type","button",1,"mat-calendar-period-button",3,"click"],["aria-hidden","true"],["viewBox","0 0 10 5","focusable","false","aria-hidden","true",1,"mat-calendar-arrow"],["points","0,0 5,5 10,0"],[1,"mat-calendar-spacer"],["mat-icon-button","","type","button",1,"mat-calendar-previous-button",3,"click","disabled"],["viewBox","0 0 24 24","focusable","false","aria-hidden","true"],["d","M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"],["mat-icon-button","","type","button",1,"mat-calendar-next-button",3,"click","disabled"],["d","M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"]],template:function(a,t){a&1&&(Pe(),l(0,"div",0)(1,"div",1)(2,"span",2),f(3),c(),l(4,"button",3),m("click",function(){return t.currentPeriodClicked()}),l(5,"span",4),f(6),c(),W(),l(7,"svg",5),k(8,"polygon",6),c()(),Ee(),k(9,"div",7),Ye(10),l(11,"button",8),m("click",function(){return t.previousClicked()}),W(),l(12,"svg",9),k(13,"path",10),c()(),Ee(),l(14,"button",11),m("click",function(){return t.nextClicked()}),W(),l(15,"svg",9),k(16,"path",12),c()()()()),a&2&&(d(2),u("id",t._periodButtonLabelId),d(),Y(t.periodButtonDescription),d(),v("aria-label",t.periodButtonLabel)("aria-describedby",t._periodButtonLabelId),d(2),Y(t.periodButtonText),d(),I("mat-calendar-invert",t.calendar.currentView!=="month"),d(4),u("disabled",!t.previousEnabled()),v("aria-label",t.prevButtonLabel),d(3),u("disabled",!t.nextEnabled()),v("aria-label",t.nextButtonLabel))},dependencies:[Be,Ne],encapsulation:2,changeDetection:0})}return r})(),$e=(()=>{class r{_dateAdapter=o(C,{optional:!0});_dateFormats=o(N,{optional:!0});_changeDetectorRef=o(E);headerComponent;_calendarHeaderPortal;_intlChanges;_moveFocusOnNextTick=!1;get startAt(){return this._startAt}set startAt(e){this._startAt=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_startAt;startView="month";get selected(){return this._selected}set selected(e){e instanceof D?this._selected=e:this._selected=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_selected;get minDate(){return this._minDate}set minDate(e){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_minDate;get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_maxDate;dateFilter;dateClass;comparisonStart;comparisonEnd;startDateAccessibleName;endDateAccessibleName;selectedChange=new p;yearSelected=new p;monthSelected=new p;viewChanged=new p(!0);_userSelection=new p;_userDragDrop=new p;monthView;yearView;multiYearView;get activeDate(){return this._clampedActiveDate}set activeDate(e){this._clampedActiveDate=this._dateAdapter.clampDate(e,this.minDate,this.maxDate),this.stateChanges.next(),this._changeDetectorRef.markForCheck()}_clampedActiveDate;get currentView(){return this._currentView}set currentView(e){let a=this._currentView!==e?e:null;this._currentView=e,this._moveFocusOnNextTick=!0,this._changeDetectorRef.markForCheck(),a&&this.viewChanged.emit(a)}_currentView;_activeDrag=null;stateChanges=new F;constructor(){this._intlChanges=o(K).changes.subscribe(()=>{this._changeDetectorRef.markForCheck(),this.stateChanges.next()})}ngAfterContentInit(){this._calendarHeaderPortal=new He(this.headerComponent||Qt),this.activeDate=this.startAt||this._dateAdapter.today(),this._currentView=this.startView}ngAfterViewChecked(){this._moveFocusOnNextTick&&(this._moveFocusOnNextTick=!1,this.focusActiveCell())}ngOnDestroy(){this._intlChanges.unsubscribe(),this.stateChanges.complete()}ngOnChanges(e){let a=e.minDate&&!this._dateAdapter.sameDate(e.minDate.previousValue,e.minDate.currentValue)?e.minDate:void 0,t=e.maxDate&&!this._dateAdapter.sameDate(e.maxDate.previousValue,e.maxDate.currentValue)?e.maxDate:void 0,i=a||t||e.dateFilter;if(i&&!i.firstChange){let n=this._getCurrentViewComponent();n&&(this._moveFocusOnNextTick=!0,this._changeDetectorRef.detectChanges(),n._init())}this.stateChanges.next()}focusActiveCell(){this._getCurrentViewComponent()._focusActiveCell(!1)}updateTodaysDate(){this._getCurrentViewComponent()._init()}_dateSelected(e){let a=e.value;(this.selected instanceof D||a&&!this._dateAdapter.sameDate(a,this.selected))&&this.selectedChange.emit(a),this._userSelection.emit(e)}_yearSelectedInMultiYearView(e){this.yearSelected.emit(e)}_monthSelectedInYearView(e){this.monthSelected.emit(e)}_goToDateInView(e,a){this.activeDate=e,this.currentView=a}_dragStarted(e){this._activeDrag=e}_dragEnded(e){this._activeDrag&&(e.value&&this._userDragDrop.emit(e),this._activeDrag=null)}_getCurrentViewComponent(){return this.monthView||this.yearView||this.multiYearView}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-calendar"]],viewQuery:function(a,t){if(a&1&&(V(Yt,5),V(Nt,5),V(Lt,5)),a&2){let i;M(i=S())&&(t.monthView=i.first),M(i=S())&&(t.yearView=i.first),M(i=S())&&(t.multiYearView=i.first)}},hostAttrs:[1,"mat-calendar"],inputs:{headerComponent:"headerComponent",startAt:"startAt",startView:"startView",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",monthSelected:"monthSelected",viewChanged:"viewChanged",_userSelection:"_userSelection",_userDragDrop:"_userDragDrop"},exportAs:["matCalendar"],features:[ge([jt]),R],decls:5,vars:2,consts:[[3,"cdkPortalOutlet"],["cdkMonitorSubtreeFocus","","tabindex","-1",1,"mat-calendar-content"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","_userSelection","dragStarted","dragEnded","activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDateChange","monthSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","yearSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"]],template:function(a,t){if(a&1&&(T(0,ha,0,0,"ng-template",0),l(1,"div",1),T(2,ua,1,11,"mat-month-view",2)(3,ma,1,6,"mat-year-view",3)(4,_a,1,6,"mat-multi-year-view",3),c()),a&2){let i;u("cdkPortalOutlet",t._calendarHeaderPortal),d(2),Q((i=t.currentView)==="month"?2:i==="year"?3:i==="multi-year"?4:-1)}},dependencies:[ze,bt,Yt,Nt,Lt],styles:[`.mat-calendar{display:block;line-height:normal;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-sys-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-sys-body-medium-size))}.mat-calendar-header{padding:8px 8px 0 8px}.mat-calendar-content{padding:0 8px 8px 8px;outline:none}.mat-calendar-controls{display:flex;align-items:center;margin:5% calc(4.7142857143% - 16px)}.mat-calendar-spacer{flex:1 1 auto}.mat-calendar-period-button{min-width:0;margin:0 8px;font-size:var(--mat-datepicker-calendar-period-button-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-period-button-text-weight, var(--mat-sys-title-small-weight));--mdc-text-button-label-text-color:var(--mat-datepicker-calendar-period-button-text-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow{display:inline-block;width:10px;height:5px;margin:0 0 0 5px;vertical-align:middle;fill:var(--mat-datepicker-calendar-period-button-icon-color, var(--mat-sys-on-surface-variant))}.mat-calendar-arrow.mat-calendar-invert{transform:rotate(180deg)}[dir=rtl] .mat-calendar-arrow{margin:0 5px 0 0}@media(forced-colors: active){.mat-calendar-arrow{fill:CanvasText}}.mat-datepicker-content .mat-calendar-previous-button:not(.mat-mdc-button-disabled),.mat-datepicker-content .mat-calendar-next-button:not(.mat-mdc-button-disabled){color:var(--mat-datepicker-calendar-navigation-button-icon-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-calendar-previous-button,[dir=rtl] .mat-calendar-next-button{transform:rotate(180deg)}.mat-calendar-table{border-spacing:0;border-collapse:collapse;width:100%}.mat-calendar-table-header th{text-align:center;padding:0 0 8px 0;color:var(--mat-datepicker-calendar-header-text-color, var(--mat-sys-on-surface-variant));font-size:var(--mat-datepicker-calendar-header-text-size, var(--mat-sys-title-small-size));font-weight:var(--mat-datepicker-calendar-header-text-weight, var(--mat-sys-title-small-weight))}.mat-calendar-table-header-divider{position:relative;height:1px}.mat-calendar-table-header-divider::after{content:"";position:absolute;top:0;left:-8px;right:-8px;height:1px;background:var(--mat-datepicker-calendar-header-divider-color, transparent)}.mat-calendar-body-cell-content::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}.mat-calendar-body-cell:focus .mat-focus-indicator::before{content:""}
`],encapsulation:2,changeDetection:0})}return r})(),Gt=new Ve("mat-datepicker-scroll-strategy",{providedIn:"root",factory:()=>{let r=o(ve);return()=>r.scrollStrategies.reposition()}});function Sa(r){return()=>r.scrollStrategies.reposition()}var Va={provide:Gt,deps:[ve],useFactory:Sa},Ut=(()=>{class r{_elementRef=o(le);_animationsDisabled=o(dt,{optional:!0})==="NoopAnimations";_changeDetectorRef=o(E);_globalModel=o(ee);_dateAdapter=o(C);_ngZone=o(xe);_rangeSelectionStrategy=o(Kt,{optional:!0});_stateChanges;_model;_eventCleanups;_animationFallback;_calendar;color;datepicker;comparisonStart;comparisonEnd;startDateAccessibleName;endDateAccessibleName;_isAbove;_animationDone=new F;_isAnimating=!1;_closeButtonText;_closeButtonFocused;_actionsPortal=null;_dialogLabelId;constructor(){if(o(U).load(fe),this._closeButtonText=o(K).closeCalendarLabel,!this._animationsDisabled){let e=this._elementRef.nativeElement,a=o(Re);this._eventCleanups=this._ngZone.runOutsideAngular(()=>[a.listen(e,"animationstart",this._handleAnimationEvent),a.listen(e,"animationend",this._handleAnimationEvent),a.listen(e,"animationcancel",this._handleAnimationEvent)])}}ngAfterViewInit(){this._stateChanges=this.datepicker.stateChanges.subscribe(()=>{this._changeDetectorRef.markForCheck()}),this._calendar.focusActiveCell()}ngOnDestroy(){clearTimeout(this._animationFallback),this._eventCleanups?.forEach(e=>e()),this._stateChanges?.unsubscribe(),this._animationDone.complete()}_handleUserSelection(e){let a=this._model.selection,t=e.value,i=a instanceof D;if(i&&this._rangeSelectionStrategy){let n=this._rangeSelectionStrategy.selectionFinished(t,a,e.event);this._model.updateSelection(n,this)}else t&&(i||!this._dateAdapter.sameDate(t,a))&&this._model.add(t);(!this._model||this._model.isComplete())&&!this._actionsPortal&&this.datepicker.close()}_handleUserDragDrop(e){this._model.updateSelection(e.value,this)}_startExitAnimation(){this._elementRef.nativeElement.classList.add("mat-datepicker-content-exit"),this._animationsDisabled?this._animationDone.next():(clearTimeout(this._animationFallback),this._animationFallback=setTimeout(()=>{this._isAnimating||this._animationDone.next()},200))}_handleAnimationEvent=e=>{let a=this._elementRef.nativeElement;e.target!==a||!e.animationName.startsWith("_mat-datepicker-content")||(clearTimeout(this._animationFallback),this._isAnimating=e.type==="animationstart",a.classList.toggle("mat-datepicker-content-animating",this._isAnimating),this._isAnimating||this._animationDone.next())};_getSelected(){return this._model.selection}_applyPendingSelection(){this._model!==this._globalModel&&this._globalModel.updateSelection(this._model.selection,this)}_assignActions(e,a){this._model=e?this._globalModel.clone():this._globalModel,this._actionsPortal=e,a&&this._changeDetectorRef.detectChanges()}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-datepicker-content"]],viewQuery:function(a,t){if(a&1&&V($e,5),a&2){let i;M(i=S())&&(t._calendar=i.first)}},hostAttrs:[1,"mat-datepicker-content"],hostVars:6,hostBindings:function(a,t){a&2&&(Oe(t.color?"mat-"+t.color:""),I("mat-datepicker-content-touch",t.datepicker.touchUi)("mat-datepicker-content-animations-enabled",!t._animationsDisabled))},inputs:{color:"color"},exportAs:["matDatepickerContent"],decls:5,vars:26,consts:[["cdkTrapFocus","","role","dialog",1,"mat-datepicker-content-container"],[3,"yearSelected","monthSelected","viewChanged","_userSelection","_userDragDrop","id","startAt","startView","minDate","maxDate","dateFilter","headerComponent","selected","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName"],[3,"cdkPortalOutlet"],["type","button","mat-raised-button","",1,"mat-datepicker-close-button",3,"focus","blur","click","color"]],template:function(a,t){if(a&1&&(l(0,"div",0)(1,"mat-calendar",1),m("yearSelected",function(n){return t.datepicker._selectYear(n)})("monthSelected",function(n){return t.datepicker._selectMonth(n)})("viewChanged",function(n){return t.datepicker._viewChanged(n)})("_userSelection",function(n){return t._handleUserSelection(n)})("_userDragDrop",function(n){return t._handleUserDragDrop(n)}),c(),T(2,ga,0,0,"ng-template",2),l(3,"button",3),m("focus",function(){return t._closeButtonFocused=!0})("blur",function(){return t._closeButtonFocused=!1})("click",function(){return t.datepicker.close()}),f(4),c()()),a&2){let i;I("mat-datepicker-content-container-with-custom-header",t.datepicker.calendarHeaderComponent)("mat-datepicker-content-container-with-actions",t._actionsPortal),v("aria-modal",!0)("aria-labelledby",(i=t._dialogLabelId)!==null&&i!==void 0?i:void 0),d(),Oe(t.datepicker.panelClass),u("id",t.datepicker.id)("startAt",t.datepicker.startAt)("startView",t.datepicker.startView)("minDate",t.datepicker._getMinDate())("maxDate",t.datepicker._getMaxDate())("dateFilter",t.datepicker._getDateFilter())("headerComponent",t.datepicker.calendarHeaderComponent)("selected",t._getSelected())("dateClass",t.datepicker.dateClass)("comparisonStart",t.comparisonStart)("comparisonEnd",t.comparisonEnd)("startDateAccessibleName",t.startDateAccessibleName)("endDateAccessibleName",t.endDateAccessibleName),d(),u("cdkPortalOutlet",t._actionsPortal),d(),I("cdk-visually-hidden",!t._closeButtonFocused),u("color",t.color||"primary"),d(),Y(t._closeButtonText)}},dependencies:[vt,$e,ze,Be],styles:[`@keyframes _mat-datepicker-content-dropdown-enter{from{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-dialog-enter{from{opacity:0;transform:scale(0.8)}to{opacity:1;transform:none}}@keyframes _mat-datepicker-content-exit{from{opacity:1}to{opacity:0}}.mat-datepicker-content{display:block;border-radius:4px;background-color:var(--mat-datepicker-calendar-container-background-color, var(--mat-sys-surface-container-high));color:var(--mat-datepicker-calendar-container-text-color, var(--mat-sys-on-surface));box-shadow:var(--mat-datepicker-calendar-container-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-shape, var(--mat-sys-corner-large))}.mat-datepicker-content.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dropdown-enter 120ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content .mat-calendar{width:296px;height:354px}.mat-datepicker-content .mat-datepicker-content-container-with-custom-header .mat-calendar{height:auto}.mat-datepicker-content .mat-datepicker-close-button{position:absolute;top:100%;left:0;margin-top:8px}.mat-datepicker-content-animating .mat-datepicker-content .mat-datepicker-close-button{display:none}.mat-datepicker-content-container{display:flex;flex-direction:column;justify-content:space-between}.mat-datepicker-content-touch{display:block;max-height:80vh;box-shadow:var(--mat-datepicker-calendar-container-touch-elevation-shadow, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));border-radius:var(--mat-datepicker-calendar-container-touch-shape, var(--mat-sys-corner-extra-large));position:relative;overflow:visible}.mat-datepicker-content-touch.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-dialog-enter 150ms cubic-bezier(0, 0, 0.2, 1)}.mat-datepicker-content-touch .mat-datepicker-content-container{min-height:312px;max-height:788px;min-width:250px;max-width:750px}.mat-datepicker-content-touch .mat-calendar{width:100%;height:auto}.mat-datepicker-content-exit.mat-datepicker-content-animations-enabled{animation:_mat-datepicker-content-exit 100ms linear}@media all and (orientation: landscape){.mat-datepicker-content-touch .mat-datepicker-content-container{width:64vh;height:80vh}}@media all and (orientation: portrait){.mat-datepicker-content-touch .mat-datepicker-content-container{width:80vw;height:100vw}.mat-datepicker-content-touch .mat-datepicker-content-container-with-actions{height:115vw}}
`],encapsulation:2,changeDetection:0})}return r})(),Bt=(()=>{class r{_overlay=o(ve);_viewContainerRef=o(ct);_dateAdapter=o(C,{optional:!0});_dir=o($,{optional:!0});_model=o(ee);_scrollStrategy=o(Gt);_inputStateChanges=w.EMPTY;_document=o(mt);calendarHeaderComponent;get startAt(){return this._startAt||(this.datepickerInput?this.datepickerInput.getStartValue():null)}set startAt(e){this._startAt=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e))}_startAt;startView="month";get color(){return this._color||(this.datepickerInput?this.datepickerInput.getThemePalette():void 0)}set color(e){this._color=e}_color;touchUi=!1;get disabled(){return this._disabled===void 0&&this.datepickerInput?this.datepickerInput.disabled:!!this._disabled}set disabled(e){e!==this._disabled&&(this._disabled=e,this.stateChanges.next(void 0))}_disabled;xPosition="start";yPosition="below";restoreFocus=!0;yearSelected=new p;monthSelected=new p;viewChanged=new p(!0);dateClass;openedStream=new p;closedStream=new p;get panelClass(){return this._panelClass}set panelClass(e){this._panelClass=yt(e)}_panelClass;get opened(){return this._opened}set opened(e){e?this.open():this.close()}_opened=!1;id=o(be).getId("mat-datepicker-");_getMinDate(){return this.datepickerInput&&this.datepickerInput.min}_getMaxDate(){return this.datepickerInput&&this.datepickerInput.max}_getDateFilter(){return this.datepickerInput&&this.datepickerInput.dateFilter}_overlayRef;_componentRef;_focusedElementBeforeOpen=null;_backdropHarnessClass=`${this.id}-backdrop`;_actionsPortal;datepickerInput;stateChanges=new F;_injector=o(Ie);_changeDetectorRef=o(E);constructor(){this._dateAdapter,this._model.selectionChanged.subscribe(()=>{this._changeDetectorRef.markForCheck()})}ngOnChanges(e){let a=e.xPosition||e.yPosition;if(a&&!a.firstChange&&this._overlayRef){let t=this._overlayRef.getConfig().positionStrategy;t instanceof Vt&&(this._setConnectedPositions(t),this.opened&&this._overlayRef.updatePosition())}this.stateChanges.next(void 0)}ngOnDestroy(){this._destroyOverlay(),this.close(),this._inputStateChanges.unsubscribe(),this.stateChanges.complete()}select(e){this._model.add(e)}_selectYear(e){this.yearSelected.emit(e)}_selectMonth(e){this.monthSelected.emit(e)}_viewChanged(e){this.viewChanged.emit(e)}registerInput(e){return this.datepickerInput,this._inputStateChanges.unsubscribe(),this.datepickerInput=e,this._inputStateChanges=e.stateChanges.subscribe(()=>this.stateChanges.next(void 0)),this._model}registerActions(e){this._actionsPortal,this._actionsPortal=e,this._componentRef?.instance._assignActions(e,!0)}removeActions(e){e===this._actionsPortal&&(this._actionsPortal=null,this._componentRef?.instance._assignActions(null,!0))}open(){this._opened||this.disabled||this._componentRef?.instance._isAnimating||(this.datepickerInput,this._focusedElementBeforeOpen=gt(),this._openOverlay(),this._opened=!0,this.openedStream.emit())}close(){if(!this._opened||this._componentRef?.instance._isAnimating)return;let e=this.restoreFocus&&this._focusedElementBeforeOpen&&typeof this._focusedElementBeforeOpen.focus=="function",a=()=>{this._opened&&(this._opened=!1,this.closedStream.emit())};if(this._componentRef){let{instance:t,location:i}=this._componentRef;t._animationDone.pipe(et(1)).subscribe(()=>{let n=this._document.activeElement;e&&(!n||n===this._document.activeElement||i.nativeElement.contains(n))&&this._focusedElementBeforeOpen.focus(),this._focusedElementBeforeOpen=null,this._destroyOverlay()}),t._startExitAnimation()}e?setTimeout(a):a()}_applyPendingSelection(){this._componentRef?.instance?._applyPendingSelection()}_forwardContentValues(e){e.datepicker=this,e.color=this.color,e._dialogLabelId=this.datepickerInput.getOverlayLabelId(),e._assignActions(this._actionsPortal,!1)}_openOverlay(){this._destroyOverlay();let e=this.touchUi,a=new He(Ut,this._viewContainerRef),t=this._overlayRef=this._overlay.create(new St({positionStrategy:e?this._getDialogStrategy():this._getDropdownStrategy(),hasBackdrop:!0,backdropClass:[e?"cdk-overlay-dark-backdrop":"mat-overlay-transparent-backdrop",this._backdropHarnessClass],direction:this._dir||"ltr",scrollStrategy:e?this._overlay.scrollStrategies.block():this._scrollStrategy(),panelClass:`mat-datepicker-${e?"dialog":"popup"}`}));this._getCloseStream(t).subscribe(i=>{i&&i.preventDefault(),this.close()}),t.keydownEvents().subscribe(i=>{let n=i.keyCode;(n===38||n===40||n===37||n===39||n===33||n===34)&&i.preventDefault()}),this._componentRef=t.attach(a),this._forwardContentValues(this._componentRef.instance),e||Fe(()=>{t.updatePosition()},{injector:this._injector})}_destroyOverlay(){this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=this._componentRef=null)}_getDialogStrategy(){return this._overlay.position().global().centerHorizontally().centerVertically()}_getDropdownStrategy(){let e=this._overlay.position().flexibleConnectedTo(this.datepickerInput.getConnectedOverlayOrigin()).withTransformOriginOn(".mat-datepicker-content").withFlexibleDimensions(!1).withViewportMargin(8).withLockedPosition();return this._setConnectedPositions(e)}_setConnectedPositions(e){let a=this.xPosition==="end"?"end":"start",t=a==="start"?"end":"start",i=this.yPosition==="above"?"bottom":"top",n=i==="top"?"bottom":"top";return e.withPositions([{originX:a,originY:n,overlayX:a,overlayY:i},{originX:a,originY:i,overlayX:a,overlayY:n},{originX:t,originY:n,overlayX:t,overlayY:i},{originX:t,originY:i,overlayX:t,overlayY:n}])}_getCloseStream(e){let a=["ctrlKey","shiftKey","metaKey"];return se(e.backdropClick(),e.detachments(),e.keydownEvents().pipe(Je(t=>t.keyCode===27&&!P(t)||this.datepickerInput&&P(t,"altKey")&&t.keyCode===38&&a.every(i=>!P(t,i)))))}static \u0275fac=function(a){return new(a||r)};static \u0275dir=q({type:r,inputs:{calendarHeaderComponent:"calendarHeaderComponent",startAt:"startAt",startView:"startView",color:"color",touchUi:[2,"touchUi","touchUi",O],disabled:[2,"disabled","disabled",O],xPosition:"xPosition",yPosition:"yPosition",restoreFocus:[2,"restoreFocus","restoreFocus",O],dateClass:"dateClass",panelClass:"panelClass",opened:[2,"opened","opened",O]},outputs:{yearSelected:"yearSelected",monthSelected:"monthSelected",viewChanged:"viewChanged",openedStream:"opened",closedStream:"closed"},features:[R]})}return r})(),Fi=(()=>{class r extends Bt{static \u0275fac=(()=>{let e;return function(t){return(e||(e=nt(r)))(t||r)}})();static \u0275cmp=A({type:r,selectors:[["mat-datepicker"]],exportAs:["matDatepicker"],features:[ge([jt,{provide:Bt,useExisting:r}]),Te],decls:0,vars:0,template:function(a,t){},encapsulation:2,changeDetection:0})}return r})(),B=class{target;targetElement;value;constructor(s,e){this.target=s,this.targetElement=e,this.value=this.target.value}},Ea=(()=>{class r{_elementRef=o(le);_dateAdapter=o(C,{optional:!0});_dateFormats=o(N,{optional:!0});_isInitialized;get value(){return this._model?this._getValueFromModel(this._model.selection):this._pendingValue}set value(e){this._assignValueProgrammatically(e)}_model;get disabled(){return!!this._disabled||this._parentDisabled()}set disabled(e){let a=e,t=this._elementRef.nativeElement;this._disabled!==a&&(this._disabled=a,this.stateChanges.next(void 0)),a&&this._isInitialized&&t.blur&&t.blur()}_disabled;dateChange=new p;dateInput=new p;stateChanges=new F;_onTouched=()=>{};_validatorOnChange=()=>{};_cvaOnChange=()=>{};_valueChangesSubscription=w.EMPTY;_localeSubscription=w.EMPTY;_pendingValue;_parseValidator=()=>this._lastValueValid?null:{matDatepickerParse:{text:this._elementRef.nativeElement.value}};_filterValidator=e=>{let a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e.value));return!a||this._matchesFilter(a)?null:{matDatepickerFilter:!0}};_minValidator=e=>{let a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e.value)),t=this._getMinDate();return!t||!a||this._dateAdapter.compareDate(t,a)<=0?null:{matDatepickerMin:{min:t,actual:a}}};_maxValidator=e=>{let a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e.value)),t=this._getMaxDate();return!t||!a||this._dateAdapter.compareDate(t,a)>=0?null:{matDatepickerMax:{max:t,actual:a}}};_getValidators(){return[this._parseValidator,this._minValidator,this._maxValidator,this._filterValidator]}_registerModel(e){this._model=e,this._valueChangesSubscription.unsubscribe(),this._pendingValue&&this._assignValue(this._pendingValue),this._valueChangesSubscription=this._model.selectionChanged.subscribe(a=>{if(this._shouldHandleChangeEvent(a)){let t=this._getValueFromModel(a.selection);this._lastValueValid=this._isValidValue(t),this._cvaOnChange(t),this._onTouched(),this._formatValue(t),this.dateInput.emit(new B(this,this._elementRef.nativeElement)),this.dateChange.emit(new B(this,this._elementRef.nativeElement))}})}_lastValueValid=!1;constructor(){this._localeSubscription=this._dateAdapter.localeChanges.subscribe(()=>{this._assignValueProgrammatically(this.value)})}ngAfterViewInit(){this._isInitialized=!0}ngOnChanges(e){Ia(e,this._dateAdapter)&&this.stateChanges.next(void 0)}ngOnDestroy(){this._valueChangesSubscription.unsubscribe(),this._localeSubscription.unsubscribe(),this.stateChanges.complete()}registerOnValidatorChange(e){this._validatorOnChange=e}validate(e){return this._validator?this._validator(e):null}writeValue(e){this._assignValueProgrammatically(e)}registerOnChange(e){this._cvaOnChange=e}registerOnTouched(e){this._onTouched=e}setDisabledState(e){this.disabled=e}_onKeydown(e){let a=["ctrlKey","shiftKey","metaKey"];P(e,"altKey")&&e.keyCode===40&&a.every(i=>!P(e,i))&&!this._elementRef.nativeElement.readOnly&&(this._openPopup(),e.preventDefault())}_onInput(e){let a=this._lastValueValid,t=this._dateAdapter.parse(e,this._dateFormats.parse.dateInput);this._lastValueValid=this._isValidValue(t),t=this._dateAdapter.getValidDateOrNull(t);let i=!this._dateAdapter.sameDate(t,this.value);!t||i?this._cvaOnChange(t):(e&&!this.value&&this._cvaOnChange(t),a!==this._lastValueValid&&this._validatorOnChange()),i&&(this._assignValue(t),this.dateInput.emit(new B(this,this._elementRef.nativeElement)))}_onChange(){this.dateChange.emit(new B(this,this._elementRef.nativeElement))}_onBlur(){this.value&&this._formatValue(this.value),this._onTouched()}_formatValue(e){this._elementRef.nativeElement.value=e!=null?this._dateAdapter.format(e,this._dateFormats.display.dateInput):""}_assignValue(e){this._model?(this._assignValueToModel(e),this._pendingValue=null):this._pendingValue=e}_isValidValue(e){return!e||this._dateAdapter.isValid(e)}_parentDisabled(){return!1}_assignValueProgrammatically(e){e=this._dateAdapter.deserialize(e),this._lastValueValid=this._isValidValue(e),e=this._dateAdapter.getValidDateOrNull(e),this._assignValue(e),this._formatValue(e)}_matchesFilter(e){let a=this._getDateFilter();return!a||a(e)}static \u0275fac=function(a){return new(a||r)};static \u0275dir=q({type:r,inputs:{value:"value",disabled:[2,"disabled","disabled",O]},outputs:{dateChange:"dateChange",dateInput:"dateInput"},features:[R]})}return r})();function Ia(r,s){let e=Object.keys(r);for(let a of e){let{previousValue:t,currentValue:i}=r[a];if(s.isDateInstance(t)&&s.isDateInstance(i)){if(!s.sameDate(t,i))return!0}else return!0}return!1}var xa={provide:It,useExisting:Se(()=>$t),multi:!0},Fa={provide:xt,useExisting:Se(()=>$t),multi:!0},$t=(()=>{class r extends Ea{_formField=o(Rt,{optional:!0});_closedSubscription=w.EMPTY;_openedSubscription=w.EMPTY;set matDatepicker(e){e&&(this._datepicker=e,this._ariaOwns.set(e.opened?e.id:null),this._closedSubscription=e.closedStream.subscribe(()=>{this._onTouched(),this._ariaOwns.set(null)}),this._openedSubscription=e.openedStream.subscribe(()=>{this._ariaOwns.set(e.id)}),this._registerModel(e.registerInput(this)))}_datepicker;_ariaOwns=ot(null);get min(){return this._min}set min(e){let a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e));this._dateAdapter.sameDate(a,this._min)||(this._min=a,this._validatorOnChange())}_min;get max(){return this._max}set max(e){let a=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(e));this._dateAdapter.sameDate(a,this._max)||(this._max=a,this._validatorOnChange())}_max;get dateFilter(){return this._dateFilter}set dateFilter(e){let a=this._matchesFilter(this.value);this._dateFilter=e,this._matchesFilter(this.value)!==a&&this._validatorOnChange()}_dateFilter;_validator;constructor(){super(),this._validator=Ft.compose(super._getValidators())}getConnectedOverlayOrigin(){return this._formField?this._formField.getConnectedOverlayOrigin():this._elementRef}getOverlayLabelId(){return this._formField?this._formField.getLabelId():this._elementRef.nativeElement.getAttribute("aria-labelledby")}getThemePalette(){return this._formField?this._formField.color:void 0}getStartValue(){return this.value}ngOnDestroy(){super.ngOnDestroy(),this._closedSubscription.unsubscribe(),this._openedSubscription.unsubscribe()}_openPopup(){this._datepicker&&this._datepicker.open()}_getValueFromModel(e){return e}_assignValueToModel(e){this._model&&this._model.updateSelection(e,this)}_getMinDate(){return this._min}_getMaxDate(){return this._max}_getDateFilter(){return this._dateFilter}_shouldHandleChangeEvent(e){return e.source!==this}static \u0275fac=function(a){return new(a||r)};static \u0275dir=q({type:r,selectors:[["input","matDatepicker",""]],hostAttrs:[1,"mat-datepicker-input"],hostVars:6,hostBindings:function(a,t){a&1&&m("input",function(n){return t._onInput(n.target.value)})("change",function(){return t._onChange()})("blur",function(){return t._onBlur()})("keydown",function(n){return t._onKeydown(n)}),a&2&&(ht("disabled",t.disabled),v("aria-haspopup",t._datepicker?"dialog":null)("aria-owns",t._ariaOwns())("min",t.min?t._dateAdapter.toIso8601(t.min):null)("max",t.max?t._dateAdapter.toIso8601(t.max):null)("data-mat-calendar",t._datepicker?t._datepicker.id:null))},inputs:{matDatepicker:"matDatepicker",min:"min",max:"max",dateFilter:[0,"matDatepickerFilter","dateFilter"]},exportAs:["matDatepickerInput"],features:[ge([xa,Fa,{provide:Tt,useExisting:r}]),Te]})}return r})(),Ra=(()=>{class r{static \u0275fac=function(a){return new(a||r)};static \u0275dir=q({type:r,selectors:[["","matDatepickerToggleIcon",""]]})}return r})(),Ta=(()=>{class r{_intl=o(K);_changeDetectorRef=o(E);_stateChanges=w.EMPTY;datepicker;tabIndex;ariaLabel;get disabled(){return this._disabled===void 0&&this.datepicker?this.datepicker.disabled:!!this._disabled}set disabled(e){this._disabled=e}_disabled;disableRipple;_customIcon;_button;constructor(){let e=o(new st("tabindex"),{optional:!0}),a=Number(e);this.tabIndex=a||a===0?a:null}ngOnChanges(e){e.datepicker&&this._watchStateChanges()}ngOnDestroy(){this._stateChanges.unsubscribe()}ngAfterContentInit(){this._watchStateChanges()}_open(e){this.datepicker&&!this.disabled&&(this.datepicker.open(),e.stopPropagation())}_watchStateChanges(){let e=this.datepicker?this.datepicker.stateChanges:ne(),a=this.datepicker&&this.datepicker.datepickerInput?this.datepicker.datepickerInput.stateChanges:ne(),t=this.datepicker?se(this.datepicker.openedStream,this.datepicker.closedStream):ne();this._stateChanges.unsubscribe(),this._stateChanges=se(this._intl.changes,e,a,t).subscribe(()=>this._changeDetectorRef.markForCheck())}static \u0275fac=function(a){return new(a||r)};static \u0275cmp=A({type:r,selectors:[["mat-datepicker-toggle"]],contentQueries:function(a,t,i){if(a&1&&ut(i,Ra,5),a&2){let n;M(n=S())&&(t._customIcon=n.first)}},viewQuery:function(a,t){if(a&1&&V(fa,5),a&2){let i;M(i=S())&&(t._button=i.first)}},hostAttrs:[1,"mat-datepicker-toggle"],hostVars:8,hostBindings:function(a,t){a&1&&m("click",function(n){return t._open(n)}),a&2&&(v("tabindex",null)("data-mat-calendar",t.datepicker?t.datepicker.id:null),I("mat-datepicker-toggle-active",t.datepicker&&t.datepicker.opened)("mat-accent",t.datepicker&&t.datepicker.color==="accent")("mat-warn",t.datepicker&&t.datepicker.color==="warn"))},inputs:{datepicker:[0,"for","datepicker"],tabIndex:"tabIndex",ariaLabel:[0,"aria-label","ariaLabel"],disabled:[2,"disabled","disabled",O],disableRipple:"disableRipple"},exportAs:["matDatepickerToggle"],features:[R],ngContentSelectors:va,decls:4,vars:7,consts:[["button",""],["mat-icon-button","","type","button",3,"disabled","disableRipple"],["viewBox","0 0 24 24","width","24px","height","24px","fill","currentColor","focusable","false","aria-hidden","true",1,"mat-datepicker-toggle-default-icon"],["d","M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"]],template:function(a,t){a&1&&(Pe(ba),l(0,"button",1,0),T(2,Da,2,0,":svg:svg",2),Ye(3),c()),a&2&&(u("disabled",t.disabled)("disableRipple",t.disableRipple),v("aria-haspopup",t.datepicker?"dialog":null)("aria-label",t.ariaLabel||t._intl.openCalendarLabel)("tabindex",t.disabled?-1:t.tabIndex)("aria-expanded",t.datepicker?t.datepicker.opened:null),d(2),Q(t._customIcon?-1:2))},dependencies:[Ne],styles:[`.mat-datepicker-toggle{pointer-events:auto;color:var(--mat-datepicker-toggle-icon-color, var(--mat-sys-on-surface-variant))}.mat-datepicker-toggle-active{color:var(--mat-datepicker-toggle-active-state-icon-color, var(--mat-sys-on-surface-variant))}@media(forced-colors: active){.mat-datepicker-toggle-default-icon{color:CanvasText}}
`],encapsulation:2,changeDetection:0})}return r})();var Ri=(()=>{class r{static \u0275fac=function(a){return new(a||r)};static \u0275mod=pt({type:r});static \u0275inj=tt({providers:[K,Va],imports:[At,Et,Dt,kt,wt,Ut,Ta,Qt,Mt]})}return r})();var Jt="basil",Oa=function(s){return s===3?"v3":s},ea="https://js.stripe.com",Pa="".concat(ea,"/").concat(Jt,"/stripe.js"),Ya=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,La=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,Xt="loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used",Na=function(s){return Ya.test(s)||La.test(s)},Ba=function(){for(var s=document.querySelectorAll('script[src^="'.concat(ea,'"]')),e=0;e<s.length;e++){var a=s[e];if(Na(a.src))return a}return null},Zt=function(s){var e=s&&!s.advancedFraudSignals?"?advancedFraudSignals=false":"",a=document.createElement("script");a.src="".concat(Pa).concat(e);var t=document.head||document.body;if(!t)throw new Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return t.appendChild(a),a},Ha=function(s,e){!s||!s._registerWrapper||s._registerWrapper({name:"stripe-js",version:"7.3.0",startTime:e})},ae=null,ke=null,Me=null,za=function(s){return function(e){s(new Error("Failed to load Stripe.js",{cause:e}))}},ja=function(s,e){return function(){window.Stripe?s(window.Stripe):e(new Error("Stripe.js not available"))}},Ka=function(s){return ae!==null?ae:(ae=new Promise(function(e,a){if(typeof window>"u"||typeof document>"u"){e(null);return}if(window.Stripe&&s&&console.warn(Xt),window.Stripe){e(window.Stripe);return}try{var t=Ba();if(t&&s)console.warn(Xt);else if(!t)t=Zt(s);else if(t&&Me!==null&&ke!==null){var i;t.removeEventListener("load",Me),t.removeEventListener("error",ke),(i=t.parentNode)===null||i===void 0||i.removeChild(t),t=Zt(s)}Me=ja(e,a),ke=za(a),t.addEventListener("load",Me),t.addEventListener("error",ke)}catch(n){a(n);return}}),ae.catch(function(e){return ae=null,Promise.reject(e)}))},Wa=function(s,e,a){if(s===null)return null;var t=e[0],i=t.match(/^pk_test/),n=Oa(s.version),y=Jt;i&&n!==y&&console.warn("Stripe.js@".concat(n," was loaded on the page, but @stripe/stripe-js@").concat("7.3.0"," expected Stripe.js@").concat(y,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var re=s.apply(void 0,e);return Ha(re,a),re},ie,ta=!1,aa=function(){return ie||(ie=Ka(null).catch(function(s){return ie=null,Promise.reject(s)}),ie)};Promise.resolve().then(function(){return aa()}).catch(function(r){ta||console.warn(r)});var Oi=function(){for(var s=arguments.length,e=new Array(s),a=0;a<s;a++)e[a]=arguments[a];ta=!0;var t=Date.now();return aa().then(function(i){return Wa(i,e,t)})};export{Fi as a,$t as b,Ta as c,Ri as d,Oi as e};
