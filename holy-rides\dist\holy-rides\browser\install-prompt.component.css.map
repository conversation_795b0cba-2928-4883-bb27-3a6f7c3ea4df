{"version": 3, "sources": ["src/app/shared/components/install-prompt/install-prompt.component.ts"], "sourcesContent": ["\n    .install-container {\n      position: fixed;\n      bottom: 0;\n      left: 0;\n      right: 0;\n      z-index: 999;\n      padding: 16px;\n    }\n\n    .install-card {\n      background-color: white;\n      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\n    }\n\n    .install-content {\n      display: flex;\n      align-items: center;\n      margin-bottom: 16px;\n    }\n\n    .install-text {\n      margin-left: 16px;\n    }\n\n    .install-text h3 {\n      margin: 0;\n      font-size: 18px;\n    }\n\n    .install-text p {\n      margin: 4px 0 0;\n      font-size: 14px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .install-actions {\n      display: flex;\n      justify-content: flex-end;\n    }\n\n    mat-icon {\n      font-size: 36px;\n      height: 36px;\n      width: 36px;\n      color: #3f51b5;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,WAAA;AACA,WAAA;;AAGF,CAAA;AACE,oBAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAJA,aAIA;AACE,UAAA;AACA,aAAA;;AAGF,CATA,aASA;AACE,UAAA,IAAA,EAAA;AACA,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;;AAGF;AACE,aAAA;AACA,UAAA;AACA,SAAA;AACA,SAAA;;", "names": []}