import{c as tr}from"./chunk-TUEROBIF.js";import{a as Oi,b as Bi,c as Ui,d as zi,e as qi}from"./chunk-HNQXKFUQ.js";import{A as Pi,h as be,i as lt,j as Si,k as yi,l as Ce,m as xe,n as Me,o as ie,r as <PERSON>,u as Lt,v as Ri,w as ki,x as Ii,y as Ei,z as Ti}from"./chunk-XDJELRYM.js";import"./chunk-MS4AQ6UA.js";import{a as Ji,b as <PERSON>,c as Zi,d as Yi,e as er}from"./chunk-JRFUAQJR.js";import{b as Xi}from"./chunk-EMQER2I7.js";import{a as $i,b as ze,c as ji,d as Gi,e as Hi,f as Qi,g as Wi,h as ut,j as gt,k as ht,l as ft,m as _t,n as vt,o as wt,p as St,q as yt,r as bt,s as Ct,t as xt,u as Ne,v as jt,w as Mt,x as Dt}from"./chunk-4GJZFU7Z.js";import{a as le,b as ce,c as me,d as Li,e as Ve}from"./chunk-4XM5VEPX.js";import{a as Vi,b as Ni}from"./chunk-OFTCLERB.js";import"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as oe,b as J}from"./chunk-WSXVBUWR.js";import{a as Z}from"./chunk-3NZGXQSR.js";import{a as Te,c as Ae,d as ae}from"./chunk-ZN5FMN3P.js";import"./chunk-YTNZ52NK.js";import{a as $,b as te}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{C as W,F as X,H as se,I as de,J as Ai,a as bi,b as re,c as Ci,d as F,f as G,g as De,j as ct,k as Re,l as mt,n as ke,o as Ie,p as xi,q as Mi,s as Ee,t as H,u as ne,w as Q,x as Pe,z as pt}from"./chunk-AG3SD6JT.js";import{$b as at,Ab as He,Ad as vi,Bb as Qe,Bd as dt,Cd as wi,Db as mi,Ea as si,Eb as Y,Ed as ye,Fb as L,Fc as z,Gb as q,Gd as V,Jb as ee,Jd as N,Kb as s,Kd as qt,La as l,Lb as M,Ld as Fe,Mb as R,Md as Oe,Nd as Be,Ob as ge,Od as Fi,Pa as y,Pb as he,Qb as fe,Qd as Ue,Rd as K,Sb as nt,Tb as $t,Tc as st,Wa as O,Wc as gi,Xa as rt,Y as ii,Yc as hi,Z as ri,_ as et,aa as tt,ab as p,ca as it,da as U,dc as pi,fc as _e,g as ti,gb as ue,gc as ui,hb as m,hd as fi,jb as Ge,ka as ni,kb as di,la as b,ma as C,mb as li,na as ai,qb as n,rb as o,sa as oi,sb as f,tb as E,ub as T,uc as ot,va as Nt,vc as j,wb as P,xb as ci,yb as S,yd as _i,za as je,zb as g}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as Le,b as qe,i as x}from"./chunk-ODN5LVDJ.js";var Rt=class{tracker;columnIndex=0;rowIndex=0;get rowCount(){return this.rowIndex+1}get rowspan(){let t=Math.max(...this.tracker);return t>1?this.rowCount+t-1:this.rowCount}positions;update(t,e){this.columnIndex=0,this.rowIndex=0,this.tracker=new Array(t),this.tracker.fill(0,0,this.tracker.length),this.positions=e.map(i=>this._trackTile(i))}_trackTile(t){let e=this._findMatchingGap(t.colspan);return this._markTilePosition(e,t),this.columnIndex=e+t.colspan,new Gt(this.rowIndex,e)}_findMatchingGap(t){t>this.tracker.length;let e=-1,i=-1;do{if(this.columnIndex+t>this.tracker.length){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}if(e=this.tracker.indexOf(0,this.columnIndex),e==-1){this._nextRow(),e=this.tracker.indexOf(0,this.columnIndex),i=this._findGapEndIndex(e);continue}i=this._findGapEndIndex(e),this.columnIndex=e+1}while(i-e<t||i==0);return Math.max(e,0)}_nextRow(){this.columnIndex=0,this.rowIndex++;for(let t=0;t<this.tracker.length;t++)this.tracker[t]=Math.max(0,this.tracker[t]-1)}_findGapEndIndex(t){for(let e=t+1;e<this.tracker.length;e++)if(this.tracker[e]!=0)return e;return this.tracker.length}_markTilePosition(t,e){for(let i=0;i<e.colspan;i++)this.tracker[t+i]=e.rowspan}},Gt=class{row;col;constructor(t,e){this.row=t,this.col=e}};var ir=["*"];var hr=`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`,rr=new tt("MAT_GRID_LIST"),Xt=(()=>{class r{_element=U(je);_gridList=U(rr,{optional:!0});_rowspan=1;_colspan=1;constructor(){}get rowspan(){return this._rowspan}set rowspan(e){this._rowspan=Math.round(st(e))}get colspan(){return this._colspan}set colspan(e){this._colspan=Math.round(st(e))}_setStyle(e,i){this._element.nativeElement.style[e]=i}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=O({type:r,selectors:[["mat-grid-tile"]],hostAttrs:[1,"mat-grid-tile"],hostVars:2,hostBindings:function(i,a){i&2&&ue("rowspan",a.rowspan)("colspan",a.colspan)},inputs:{rowspan:"rowspan",colspan:"colspan"},exportAs:["matGridTile"],ngContentSelectors:ir,decls:2,vars:0,consts:[[1,"mat-grid-tile-content"]],template:function(i,a){i&1&&(He(),n(0,"div",0),Qe(1),o())},styles:[`.mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}
`],encapsulation:2,changeDetection:0})}return r})();var fr=/^-?\d+((\.\d+)?[A-Za-z%$]?)+$/,We=class{_gutterSize;_rows=0;_rowspan=0;_cols;_direction;init(t,e,i,a){this._gutterSize=nr(t),this._rows=e.rowCount,this._rowspan=e.rowspan,this._cols=i,this._direction=a}getBaseTileSize(t,e){return`(${t}% - (${this._gutterSize} * ${e}))`}getTilePosition(t,e){return e===0?"0":ve(`(${t} + ${this._gutterSize}) * ${e}`)}getTileSize(t,e){return`(${t} * ${e}) + (${e-1} * ${this._gutterSize})`}setStyle(t,e,i){let a=100/this._cols,d=(this._cols-1)/this._cols;this.setColStyles(t,i,a,d),this.setRowStyles(t,e,a,d)}setColStyles(t,e,i,a){let d=this.getBaseTileSize(i,a),c=this._direction==="rtl"?"right":"left";t._setStyle(c,this.getTilePosition(d,e)),t._setStyle("width",ve(this.getTileSize(d,t.colspan)))}getGutterSpan(){return`${this._gutterSize} * (${this._rowspan} - 1)`}getTileSpan(t){return`${this._rowspan} * ${this.getTileSize(t,1)}`}getComputedHeight(){return null}},Ht=class extends We{fixedRowHeight;constructor(t){super(),this.fixedRowHeight=t}init(t,e,i,a){super.init(t,e,i,a),this.fixedRowHeight=nr(this.fixedRowHeight),fr.test(this.fixedRowHeight)}setRowStyles(t,e){t._setStyle("top",this.getTilePosition(this.fixedRowHeight,e)),t._setStyle("height",ve(this.getTileSize(this.fixedRowHeight,t.rowspan)))}getComputedHeight(){return["height",ve(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["height",null]),t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}},Qt=class extends We{rowHeightRatio;baseTileHeight;constructor(t){super(),this._parseRatio(t)}setRowStyles(t,e,i,a){let d=i/this.rowHeightRatio;this.baseTileHeight=this.getBaseTileSize(d,a),t._setStyle("marginTop",this.getTilePosition(this.baseTileHeight,e)),t._setStyle("paddingTop",ve(this.getTileSize(this.baseTileHeight,t.rowspan)))}getComputedHeight(){return["paddingBottom",ve(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)]}reset(t){t._setListStyle(["paddingBottom",null]),t._tiles.forEach(e=>{e._setStyle("marginTop",null),e._setStyle("paddingTop",null)})}_parseRatio(t){let e=t.split(":");e.length,this.rowHeightRatio=parseFloat(e[0])/parseFloat(e[1])}},Wt=class extends We{setRowStyles(t,e){let i=100/this._rowspan,a=(this._rows-1)/this._rows,d=this.getBaseTileSize(i,a);t._setStyle("top",this.getTilePosition(d,e)),t._setStyle("height",ve(this.getTileSize(d,t.rowspan)))}reset(t){t._tiles&&t._tiles.forEach(e=>{e._setStyle("top",null),e._setStyle("height",null)})}};function ve(r){return`calc(${r})`}function nr(r){return r.match(/([A-Za-z%]+)$/)?r:`${r}px`}var _r="fit",ar=(()=>{class r{_element=U(je);_dir=U(wi,{optional:!0});_cols;_tileCoordinator;_rowHeight;_gutter="1px";_tileStyler;_tiles;constructor(){}get cols(){return this._cols}set cols(e){this._cols=Math.max(1,Math.round(st(e)))}get gutterSize(){return this._gutter}set gutterSize(e){this._gutter=`${e??""}`}get rowHeight(){return this._rowHeight}set rowHeight(e){let i=`${e??""}`;i!==this._rowHeight&&(this._rowHeight=i,this._setTileStyler(this._rowHeight))}ngOnInit(){this._checkCols(),this._checkRowHeight()}ngAfterContentChecked(){this._layoutTiles()}_checkCols(){this.cols}_checkRowHeight(){this._rowHeight||this._setTileStyler("1:1")}_setTileStyler(e){this._tileStyler&&this._tileStyler.reset(this),e===_r?this._tileStyler=new Wt:e&&e.indexOf(":")>-1?this._tileStyler=new Qt(e):this._tileStyler=new Ht(e)}_layoutTiles(){this._tileCoordinator||(this._tileCoordinator=new Rt);let e=this._tileCoordinator,i=this._tiles.filter(d=>!d._gridList||d._gridList===this),a=this._dir?this._dir.value:"ltr";this._tileCoordinator.update(this.cols,i),this._tileStyler.init(this.gutterSize,e,this.cols,a),i.forEach((d,c)=>{let h=e.positions[c];this._tileStyler.setStyle(d,h.row,h.col)}),this._setListStyle(this._tileStyler.getComputedHeight())}_setListStyle(e){e&&(this._element.nativeElement.style[e[0]]=e[1])}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=O({type:r,selectors:[["mat-grid-list"]],contentQueries:function(i,a,d){if(i&1&&mi(d,Xt,5),i&2){let c;L(c=q())&&(a._tiles=c)}},hostAttrs:[1,"mat-grid-list"],hostVars:1,hostBindings:function(i,a){i&2&&ue("cols",a.cols)},inputs:{cols:"cols",gutterSize:"gutterSize",rowHeight:"rowHeight"},exportAs:["matGridList"],features:[nt([{provide:rr,useExisting:r}])],ngContentSelectors:ir,decls:2,vars:0,template:function(i,a){i&1&&(He(),n(0,"div"),Qe(1),o())},styles:[hr],encapsulation:2,changeDetection:0})}return r})(),or=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=rt({type:r});static \u0275inj=et({imports:[Lt,ye,Lt,ye]})}return r})();function wr(r,t){r&1&&(n(0,"div",7),f(1,"mat-spinner",8),n(2,"p"),s(3,"Loading available drivers..."),o()())}function Sr(r,t){if(r&1&&(n(0,"span",15),s(1),o()),r&2){let e=g().$implicit;l(),R(" (",e.rating.toFixed(1)," \u2B50) ")}}function yr(r,t){if(r&1&&(n(0,"mat-option",13),s(1),p(2,Sr,2,1,"span",14),o()),r&2){let e=t.$implicit;m("value",e.id),l(),R(" ",e.full_name||e.email," "),l(),m("ngIf",e.rating)}}function br(r,t){r&1&&(n(0,"p",16),s(1," No approved drivers available at this time. "),o())}function Cr(r,t){if(r&1){let e=P();n(0,"div")(1,"mat-form-field",9)(2,"mat-label"),s(3,"Select Driver"),o(),n(4,"mat-select",10),fe("ngModelChange",function(a){b(e);let d=g();return he(d.selectedDriverId,a)||(d.selectedDriverId=a),C(a)}),p(5,yr,3,3,"mat-option",11),o()(),p(6,br,2,0,"p",12),o()}if(r&2){let e=g();l(4),ge("ngModel",e.selectedDriverId),m("disabled",e.loading),l(),m("ngForOf",e.availableDrivers),l(),m("ngIf",e.availableDrivers.length===0)}}var kt=class r{constructor(t,e,i,a,d){this.dialogRef=t;this.data=e;this.userService=i;this.ratingService=a;this.snackBar=d}availableDrivers=[];selectedDriverId="";loading=!1;ngOnInit(){this.loadAvailableDrivers()}loadAvailableDrivers(){return x(this,null,function*(){this.loading=!0;try{let t=yield this.userService.getAllUsers();this.availableDrivers=t.filter(e=>e.role==="driver"&&e.is_approved===!0),yield Promise.all(this.availableDrivers.map(e=>x(this,null,function*(){try{let i=yield this.ratingService.getUserRatingSummary(e.id);i&&(e.rating=i.averageRating)}catch(i){console.error(`Error loading rating for driver ${e.id}:`,i)}}))),this.availableDrivers.sort((e,i)=>(i.rating||0)-(e.rating||0)),this.availableDrivers.length===0&&this.snackBar.open("No available drivers found","Close",{duration:3e3})}catch(t){console.error("Error loading drivers:",t),this.snackBar.open("Failed to load available drivers","Close",{duration:3e3})}finally{this.loading=!1}})}onCancel(){this.dialogRef.close()}onAssign(){this.selectedDriverId?this.availableDrivers.some(t=>t.id===this.selectedDriverId)?this.dialogRef.close(this.selectedDriverId):this.snackBar.open("Selected driver is no longer available","Close",{duration:3e3}):this.snackBar.open("Please select a driver","Close",{duration:3e3})}static \u0275fac=function(e){return new(e||r)(y(be),y(lt),y(Z),y(Qi),y($))};static \u0275cmp=O({type:r,selectors:[["app-driver-selection-dialog"]],decls:10,vars:5,consts:[["mat-dialog-title",""],["mat-dialog-content",""],["class","loading-container",4,"ngIf"],[4,"ngIf"],["mat-dialog-actions","","align","end"],["mat-button","",3,"click","disabled"],["mat-raised-button","","color","primary",3,"click","disabled"],[1,"loading-container"],["diameter","40"],["appearance","outline",2,"width","100%"],[3,"ngModelChange","ngModel","disabled"],[3,"value",4,"ngFor","ngForOf"],["class","no-drivers-message",4,"ngIf"],[3,"value"],["class","driver-rating",4,"ngIf"],[1,"driver-rating"],[1,"no-drivers-message"]],template:function(e,i){e&1&&(n(0,"h2",0),s(1,"Assign Driver"),o(),n(2,"div",1),p(3,wr,4,0,"div",2)(4,Cr,7,4,"div",3),o(),n(5,"div",4)(6,"button",5),S("click",function(){return i.onCancel()}),s(7,"Cancel"),o(),n(8,"button",6),S("click",function(){return i.onAssign()}),s(9),o()()),e&2&&(l(3),m("ngIf",i.loading),l(),m("ngIf",!i.loading),l(2),m("disabled",i.loading),l(2),m("disabled",!i.selectedDriverId||i.loading),l(),R(" ",i.loading?"Assigning...":"Assign"," "))},dependencies:[z,ot,j,H,G,ct,ie,Ce,Me,xe,X,W,Q,ae,Ae,Te,N,V,me,ce],styles:[".loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px;color:#0000008a}.no-drivers-message[_ngcontent-%COMP%]{color:#f44336;font-style:italic;text-align:center;margin:16px 0}.driver-rating[_ngcontent-%COMP%]{margin-left:8px;color:#0000008a}"]})};var xr=["switch"],Mr=["*"];function Dr(r,t){r&1&&(n(0,"span",10),ai(),n(1,"svg",12),f(2,"path",13),o(),n(3,"svg",14),f(4,"path",15),o()())}var Rr=new tt("mat-slide-toggle-default-options",{providedIn:"root",factory:()=>({disableToggleValue:!1,hideIcon:!1,disabledInteractive:!1})}),kr={provide:bi,useExisting:ii(()=>$e),multi:!0},At=class{source;checked;constructor(t,e){this.source=t,this.checked=e}},$e=(()=>{class r{_elementRef=U(je);_focusMonitor=U(gi);_changeDetectorRef=U(pi);defaults=U(Rr);_onChange=e=>{};_onTouched=()=>{};_validatorOnChange=()=>{};_uniqueId;_checked=!1;_createChangeEvent(e){return new At(this,e)}_labelId;get buttonId(){return`${this.id||this._uniqueId}-button`}_switchElement;focus(){this._switchElement.nativeElement.focus()}_noopAnimations;_focused;name=null;id;labelPosition="after";ariaLabel=null;ariaLabelledby=null;ariaDescribedby;required;color;disabled=!1;disableRipple=!1;tabIndex=0;get checked(){return this._checked}set checked(e){this._checked=e,this._changeDetectorRef.markForCheck()}hideIcon;disabledInteractive;change=new Nt;toggleChange=new Nt;get inputId(){return`${this.id||this._uniqueId}-input`}constructor(){U(hi).load(vi);let e=U(new oi("tabindex"),{optional:!0}),i=this.defaults,a=U(si,{optional:!0});this.tabIndex=e==null?0:parseInt(e)||0,this.color=i.color||"accent",this._noopAnimations=a==="NoopAnimations",this.id=this._uniqueId=U(fi).getId("mat-mdc-slide-toggle-"),this.hideIcon=i.hideIcon??!1,this.disabledInteractive=i.disabledInteractive??!1,this._labelId=this._uniqueId+"-label"}ngAfterContentInit(){this._focusMonitor.monitor(this._elementRef,!0).subscribe(e=>{e==="keyboard"||e==="program"?(this._focused=!0,this._changeDetectorRef.markForCheck()):e||Promise.resolve().then(()=>{this._focused=!1,this._onTouched(),this._changeDetectorRef.markForCheck()})})}ngOnChanges(e){e.required&&this._validatorOnChange()}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef)}writeValue(e){this.checked=!!e}registerOnChange(e){this._onChange=e}registerOnTouched(e){this._onTouched=e}validate(e){return this.required&&e.value!==!0?{required:!0}:null}registerOnValidatorChange(e){this._validatorOnChange=e}setDisabledState(e){this.disabled=e,this._changeDetectorRef.markForCheck()}toggle(){this.checked=!this.checked,this._onChange(this.checked)}_emitChangeEvent(){this._onChange(this.checked),this.change.emit(this._createChangeEvent(this.checked))}_handleClick(){this.disabled||(this.toggleChange.emit(),this.defaults.disableToggleValue||(this.checked=!this.checked,this._onChange(this.checked),this.change.emit(new At(this,this.checked))))}_getAriaLabelledBy(){return this.ariaLabelledby?this.ariaLabelledby:this.ariaLabel?null:this._labelId}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=O({type:r,selectors:[["mat-slide-toggle"]],viewQuery:function(i,a){if(i&1&&Y(xr,5),i&2){let d;L(d=q())&&(a._switchElement=d.first)}},hostAttrs:[1,"mat-mdc-slide-toggle"],hostVars:13,hostBindings:function(i,a){i&2&&(ci("id",a.id),ue("tabindex",null)("aria-label",null)("name",null)("aria-labelledby",null),di(a.color?"mat-"+a.color:""),Ge("mat-mdc-slide-toggle-focused",a._focused)("mat-mdc-slide-toggle-checked",a.checked)("_mat-animation-noopable",a._noopAnimations))},inputs:{name:"name",id:"id",labelPosition:"labelPosition",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],required:[2,"required","required",_e],color:"color",disabled:[2,"disabled","disabled",_e],disableRipple:[2,"disableRipple","disableRipple",_e],tabIndex:[2,"tabIndex","tabIndex",e=>e==null?0:ui(e)],checked:[2,"checked","checked",_e],hideIcon:[2,"hideIcon","hideIcon",_e],disabledInteractive:[2,"disabledInteractive","disabledInteractive",_e]},outputs:{change:"change",toggleChange:"toggleChange"},exportAs:["matSlideToggle"],features:[nt([kr,{provide:Ci,useExisting:r,multi:!0}]),ni],ngContentSelectors:Mr,decls:13,vars:27,consts:[["switch",""],["mat-internal-form-field","",3,"labelPosition"],["role","switch","type","button",1,"mdc-switch",3,"click","tabIndex","disabled"],[1,"mdc-switch__track"],[1,"mdc-switch__handle-track"],[1,"mdc-switch__handle"],[1,"mdc-switch__shadow"],[1,"mdc-elevation-overlay"],[1,"mdc-switch__ripple"],["mat-ripple","",1,"mat-mdc-slide-toggle-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-switch__icons"],[1,"mdc-label",3,"click","for"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--on"],["d","M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z"],["viewBox","0 0 24 24","aria-hidden","true",1,"mdc-switch__icon","mdc-switch__icon--off"],["d","M20 13H4v-2h16v2z"]],template:function(i,a){if(i&1){let d=P();He(),n(0,"div",1)(1,"button",2,0),S("click",function(){return b(d),C(a._handleClick())}),f(3,"span",3),n(4,"span",4)(5,"span",5)(6,"span",6),f(7,"span",7),o(),n(8,"span",8),f(9,"span",9),o(),p(10,Dr,5,0,"span",10),o()()(),n(11,"label",11),S("click",function(h){return b(d),C(h.stopPropagation())}),Qe(12),o()()}if(i&2){let d=ee(2);m("labelPosition",a.labelPosition),l(),Ge("mdc-switch--selected",a.checked)("mdc-switch--unselected",!a.checked)("mdc-switch--checked",a.checked)("mdc-switch--disabled",a.disabled)("mat-mdc-slide-toggle-disabled-interactive",a.disabledInteractive),m("tabIndex",a.disabled&&!a.disabledInteractive?-1:a.tabIndex)("disabled",a.disabled&&!a.disabledInteractive),ue("id",a.buttonId)("name",a.name)("aria-label",a.ariaLabel)("aria-labelledby",a._getAriaLabelledBy())("aria-describedby",a.ariaDescribedby)("aria-required",a.required||null)("aria-checked",a.checked)("aria-disabled",a.disabled&&a.disabledInteractive?"true":null),l(8),m("matRippleTrigger",d)("matRippleDisabled",a.disableRipple||a.disabled)("matRippleCentered",!0),l(),li(a.hideIcon?-1:10),l(),m("for",a.buttonId),ue("id",a._labelId)}},dependencies:[_i,Di],styles:[`.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative;width:var(--mdc-switch-track-width, 52px)}.mdc-switch.mdc-switch--disabled{cursor:default;pointer-events:none}.mdc-switch.mat-mdc-slide-toggle-disabled-interactive{pointer-events:auto}.mdc-switch__track{overflow:hidden;position:relative;width:100%;height:var(--mdc-switch-track-height, 32px);border-radius:var(--mdc-switch-track-shape, var(--mat-sys-corner-full))}.mdc-switch--disabled.mdc-switch .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity, 0.12)}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";height:100%;left:0;position:absolute;width:100%;border-width:var(--mat-switch-track-outline-width, 2px);border-color:var(--mat-switch-track-outline-color, var(--mat-sys-outline))}.mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track::after{border-width:var(--mat-switch-selected-track-outline-width, 2px);border-color:var(--mat-switch-selected-track-outline-color, transparent)}.mdc-switch--disabled .mdc-switch__track::before,.mdc-switch--disabled .mdc-switch__track::after{border-width:var(--mat-switch-disabled-unselected-track-outline-width, 2px);border-color:var(--mat-switch-disabled-unselected-track-outline-color, var(--mat-sys-on-surface))}@media(forced-colors: active){.mdc-switch__track{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0);background:var(--mdc-switch-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch--selected .mdc-switch__track::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color, var(--mat-sys-surface-variant))}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color, var(--mat-sys-surface-variant))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::before,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::before,.mdc-switch.mdc-switch--disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color, var(--mat-sys-surface-variant))}.mdc-switch__track::after{transform:translateX(-100%);background:var(--mdc-switch-selected-track-color, var(--mat-sys-primary))}[dir=rtl] .mdc-switch__track::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::after{transform:translateX(0)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity, 1);transition:var(--mat-switch-visible-track-transition, opacity 75ms)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity, 0);transition:var(--mat-switch-hidden-track-transition, opacity 75ms)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color, var(--mat-sys-primary))}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color, var(--mat-sys-primary))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__track::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__track::after,.mdc-switch.mdc-switch--disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color, var(--mat-sys-on-surface))}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0);width:calc(100% - var(--mdc-switch-handle-width))}[dir=rtl] .mdc-switch__handle-track{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto;transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1);width:var(--mdc-switch-handle-width);height:var(--mdc-switch-handle-height);border-radius:var(--mdc-switch-handle-shape, var(--mat-sys-corner-full))}[dir=rtl] .mdc-switch__handle{left:auto;right:0}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size, 16px);height:var(--mat-switch-unselected-handle-size, 16px);margin:var(--mat-switch-unselected-handle-horizontal-margin, 0 8px)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin, 0 4px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size, 24px);height:var(--mat-switch-selected-handle-size, 24px);margin:var(--mat-switch-selected-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin, 0 24px)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size, 24px);height:var(--mat-switch-with-icon-handle-size, 24px)}.mat-mdc-slide-toggle .mdc-switch:active:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size, 28px);height:var(--mat-switch-pressed-handle-size, 28px)}.mat-mdc-slide-toggle .mdc-switch--selected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin, 0 22px)}.mat-mdc-slide-toggle .mdc-switch--unselected:active:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin, 0 2px)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity, 1)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity, 0.38)}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:"";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media(forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color, var(--mat-sys-on-primary))}.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color, var(--mat-sys-primary-container))}.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color, var(--mat-sys-primary-container))}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:hover:not(:focus):not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:focus:not(:active) .mdc-switch__handle::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled.mdc-switch--selected:active .mdc-switch__handle::after,.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color, var(--mat-sys-surface))}.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color, var(--mat-sys-outline))}.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color, var(--mat-sys-on-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color, var(--mat-sys-on-surface))}.mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:hover:not(:focus):not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:focus:not(:active) .mdc-switch__shadow,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:active .mdc-switch__shadow,.mdc-switch.mdc-switch--disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1;width:var(--mdc-switch-state-layer-size, 40px);height:var(--mdc-switch-state-layer-size, 40px)}.mdc-switch__ripple::after{content:"";opacity:0}.mdc-switch--disabled .mdc-switch__ripple::after{display:none}.mat-mdc-slide-toggle-disabled-interactive .mdc-switch__ripple::after{display:block}.mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:75ms opacity cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:focus .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:active .mdc-switch__ripple::after,.mat-mdc-slide-toggle-disabled-interactive.mdc-switch--disabled:enabled:hover:not(:focus) .mdc-switch__ripple::after,.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-unselected-pressed-state-layer-color, var(--mat-sys-on-surface));opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background:var(--mdc-switch-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background:var(--mdc-switch-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background:var(--mdc-switch-selected-pressed-state-layer-color, var(--mat-sys-primary));opacity:var(--mdc-switch-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));transition:opacity 75ms linear}.mdc-switch__icons{position:relative;height:100%;width:100%;z-index:1;transform:translateZ(0)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity, 0.38)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity, 0.38)}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size, 16px);height:var(--mdc-switch-unselected-icon-size, 16px);fill:var(--mdc-switch-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color, var(--mat-sys-surface-variant))}.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size, 16px);height:var(--mdc-switch-selected-icon-size, 16px);fill:var(--mdc-switch-selected-icon-color, var(--mat-sys-on-primary-container))}.mdc-switch--selected.mdc-switch--disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color, var(--mat-sys-on-surface))}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle{-webkit-user-select:none;user-select:none;display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-focus-indicator::before{content:""}.mat-mdc-slide-toggle .mat-internal-form-field{color:var(--mat-switch-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-switch-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-switch-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-switch-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-switch-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-switch-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}
`],encapsulation:2,changeDetection:0})}return r})();var Ft=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=rt({type:r});static \u0275inj=et({imports:[$e,ye,ye]})}return r})();var Ot=class r{constructor(t,e,i,a){this.dialogRef=t;this.data=e;this.userService=i;this.snackBar=a}formatRole(t){return t.charAt(0).toUpperCase()+t.slice(1)}formatDate(t){return new Date(t).toLocaleString()}toggleApprovalStatus(t){return x(this,null,function*(){try{if(t)if(yield this.userService.approveDriver(this.data.id))this.data.is_approved=!0,this.snackBar.open("User approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve user");else if(yield this.userService.updateUserStatus(this.data.id,!1))this.data.is_approved=!1,this.snackBar.open("User approval revoked successfully","Close",{duration:3e3});else throw new Error("Failed to revoke user approval")}catch(e){console.error("Error updating user approval status:",e),this.snackBar.open("Failed to update user approval status","Close",{duration:3e3}),this.data.is_approved=!t}})}close(){this.dialogRef.close()}static \u0275fac=function(e){return new(e||r)(y(be),y(lt),y(Z),y($))};static \u0275cmp=O({type:r,selectors:[["app-user-details-dialog"]],decls:38,vars:8,consts:[["mat-dialog-title",""],[1,"user-details"],[1,"detail-row"],[1,"label"],[1,"value"],["color","primary","selected",""],["selected","",3,"color"],["color","primary",3,"change","checked"],["align","end"],["mat-button","",3,"click"]],template:function(e,i){e&1&&(n(0,"h2",0),s(1,"User Details"),o(),n(2,"mat-dialog-content")(3,"div",1)(4,"div",2)(5,"span",3),s(6,"Email:"),o(),n(7,"span",4),s(8),o()(),n(9,"div",2)(10,"span",3),s(11,"Full Name:"),o(),n(12,"span",4),s(13),o()(),n(14,"div",2)(15,"span",3),s(16,"Phone:"),o(),n(17,"span",4),s(18),o()(),n(19,"div",2)(20,"span",3),s(21,"Role:"),o(),n(22,"mat-chip",5),s(23),o()(),n(24,"div",2)(25,"span",3),s(26,"Status:"),o(),n(27,"mat-chip",6),s(28),o(),n(29,"mat-slide-toggle",7),S("change",function(d){return i.toggleApprovalStatus(d.checked)}),o()(),n(30,"div",2)(31,"span",3),s(32,"Registered:"),o(),n(33,"span",4),s(34),o()()()(),n(35,"mat-dialog-actions",8)(36,"button",9),S("click",function(){return i.close()}),s(37,"Close"),o()()),e&2&&(l(8),M(i.data.email),l(5),M(i.data.full_name||"Not provided"),l(5),M(i.data.phone||"Not provided"),l(5),M(i.formatRole(i.data.role)),l(4),m("color",i.data.is_approved?"primary":"warn"),l(),R(" ",i.data.is_approved?"Approved":"Pending Approval"," "),l(),m("checked",i.data.is_approved),l(5),M(i.formatDate(i.data.created_at)))},dependencies:[z,H,ie,Ce,Me,xe,N,V,K,J,Ve,gt,ut,Ft,$e],styles:[".user-details[_ngcontent-%COMP%]{padding:16px}.detail-row[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:16px}.label[_ngcontent-%COMP%]{font-weight:500;min-width:120px;color:#0000008a}.value[_ngcontent-%COMP%]{flex:1}mat-slide-toggle[_ngcontent-%COMP%]{margin-left:8px}"]})};var Er=["cardElement"];function Tr(r,t){r&1&&(n(0,"div",4)(1,"p"),s(2,"Loading Stripe SDK..."),o(),f(3,"mat-spinner",5),o())}function Pr(r,t){r&1&&(n(0,"mat-error"),s(1," Amount is required "),o())}function Ar(r,t){r&1&&(n(0,"mat-error"),s(1," Amount must be at least $1 "),o())}function Fr(r,t){if(r&1&&(n(0,"div",18),s(1),o()),r&2){let e=g(2);l(),M(e.cardError)}}function Or(r,t){if(r&1){let e=P();n(0,"div",6)(1,"div",7)(2,"h3"),s(3,"Process a Payment"),o(),n(4,"p"),s(5,"Use this form to process a one-time payment"),o(),n(6,"form",8),S("ngSubmit",function(){b(e);let a=g();return C(a.processPayment())}),n(7,"div",9)(8,"mat-form-field",10)(9,"mat-label"),s(10,"Amount (USD)"),o(),f(11,"input",11),p(12,Pr,2,0,"mat-error",12)(13,Ar,2,0,"mat-error",12),o()(),n(14,"div",9)(15,"mat-form-field",10)(16,"mat-label"),s(17,"Description"),o(),f(18,"input",13),o()(),n(19,"div",9),f(20,"div",14,0),p(22,Fr,2,1,"div",15),o(),n(23,"div",16)(24,"button",17)(25,"mat-icon"),s(26,"payment"),o(),s(27),o()()()()()}if(r&2){let e,i,a=g();l(6),m("formGroup",a.paymentForm),l(6),m("ngIf",(e=a.paymentForm.get("amount"))==null?null:e.hasError("required")),l(),m("ngIf",(i=a.paymentForm.get("amount"))==null?null:i.hasError("min")),l(9),m("ngIf",a.cardError),l(2),m("disabled",a.paymentForm.invalid||a.processing),l(3),R(" ",a.processing?"Processing...":"Process Payment"," ")}}var Bt=class r{constructor(t,e,i,a,d){this.formBuilder=t;this.snackBar=e;this.paymentService=i;this.rideService=a;this.authService=d;this.paymentForm=this.formBuilder.group({amount:[10,[F.required,F.min(1)]],description:["",F.required]})}cardElement;paymentForm;stripe;card;sdkLoaded=!1;processing=!1;cardError="";paymentResult=null;recentTransactions=[];ngOnInit(){return x(this,null,function*(){yield this.loadStripe()})}ngAfterViewInit(){this.sdkLoaded&&this.initializeCard()}loadStripe(){return x(this,null,function*(){try{let t=yield qi(qt.stripePublishableKey);this.stripe=t,this.sdkLoaded=!0,setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error loading Stripe:",t),this.snackBar.open("Error loading Stripe. Please check your API keys.","Close",{duration:5e3})}})}loadStripeScript(){if(window.Stripe){this.initializeStripe();return}let t=document.createElement("script");t.src="https://js.stripe.com/v3/",t.async=!0,t.onload=()=>{this.initializeStripe()},document.body.appendChild(t)}initializeStripe(){if(!window.Stripe){this.snackBar.open("Stripe SDK not available","Close",{duration:3e3});return}try{this.stripe=window.Stripe(qt.stripePublishableKey),setTimeout(()=>this.initializeCard(),100)}catch(t){console.error("Error initializing Stripe:",t),this.snackBar.open("Error initializing Stripe payments. Check your credentials.","Close",{duration:5e3})}}initializeCard(){if(!this.cardElement||!this.cardElement.nativeElement||!this.stripe){setTimeout(()=>this.initializeCard(),100);return}try{let t=this.stripe.elements();this.card=t.create("card",{style:{base:{iconColor:"#666EE8",color:"#31325F",fontWeight:400,fontFamily:'"Helvetica Neue", Helvetica, sans-serif',fontSize:"16px","::placeholder":{color:"#CFD7E0"}}}}),this.card.mount(this.cardElement.nativeElement),this.card.on("change",e=>{this.cardError=e.error?e.error.message:""}),this.sdkLoaded=!0}catch(t){console.error("Error initializing Stripe card:",t),this.snackBar.open("Error initializing Stripe card form","Close",{duration:5e3})}}loadRecentTransactions(){return x(this,null,function*(){try{let{data:t,error:e}=yield this.authService.supabase.functions.invoke("stripe",{body:{action:"listPaymentIntents",limit:10}});if(e){console.error("Error loading recent transactions:",e),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3});return}t&&t.paymentIntents&&(this.recentTransactions=t.paymentIntents.map(i=>({id:i.id,date:new Date(i.created*1e3),amount:i.amount/100,description:i.description||"No description",status:this.formatPaymentStatus(i.status)})))}catch(t){console.error("Error loading recent transactions:",t),this.snackBar.open("Error loading recent transactions","Close",{duration:3e3})}})}formatPaymentStatus(t){return t.charAt(0).toUpperCase()+t.slice(1)}processPayment(){return x(this,null,function*(){if(this.paymentForm.invalid||!this.card||!this.stripe)return;this.processing=!0,this.paymentResult=null;let t=this.paymentForm.get("amount")?.value,e=this.paymentForm.get("description")?.value;try{let{paymentMethod:i,error:a}=yield this.stripe.createPaymentMethod({type:"card",card:this.card});if(a)throw a;let d={amount:t*100,currency:"usd",description:e,payment_method:i.id};console.log(d);let{data:c,error:h}=yield this.authService.supabase.functions.invoke("stripe",{body:d});if(h)throw console.error("Error creating payment intent:",h),new Error(`Failed to create payment intent: ${h.message}`);if(console.log("Payment intent created:",c),!c||!c.client_secret)throw new Error("No client secret returned from payment intent creation");let v=c.client_secret,{error:D,paymentIntent:A}=yield this.stripe.confirmCardPayment(v,{payment_method:i.id});if(D)throw D;this.paymentResult={success:!0,paymentIntent:A},this.snackBar.open("Payment processed successfully!","Close",{duration:3e3}),this.paymentForm.reset({amount:10}),this.card.clear()}catch(i){console.error("Error processing payment:",i),this.paymentResult={success:!1,error:{message:i.message}},this.snackBar.open(`Payment error: ${i.message}`,"Close",{duration:5e3})}finally{this.processing=!1}})}static \u0275fac=function(e){return new(e||r)(y(Ee),y($),y(ze),y(le),y(Ai))};static \u0275cmp=O({type:r,selectors:[["app-stripe-payment"]],viewQuery:function(e,i){if(e&1&&Y(Er,5),e&2){let a;L(a=q())&&(i.cardElement=a.first)}},decls:10,vars:2,consts:[["cardElement",""],[1,"container"],["class","sdk-status",4,"ngIf"],["class","payment-tabs",4,"ngIf"],[1,"sdk-status"],["diameter","30"],[1,"payment-tabs"],[1,"payment-section"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline"],["matInput","","type","number","formControlName","amount","min","1","step","0.01"],[4,"ngIf"],["matInput","","formControlName","description","placeholder","Payment description"],[1,"card-element"],["class","card-errors",4,"ngIf"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"card-errors"]],template:function(e,i){e&1&&(n(0,"div",1)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),s(4,"Stripe Payment Processing"),o(),n(5,"mat-card-subtitle"),s(6,"Process payments securely with Stripe"),o()(),n(7,"mat-card-content"),p(8,Tr,4,0,"div",2)(9,Or,28,6,"div",3),o()()()),e&2&&(l(8),m("ngIf",!i.sdkLoaded),l(),m("ngIf",i.sdkLoaded))},dependencies:[z,j,H,Re,re,mt,G,De,xi,ne,ke,Ie,K,Fe,Be,Ue,Fi,Oe,X,W,Q,Pe,de,se,N,V,ae,te,me,ce,Ve,J,oe,Ne],styles:[".container[_ngcontent-%COMP%]{padding:20px}.form-row[_ngcontent-%COMP%]{margin-bottom:20px}mat-form-field[_ngcontent-%COMP%]{width:100%}.card-element[_ngcontent-%COMP%]{border:1px solid #ccc;padding:10px;border-radius:4px;height:40px;background-color:#fff}.card-errors[_ngcontent-%COMP%]{color:#f44336;margin-top:8px;font-size:14px}.form-actions[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.divider[_ngcontent-%COMP%]{margin:30px 0}.payment-result[_ngcontent-%COMP%]{margin-top:20px}.payment-result[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:10px;border-radius:4px;overflow-x:auto}.payment-section[_ngcontent-%COMP%]{margin-bottom:30px}.payment-tabs[_ngcontent-%COMP%]{margin-top:20px}.recent-transactions[_ngcontent-%COMP%]{margin-top:30px}table[_ngcontent-%COMP%]{width:100%}.status-succeeded[_ngcontent-%COMP%], .status-completed[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.status-pending[_ngcontent-%COMP%]{color:#ff9800;font-weight:500}.status-failed[_ngcontent-%COMP%], .status-refunded[_ngcontent-%COMP%]{color:#f44336;font-weight:500}.result-card[_ngcontent-%COMP%]{display:flex;align-items:center;padding:15px;border-radius:4px;margin-top:10px}.result-card.success[_ngcontent-%COMP%]{background-color:#4caf501a}.result-card.error[_ngcontent-%COMP%]{background-color:#f443361a}.result-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:15px;font-size:24px;height:24px;width:24px}.result-card.success[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50}.result-card.error[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f44336}.result-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 5px}.result-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.sdk-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;padding:40px}"]})};function zr(r,t){r&1&&(n(0,"mat-error"),s(1," Name is required "),o())}function Vr(r,t){r&1&&(n(0,"mat-error"),s(1," Base fare is required "),o())}function Nr(r,t){r&1&&(n(0,"mat-error"),s(1," Base fare must be positive "),o())}function $r(r,t){r&1&&(n(0,"mat-error"),s(1," Distance rate is required "),o())}function Lr(r,t){r&1&&(n(0,"mat-error"),s(1," Distance rate must be positive "),o())}function qr(r,t){r&1&&(n(0,"mat-error"),s(1," Time rate is required "),o())}function jr(r,t){r&1&&(n(0,"mat-error"),s(1," Time rate must be positive "),o())}function Gr(r,t){r&1&&(n(0,"div",20),f(1,"mat-spinner",21),n(2,"p"),s(3,"Loading pricing configurations..."),o()())}function Hr(r,t){r&1&&(n(0,"th",35),s(1,"Name"),o())}function Qr(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;l(),M(e.name)}}function Wr(r,t){r&1&&(n(0,"th",35),s(1,"Base Fare"),o())}function Xr(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;l(),R("$",e.base_fare.toFixed(2),"")}}function Jr(r,t){r&1&&(n(0,"th",35),s(1,"Distance Rate"),o())}function Kr(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;l(),R("$",e.distance_rate.toFixed(2),"/mile")}}function Zr(r,t){r&1&&(n(0,"th",35),s(1,"Time Rate"),o())}function Yr(r,t){if(r&1&&(n(0,"td",36),s(1),o()),r&2){let e=t.$implicit;l(),R("$",e.time_rate.toFixed(2),"/min")}}function en(r,t){r&1&&(n(0,"th",35),s(1,"Status"),o())}function tn(r,t){if(r&1&&(n(0,"td",36)(1,"span",37),s(2),o()()),r&2){let e=t.$implicit;l(),Ge("active",e.is_active),l(),R(" ",e.is_active?"Active":"Inactive"," ")}}function rn(r,t){r&1&&(n(0,"th",35),s(1,"Actions"),o())}function nn(r,t){if(r&1){let e=P();n(0,"td",36)(1,"button",38),S("click",function(){let a=b(e).$implicit,d=g(2);return C(d.editPricing(a))}),n(2,"mat-icon"),s(3,"edit"),o()(),n(4,"button",39),S("click",function(){let a=b(e).$implicit,d=g(2);return C(d.toggleActive(a))}),n(5,"mat-icon"),s(6),o()(),n(7,"button",40),S("click",function(){let a=b(e).$implicit,d=g(2);return C(d.deletePricing(a.id))}),n(8,"mat-icon"),s(9,"delete"),o()()()}if(r&2){let e=t.$implicit;l(4),m("matTooltip",e.is_active?"Deactivate":"Activate"),l(2),M(e.is_active?"toggle_on":"toggle_off"),l(),m("disabled",e.is_active)}}function an(r,t){r&1&&f(0,"tr",41)}function on(r,t){r&1&&f(0,"tr",42)}function sn(r,t){r&1&&(n(0,"div",43)(1,"p"),s(2,"No pricing configurations found. Create one to get started."),o()())}function dn(r,t){if(r&1&&(n(0,"div",22)(1,"table",23),E(2,24),p(3,Hr,2,0,"th",25)(4,Qr,2,1,"td",26),T(),E(5,27),p(6,Wr,2,0,"th",25)(7,Xr,2,1,"td",26),T(),E(8,28),p(9,Jr,2,0,"th",25)(10,Kr,2,1,"td",26),T(),E(11,29),p(12,Zr,2,0,"th",25)(13,Yr,2,1,"td",26),T(),E(14,30),p(15,en,2,0,"th",25)(16,tn,3,3,"td",26),T(),E(17,31),p(18,rn,2,0,"th",25)(19,nn,10,3,"td",26),T(),p(20,an,1,0,"tr",32)(21,on,1,0,"tr",33),o(),p(22,sn,3,0,"div",34),o()),r&2){let e=g();l(),m("dataSource",e.pricingConfigurations),l(19),m("matHeaderRowDef",e.displayedColumns),l(),m("matRowDefColumns",e.displayedColumns),l(),m("ngIf",e.pricingConfigurations.length===0)}}var Ut=class r{constructor(t,e,i){this.fb=t;this.ridePricingService=e;this.snackBar=i;this.pricingForm=this.fb.group({name:["",[F.required]],base_fare:[5,[F.required,F.min(0)]],distance_rate:[1.5,[F.required,F.min(0)]],time_rate:[.25,[F.required,F.min(0)]],is_active:[!0]})}pricingForm;pricingConfigurations=[];displayedColumns=["name","base_fare","distance_rate","time_rate","is_active","actions"];loading=!1;activePricing=null;editMode=!1;editingId=null;ngOnInit(){this.loadPricingConfigurations()}loadPricingConfigurations(){return x(this,null,function*(){this.loading=!0;try{this.pricingConfigurations=yield this.ridePricingService.getAllPricing(),this.activePricing=yield this.ridePricingService.loadActivePricing()}catch(t){console.error("Error loading pricing configurations:",t),this.snackBar.open("Failed to load pricing configurations","Close",{duration:3e3})}finally{this.loading=!1}})}onSubmit(){return x(this,null,function*(){if(!this.pricingForm.invalid){this.loading=!0;try{let t=this.pricingForm.value;this.editMode&&this.editingId?(yield this.ridePricingService.updatePricing(this.editingId,t),this.snackBar.open("Pricing configuration updated successfully","Close",{duration:3e3})):(yield this.ridePricingService.createPricing(t),this.snackBar.open("Pricing configuration created successfully","Close",{duration:3e3})),this.resetForm(),yield this.loadPricingConfigurations()}catch(t){console.error("Error saving pricing configuration:",t),this.snackBar.open("Failed to save pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}editPricing(t){this.editMode=!0,this.editingId=t.id,this.pricingForm.patchValue({name:t.name,base_fare:t.base_fare,distance_rate:t.distance_rate,time_rate:t.time_rate,is_active:t.is_active})}resetForm(){this.editMode=!1,this.editingId=null,this.pricingForm.reset({name:"",base_fare:5,distance_rate:1.5,time_rate:.25,is_active:!0})}toggleActive(t){return x(this,null,function*(){this.loading=!0;try{yield this.ridePricingService.setActiveStatus(t.id,!t.is_active),yield this.loadPricingConfigurations(),this.snackBar.open(`Pricing configuration ${t.is_active?"deactivated":"activated"} successfully`,"Close",{duration:3e3})}catch(e){console.error("Error toggling active status:",e),this.snackBar.open("Failed to update pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}})}deletePricing(t){return x(this,null,function*(){if(confirm("Are you sure you want to delete this pricing configuration?")){this.loading=!0;try{yield this.ridePricingService.deletePricing(t),yield this.loadPricingConfigurations(),this.snackBar.open("Pricing configuration deleted successfully","Close",{duration:3e3})}catch(e){console.error("Error deleting pricing configuration:",e),this.snackBar.open("Failed to delete pricing configuration","Close",{duration:3e3})}finally{this.loading=!1}}})}calculateSampleFare(){let t=this.pricingForm.get("base_fare")?.value||0,e=this.pricingForm.get("distance_rate")?.value||0,i=this.pricingForm.get("time_rate")?.value||0;return+(t+10*e+20*i).toFixed(2)}static \u0275fac=function(e){return new(e||r)(y(Ee),y($i),y($))};static \u0275cmp=O({type:r,selectors:[["app-ride-pricing"]],decls:57,vars:15,consts:[[1,"pricing-container"],[1,"form-card"],[3,"ngSubmit","formGroup"],[1,"form-row"],["appearance","outline",1,"full-width"],["matInput","","formControlName","name","placeholder","e.g., Standard, Premium, etc."],[4,"ngIf"],["appearance","outline"],["matInput","","type","number","step","0.01","formControlName","base_fare"],["matInput","","type","number","step","0.01","formControlName","distance_rate"],["matInput","","type","number","step","0.01","formControlName","time_rate"],[1,"active-toggle"],["formControlName","is_active","color","primary"],[1,"sample-calculation"],[1,"form-actions"],["mat-button","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],[1,"table-card"],["class","loading-container",4,"ngIf"],["class","table-container",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"table-container"],["mat-table","",1,"mat-elevation-z2",3,"dataSource"],["matColumnDef","name"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","base_fare"],["matColumnDef","distance_rate"],["matColumnDef","time_rate"],["matColumnDef","is_active"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["class","no-data",4,"ngIf"],["mat-header-cell",""],["mat-cell",""],[1,"status-badge"],["mat-icon-button","","color","primary","matTooltip","Edit",3,"click"],["mat-icon-button","","color","accent",3,"click","matTooltip"],["mat-icon-button","","color","warn","matTooltip","Delete",3,"click","disabled"],["mat-header-row",""],["mat-row",""],[1,"no-data"]],template:function(e,i){if(e&1&&(n(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),s(4),o()(),n(5,"mat-card-content")(6,"form",2),S("ngSubmit",function(){return i.onSubmit()}),n(7,"div",3)(8,"mat-form-field",4)(9,"mat-label"),s(10,"Configuration Name"),o(),f(11,"input",5),p(12,zr,2,0,"mat-error",6),o()(),n(13,"div",3)(14,"mat-form-field",7)(15,"mat-label"),s(16,"Base Fare ($)"),o(),f(17,"input",8),p(18,Vr,2,0,"mat-error",6)(19,Nr,2,0,"mat-error",6),o(),n(20,"mat-form-field",7)(21,"mat-label"),s(22,"Distance Rate ($ per mile)"),o(),f(23,"input",9),p(24,$r,2,0,"mat-error",6)(25,Lr,2,0,"mat-error",6),o(),n(26,"mat-form-field",7)(27,"mat-label"),s(28,"Time Rate ($ per minute)"),o(),f(29,"input",10),p(30,qr,2,0,"mat-error",6)(31,jr,2,0,"mat-error",6),o()(),n(32,"div",3)(33,"div",11)(34,"mat-slide-toggle",12),s(35," Set as Active Configuration "),o()()(),n(36,"div",13),f(37,"mat-divider"),n(38,"h3"),s(39,"Sample Fare Calculation"),o(),n(40,"p"),s(41,"For a 10-mile, 20-minute ride: "),n(42,"strong"),s(43),o()(),f(44,"mat-divider"),o(),n(45,"div",14)(46,"button",15),S("click",function(){return i.resetForm()}),s(47),o(),n(48,"button",16),s(49),o()()()()(),n(50,"mat-card",17)(51,"mat-card-header")(52,"mat-card-title"),s(53,"Pricing Configurations"),o()(),n(54,"mat-card-content"),p(55,Gr,4,0,"div",18)(56,dn,23,4,"div",19),o()()()),e&2){let a,d,c,h,v,D,A;l(4),M(i.editMode?"Edit Pricing Configuration":"Create New Pricing Configuration"),l(2),m("formGroup",i.pricingForm),l(6),m("ngIf",(a=i.pricingForm.get("name"))==null?null:a.hasError("required")),l(6),m("ngIf",(d=i.pricingForm.get("base_fare"))==null?null:d.hasError("required")),l(),m("ngIf",(c=i.pricingForm.get("base_fare"))==null?null:c.hasError("min")),l(5),m("ngIf",(h=i.pricingForm.get("distance_rate"))==null?null:h.hasError("required")),l(),m("ngIf",(v=i.pricingForm.get("distance_rate"))==null?null:v.hasError("min")),l(5),m("ngIf",(D=i.pricingForm.get("time_rate"))==null?null:D.hasError("required")),l(),m("ngIf",(A=i.pricingForm.get("time_rate"))==null?null:A.hasError("min")),l(12),R("$",i.calculateSampleFare(),""),l(4),R(" ",i.editMode?"Cancel":"Reset"," "),l(),m("disabled",i.pricingForm.invalid||i.loading),l(),R(" ",i.editMode?"Update":"Create"," "),l(6),m("ngIf",i.loading),l(),m("ngIf",!i.loading)}},dependencies:[z,j,H,Re,re,mt,G,De,ne,ke,Ie,K,Fe,Be,Ue,Oe,X,W,Q,Pe,de,se,N,V,dt,J,oe,Ne,ht,_t,yt,vt,ft,bt,wt,St,Ct,xt,te,me,ce,Ft,$e,Dt,Mt,Ve,Li],styles:[".pricing-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;max-width:1200px;margin:0 auto}.form-card[_ngcontent-%COMP%], .table-card[_ngcontent-%COMP%]{width:100%}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:16px;align-items:center}.full-width[_ngcontent-%COMP%]{width:100%}mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:150px}.active-toggle[_ngcontent-%COMP%]{margin:16px 0}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:8px;margin-top:16px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.no-data[_ngcontent-%COMP%]{text-align:center;padding:20px;color:#0000008a}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;background-color:#e0e0e0;color:#757575}.status-badge.active[_ngcontent-%COMP%]{background-color:#c8e6c9;color:#2e7d32}.sample-calculation[_ngcontent-%COMP%]{margin:20px 0;padding:10px 0}.sample-calculation[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:10px 0;font-size:16px;font-weight:500}.sample-calculation[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:10px 0}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column}mat-form-field[_ngcontent-%COMP%]{width:100%}}"]})};function ln(r,t){if(r&1&&(n(0,"mat-option",21),s(1),o()),r&2){let e=t.$implicit;m("value",e.id),l(),R(" ",e.full_name||e.email," ")}}function cn(r,t){r&1&&(n(0,"mat-error"),s(1," Rider is required "),o())}function mn(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup location is required "),o())}function pn(r,t){r&1&&(n(0,"mat-error"),s(1," Dropoff location is required "),o())}function un(r,t){if(r&1&&(n(0,"p"),s(1),o()),r&2){let e=g(3);l(),R("Distance: ",e.estimatedDistance," miles")}}function gn(r,t){if(r&1&&(n(0,"p"),s(1),o()),r&2){let e=g(3);l(),R("Duration: ",e.estimatedDuration," minutes")}}function hn(r,t){if(r&1&&(n(0,"div",24)(1,"p"),s(2,"Estimated fare: "),n(3,"strong"),s(4),o()(),p(5,un,2,1,"p",7)(6,gn,2,1,"p",7),o()),r&2){let e=g(2);l(4),M(e.estimatedFare?"$"+e.estimatedFare.toFixed(2):""),l(),m("ngIf",e.estimatedDistance),l(),m("ngIf",e.estimatedDuration)}}function fn(r,t){if(r&1&&(n(0,"div"),f(1,"app-map-display",22),p(2,hn,7,3,"div",23),o()),r&2){let e,i,a=g();l(),m("origin",(e=a.rideForm.get("pickup_location"))==null?null:e.value)("destination",(i=a.rideForm.get("dropoff_location"))==null?null:i.value),l(),m("ngIf",a.estimatedFare)}}function _n(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup date is required "),o())}function vn(r,t){r&1&&(n(0,"mat-error"),s(1," Pickup time is required "),o())}var zt=class r{constructor(t,e,i,a,d,c,h){this.formBuilder=t;this.dialogRef=e;this.rideService=i;this.userService=a;this.locationService=d;this.paymentService=c;this.snackBar=h;this.rideForm=this.formBuilder.group({rider_id:["",F.required],pickup_location:["",F.required],dropoff_location:["",F.required],pickup_date:[new Date,F.required],pickup_time:["12:00 PM",F.required]})}rideForm;riders=[];loading=!1;showMap=!1;estimatedFare=null;estimatedDistance=null;estimatedDuration=null;locationCoordinates={};ngOnInit(){return x(this,null,function*(){try{let t=yield this.userService.getUsersByRole("rider");this.riders=t}catch(t){console.error("Error loading riders:",t),this.snackBar.open("Failed to load riders","Close",{duration:3e3})}this.rideForm.get("pickup_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()}),this.rideForm.get("dropoff_location")?.valueChanges.subscribe(()=>{this.updateRouteEstimates()})})}updateRouteEstimates(){return x(this,null,function*(){let t=this.rideForm.get("pickup_location")?.value,e=this.rideForm.get("dropoff_location")?.value;if(t&&e){this.showMap=!0;try{let{fare:i,routeInfo:a}=yield this.paymentService.estimateFare(t,e);this.estimatedFare=i,this.estimatedDistance=a.distance,this.estimatedDuration=a.duration}catch(i){console.error("Error calculating route:",i)}}else this.showMap=!1,this.estimatedFare=null,this.estimatedDistance=null,this.estimatedDuration=null})}onSubmit(){return x(this,null,function*(){if(!this.rideForm.invalid){this.loading=!0;try{this.locationCoordinates.pickup||(this.locationCoordinates.pickup=yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location)),this.locationCoordinates.dropoff||(this.locationCoordinates.dropoff=yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location));let t=yield this.locationService.calculateRoute(this.locationCoordinates.pickup,this.locationCoordinates.dropoff),e=this.rideForm.value.pickup_date,i=this.rideForm.value.pickup_time,a=new Date(e),d=i.match(/(\d+):(\d+)\s?(AM|PM)?/i);if(d){let v=parseInt(d[1],10),D=parseInt(d[2],10),A=d[3]?d[3].toUpperCase():null;A==="PM"&&v<12?v+=12:A==="AM"&&v===12&&(v=0),a.setHours(v,D,0,0)}let c=qe(Le({},this.rideForm.value),{status:"requested",pickup_time:a.toISOString(),pickup_latitude:this.locationCoordinates.pickup?.latitude,pickup_longitude:this.locationCoordinates.pickup?.longitude,dropoff_latitude:this.locationCoordinates.dropoff?.latitude,dropoff_longitude:this.locationCoordinates.dropoff?.longitude,distance_miles:t.distance,duration_minutes:t.duration,fare:this.estimatedFare||(yield this.paymentService.estimateFare(this.rideForm.value.pickup_location,this.rideForm.value.dropoff_location)).fare}),h=yield this.rideService.createRide(c);this.snackBar.open("Ride created successfully!","Close",{duration:3e3}),this.dialogRef.close(h)}catch(t){console.error("Error creating ride:",t),this.snackBar.open(t.message||"Failed to create ride","Close",{duration:3e3})}finally{this.loading=!1}}})}static \u0275fac=function(e){return new(e||r)(y(Ee),y(be),y(le),y(Z),y(Vi),y(ze),y($))};static \u0275cmp=O({type:r,selectors:[["app-admin-ride-create-dialog"]],decls:50,vars:14,consts:[["picker",""],["timepicker",""],["mat-dialog-title",""],[3,"formGroup"],["appearance","outline",1,"full-width"],["formControlName","rider_id","required",""],[3,"value",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"location-fields"],["matInput","","formControlName","pickup_location","placeholder","Enter pickup location"],["matInput","","formControlName","dropoff_location","placeholder","Enter dropoff location"],[1,"date-time-fields"],["appearance","outline"],["matInput","","formControlName","pickup_date",3,"matDatepicker"],["matSuffix","",3,"for"],["matInput","","formControlName","pickup_time",3,"ngxMatTimepicker"],["ngxMatTimepickerToggleIcon",""],["matInput","","formControlName","notes","placeholder","Any special requirements?"],["align","end"],["mat-button","","mat-dialog-close",""],["mat-raised-button","","color","primary",3,"click","disabled"],[3,"value"],[3,"origin","destination"],["class","fare-estimate",4,"ngIf"],[1,"fare-estimate"]],template:function(e,i){if(e&1){let a=P();n(0,"h2",2),s(1,"Create Ride for User"),o(),n(2,"mat-dialog-content")(3,"form",3)(4,"mat-form-field",4)(5,"mat-label"),s(6,"Select Rider"),o(),n(7,"mat-select",5),p(8,ln,2,2,"mat-option",6),o(),p(9,cn,2,0,"mat-error",7),o(),n(10,"div",8)(11,"mat-form-field",4)(12,"mat-label"),s(13,"Pickup Location"),o(),f(14,"input",9),p(15,mn,2,0,"mat-error",7),o(),n(16,"mat-form-field",4)(17,"mat-label"),s(18,"Dropoff Location"),o(),f(19,"input",10),p(20,pn,2,0,"mat-error",7),o()(),p(21,fn,3,3,"div",7),n(22,"div",11)(23,"mat-form-field",12)(24,"mat-label"),s(25,"Pickup Date"),o(),f(26,"input",13)(27,"mat-datepicker-toggle",14)(28,"mat-datepicker",null,0),p(30,_n,2,0,"mat-error",7),o(),n(31,"mat-form-field",12)(32,"mat-label"),s(33,"Pickup Time"),o(),f(34,"input",15),n(35,"ngx-mat-timepicker-toggle",14)(36,"mat-icon",16),s(37,"keyboard_arrow_down"),o()(),f(38,"ngx-mat-timepicker",null,1),p(40,vn,2,0,"mat-error",7),o()(),n(41,"mat-form-field",4)(42,"mat-label"),s(43,"Special Notes"),o(),f(44,"textarea",17),o()()(),n(45,"mat-dialog-actions",18)(46,"button",19),s(47,"Cancel"),o(),n(48,"button",20),S("click",function(){return b(a),C(i.onSubmit())}),s(49),o()()}if(e&2){let a,d,c,h,v,D,A=ee(29),B=ee(39);l(3),m("formGroup",i.rideForm),l(5),m("ngForOf",i.riders),l(),m("ngIf",(a=i.rideForm.get("rider_id"))==null||a.errors==null?null:a.errors.required),l(6),m("ngIf",(d=i.rideForm.get("pickup_location"))==null||d.errors==null?null:d.errors.required),l(5),m("ngIf",(c=i.rideForm.get("dropoff_location"))==null||c.errors==null?null:c.errors.required),l(),m("ngIf",i.showMap&&((h=i.rideForm.get("pickup_location"))==null?null:h.value)&&((h=i.rideForm.get("dropoff_location"))==null?null:h.value)),l(5),m("matDatepicker",A),l(),m("for",A),l(3),m("ngIf",(v=i.rideForm.get("pickup_date"))==null||v.errors==null?null:v.errors.required),l(4),m("ngxMatTimepicker",B),l(),m("for",B),l(5),m("ngIf",(D=i.rideForm.get("pickup_time"))==null||D.errors==null?null:D.errors.required),l(8),m("disabled",i.rideForm.invalid||i.loading),l(),R(" ",i.loading?"Creating...":"Create Ride"," ")}},dependencies:[z,ot,j,ne,Re,re,G,De,Mi,ke,Ie,ie,yi,Ce,Me,xe,K,X,W,Q,Pe,pt,de,se,N,V,zi,Oi,Bi,Ui,Ri,J,oe,ae,Ae,Te,te,Pi,ki,Ei,Ti,Ii,Ni],styles:[".full-width[_ngcontent-%COMP%]{width:100%}form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;min-width:500px}.location-fields[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.date-time-fields[_ngcontent-%COMP%]{display:flex;gap:16px}textarea[_ngcontent-%COMP%]{min-height:80px}.fare-estimate[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:4px;margin-top:16px;margin-bottom:16px}.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}"]})};var Vt=class r{constructor(t,e,i){this.userService=t;this.rideService=e;this.paymentService=i}statisticsSubject=new ti(null);statistics$=this.statisticsSubject.asObservable();generateSystemStatistics(){return x(this,null,function*(){let t=yield this.userService.getAllUsers(),e=t.filter(k=>k.role==="rider"),i=t.filter(k=>k.role==="driver"),a=t.filter(k=>k.role==="admin"),d=yield this.rideService.getAllRides(),c=d.filter(k=>k.status==="completed"),h=d.filter(k=>k.status==="in-progress"),v=d.filter(k=>k.status==="requested"),D=d.filter(k=>k.status==="canceled"),A=[{id:"1",type:"user_registered",timestamp:new Date().toISOString(),details:{userId:"user1",role:"rider"}},{id:"2",type:"ride_requested",timestamp:new Date(Date.now()-36e5).toISOString(),details:{rideId:"ride1",riderId:"rider1"}},{id:"3",type:"ride_completed",timestamp:new Date(Date.now()-72e5).toISOString(),details:{rideId:"ride2",riderId:"rider2",driverId:"driver1"}},{id:"4",type:"driver_approved",timestamp:new Date(Date.now()-108e5).toISOString(),details:{driverId:"driver2"}}],B={totalUsers:{all:t.length,riders:e.length,drivers:i.length,admins:a.length},rides:{total:d.length,completed:c.length,inProgress:h.length,requested:v.length,canceled:D.length},recentActivity:A};return this.statisticsSubject.next(B),B})}generateRideReport(t){return x(this,null,function*(){let{dateRange:e}=t,a=(yield this.rideService.getAllRides()).filter(u=>{let _=new Date(u.created_at);return _>=e.startDate&&_<=e.endDate}),d=a.filter(u=>u.status==="requested").length,c=a.filter(u=>u.status==="assigned").length,h=a.filter(u=>u.status==="in-progress").length,v=a.filter(u=>u.status==="completed").length,D=a.filter(u=>u.status==="canceled").length,A=this.groupRidesByDay(a),B=a.filter(u=>u.status==="completed"&&u.duration_minutes&&u.distance_miles),k=B.length>0?B.reduce((u,_)=>u+(_.duration_minutes||0),0)/B.length:0,we=B.length>0?B.reduce((u,_)=>u+(_.distance_miles||0),0)/B.length:0;return{dateRange:e,ridesByStatus:{requested:d,assigned:c,inProgress:h,completed:v,canceled:D},ridesByDay:A,averageDuration:k,averageDistance:we}})}generateRevenueReport(t){return x(this,null,function*(){let{dateRange:e}=t,i=yield this.rideService.getAllRides(),a=yield this.userService.getAllUsers(),d=i.filter(u=>{let _=new Date(u.created_at);return _>=e.startDate&&_<=e.endDate}),c=d.reduce((u,_)=>u+(_.amount||_.fare||0),0),h=this.groupRevenueByDay(d),v=d.filter(u=>u.payment_status==="pending").reduce((u,_)=>u+(_.amount||_.fare||0),0),D=d.filter(u=>u.payment_status==="paid").reduce((u,_)=>u+(_.amount||_.fare||0),0),A=d.filter(u=>u.payment_status==="failed").reduce((u,_)=>u+(_.amount||_.fare||0),0),B=d.filter(u=>u.payment_status==="refunded").reduce((u,_)=>u+(_.amount||_.fare||0),0),k=new Map;d.filter(u=>u.driver_id&&(u.payment_status==="paid"||u.status==="completed")).forEach(u=>{let _=u.driver_id,Se=u.amount||u.fare||0;if(k.has(_)){let w=k.get(_);k.set(_,{revenue:w.revenue+Se,rideCount:w.rideCount+1})}else k.set(_,{revenue:Se,rideCount:1})});let we=Array.from(k.entries()).map(([u,{revenue:_,rideCount:Se}])=>{let w=a.find(I=>I.id===u);return{driverId:u,driverName:w?.full_name||"Unknown Driver",revenue:_,rideCount:Se}}).sort((u,_)=>_.revenue-u.revenue).slice(0,5);return{dateRange:e,totalRevenue:c,revenueByDay:h,revenueByStatus:{pending:v,paid:D,failed:A,refunded:B},topDriversByRevenue:we}})}generateUserActivityReport(t){return x(this,null,function*(){let{dateRange:e,userRole:i}=t,a=yield this.userService.getAllUsers(),d=yield this.rideService.getAllRides(),c=i?a.filter(w=>w.role===i):a,h=c.filter(w=>{let I=new Date(w.created_at);return I>=e.startDate&&I<=e.endDate}),v=h.filter(w=>w.role==="rider").length,D=h.filter(w=>w.role==="driver").length,A=new Set(d.filter(w=>{let I=new Date(w.created_at);return I>=e.startDate&&I<=e.endDate}).map(w=>w.rider_id)),B=new Set(d.filter(w=>{let I=new Date(w.created_at);return w.driver_id&&I>=e.startDate&&I<=e.endDate}).map(w=>w.driver_id)),k=c.filter(w=>w.role==="rider"&&A.has(w.id)).length,we=c.filter(w=>w.role==="driver"&&B.has(w.id)).length,u=this.groupUsersByDay(h,d,e),_=new Map;d.filter(w=>{let I=new Date(w.created_at);return I>=e.startDate&&I<=e.endDate}).forEach(w=>{let I=w.rider_id,Ze=w.amount||w.fare||0;if(_.has(I)){let Ye=_.get(I);_.set(I,{rideCount:Ye.rideCount+1,totalSpent:Ye.totalSpent+Ze})}else _.set(I,{rideCount:1,totalSpent:Ze})});let Se=Array.from(_.entries()).map(([w,{rideCount:I,totalSpent:Ze}])=>{let Ye=a.find(ur=>ur.id===w);return{riderId:w,riderName:Ye?.full_name||"Unknown Rider",rideCount:I,totalSpent:Ze}}).sort((w,I)=>I.rideCount-w.rideCount).slice(0,5);return{dateRange:e,newUsers:{total:h.length,riders:v,drivers:D},activeUsers:{total:k+we,riders:k,drivers:we},usersByDay:u,topRiders:Se}})}groupRidesByDay(t){let e=new Map;return t.forEach(i=>{let d=new Date(i.created_at).toISOString().split("T")[0];e.has(d)?e.set(d,e.get(d)+1):e.set(d,1)}),Array.from(e.entries()).map(([i,a])=>({date:i,count:a})).sort((i,a)=>i.date.localeCompare(a.date))}groupRevenueByDay(t){let e=new Map;return t.forEach(i=>{let d=new Date(i.created_at).toISOString().split("T")[0],c=i.amount||i.fare||0;e.has(d)?e.set(d,e.get(d)+c):e.set(d,c)}),Array.from(e.entries()).map(([i,a])=>({date:i,amount:a})).sort((i,a)=>i.date.localeCompare(a.date))}groupUsersByDay(t,e,i){let a=new Map,d=new Date(i.startDate);for(;d<=i.endDate;){let c=d.toISOString().split("T")[0];a.set(c,{newUsers:0,activeUsers:0}),d.setDate(d.getDate()+1)}return t.forEach(c=>{let v=new Date(c.created_at).toISOString().split("T")[0];if(a.has(v)){let D=a.get(v);a.set(v,qe(Le({},D),{newUsers:D.newUsers+1}))}}),e.forEach(c=>{let v=new Date(c.created_at).toISOString().split("T")[0];if(a.has(v)){let D=a.get(v);a.set(v,qe(Le({},D),{activeUsers:D.activeUsers+1}))}}),Array.from(a.entries()).map(([c,{newUsers:h,activeUsers:v}])=>({date:c,newUsers:h,activeUsers:v})).sort((c,h)=>c.date.localeCompare(h.date))}exportReportToCsv(t,e){let i="";switch(t){case"rides":i=this.exportRideReportToCsv(e);break;case"revenue":i=this.exportRevenueReportToCsv(e);break;case"users":i=this.exportUserReportToCsv(e);break}return i}exportRideReportToCsv(t){let e=`Report Type,Ride Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`Rides by Status
`,e+=`Status,Count
`,e+=`Requested,${t.ridesByStatus.requested}
`,e+=`Assigned,${t.ridesByStatus.assigned}
`,e+=`In Progress,${t.ridesByStatus.inProgress}
`,e+=`Completed,${t.ridesByStatus.completed}
`,e+=`Canceled,${t.ridesByStatus.canceled}

`,e+=`Average Metrics
`,e+=`Average Duration (minutes),${t.averageDuration.toFixed(2)}
`,e+=`Average Distance (miles),${t.averageDistance.toFixed(2)}

`,e+=`Rides by Day
`,e+=`Date,Count
`,t.ridesByDay.forEach(i=>{e+=`${i.date},${i.count}
`}),e}exportRevenueReportToCsv(t){let e=`Report Type,Revenue Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}
`,e+=`Total Revenue,$${t.totalRevenue.toFixed(2)}

`,e+=`Revenue by Payment Status
`,e+=`Status,Amount
`,e+=`Pending,$${t.revenueByStatus.pending.toFixed(2)}
`,e+=`Paid,$${t.revenueByStatus.paid.toFixed(2)}
`,e+=`Failed,$${t.revenueByStatus.failed.toFixed(2)}
`,e+=`Refunded,$${t.revenueByStatus.refunded.toFixed(2)}

`,e+=`Top Drivers by Revenue
`,e+=`Driver,Revenue,Ride Count
`,t.topDriversByRevenue.forEach(i=>{e+=`${i.driverName},$${i.revenue.toFixed(2)},${i.rideCount}
`}),e+=`
`,e+=`Revenue by Day
`,e+=`Date,Amount
`,t.revenueByDay.forEach(i=>{e+=`${i.date},$${i.amount.toFixed(2)}
`}),e}exportUserReportToCsv(t){let e=`Report Type,User Activity Statistics
`;return e+=`Date Range,${t.dateRange.startDate.toISOString().split("T")[0]} to ${t.dateRange.endDate.toISOString().split("T")[0]}

`,e+=`New Users
`,e+=`Type,Count
`,e+=`Total,${t.newUsers.total}
`,e+=`Riders,${t.newUsers.riders}
`,e+=`Drivers,${t.newUsers.drivers}

`,e+=`Active Users
`,e+=`Type,Count
`,e+=`Total,${t.activeUsers.total}
`,e+=`Riders,${t.activeUsers.riders}
`,e+=`Drivers,${t.activeUsers.drivers}

`,e+=`Top Riders
`,e+=`Rider,Ride Count,Total Spent
`,t.topRiders.forEach(i=>{e+=`${i.riderName},${i.rideCount},$${i.totalSpent.toFixed(2)}
`}),e+=`
`,e+=`User Activity by Day
`,e+=`Date,New Users,Active Users
`,t.usersByDay.forEach(i=>{e+=`${i.date},${i.newUsers},${i.activeUsers}
`}),e}static \u0275fac=function(e){return new(e||r)(it(Z),it(le),it(ze))};static \u0275prov=ri({token:r,factory:r.\u0275fac,providedIn:"root"})};var Sn=["userSort"],yn=["userPaginator"],bn=["rideSort"],Cn=["ridePaginator"],pr=()=>[5,10,25,100];function xn(r,t){if(r&1&&(n(0,"div",41)(1,"mat-grid-list",42)(2,"mat-grid-tile")(3,"mat-card",43)(4,"mat-card-header")(5,"mat-card-title"),s(6,"User Statistics"),o()(),n(7,"mat-card-content")(8,"div",44)(9,"div",45)(10,"div",46),s(11),o(),n(12,"div",47),s(13,"Total Users"),o()(),n(14,"div",45)(15,"div",46),s(16),o(),n(17,"div",47),s(18,"Riders"),o()(),n(19,"div",45)(20,"div",46),s(21),o(),n(22,"div",47),s(23,"Drivers"),o()(),n(24,"div",45)(25,"div",46),s(26),o(),n(27,"div",47),s(28,"Admins"),o()()()()()(),n(29,"mat-grid-tile")(30,"mat-card",43)(31,"mat-card-header")(32,"mat-card-title"),s(33,"Ride Statistics"),o()(),n(34,"mat-card-content")(35,"div",44)(36,"div",45)(37,"div",46),s(38),o(),n(39,"div",47),s(40,"Total Rides"),o()(),n(41,"div",45)(42,"div",46),s(43),o(),n(44,"div",47),s(45,"Requested"),o()(),n(46,"div",45)(47,"div",46),s(48),o(),n(49,"div",47),s(50,"In Progress"),o()(),n(51,"div",45)(52,"div",46),s(53),o(),n(54,"div",47),s(55,"Completed"),o()()()()()()()()),r&2){let e=g();l(11),M(e.statistics.totalUsers.all),l(5),M(e.statistics.totalUsers.riders),l(5),M(e.statistics.totalUsers.drivers),l(5),M(e.statistics.totalUsers.admins),l(12),M(e.statistics.rides.total),l(5),M(e.statistics.rides.requested),l(5),M(e.statistics.rides.inProgress),l(5),M(e.statistics.rides.completed)}}function Mn(r,t){r&1&&(n(0,"div",48),f(1,"mat-spinner",49),n(2,"p"),s(3,"Loading statistics..."),o()())}function Dn(r,t){r&1&&(n(0,"th",63),s(1,"Email"),o())}function Rn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;l(),M(e.email)}}function kn(r,t){r&1&&(n(0,"th",63),s(1,"Name"),o())}function In(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;l(),M(e.full_name||"N/A")}}function En(r,t){r&1&&(n(0,"th",63),s(1,"Role"),o())}function Tn(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit,i=g(2);l(),m("color",e.role==="admin"?"warn":"primary"),l(),R(" ",i.getRoleDisplayName(e.role)," ")}}function Pn(r,t){r&1&&(n(0,"th",63),s(1,"Registered"),o())}function An(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);l(),M(i.formatDate(e.created_at))}}function Fn(r,t){r&1&&(n(0,"th",66),s(1,"Status"),o())}function On(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit;l(),m("color",e.is_approved?"accent":"warn"),l(),R(" ",e.is_approved?"Active":e.role==="driver"?"Pending Approval":"Inactive"," ")}}function Bn(r,t){r&1&&(n(0,"th",66),s(1,"Actions"),o())}function Un(r,t){if(r&1){let e=P();n(0,"button",69),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.approveDriver(a.id))}),n(1,"mat-icon"),s(2,"check_circle"),o()()}}function zn(r,t){if(r&1){let e=P();n(0,"td",64),p(1,Un,3,0,"button",67),n(2,"button",68),S("click",function(){let a=b(e).$implicit,d=g(2);return C(d.openUserDetails(a))}),n(3,"mat-icon"),s(4,"visibility"),o()()()}if(r&2){let e=t.$implicit;l(),m("ngIf",e.role==="driver"&&!e.is_approved)}}function Vn(r,t){r&1&&f(0,"tr",70)}function Nn(r,t){r&1&&f(0,"tr",71)}function $n(r,t){if(r&1&&(n(0,"div")(1,"table",50,3),E(3,51),p(4,Dn,2,0,"th",52)(5,Rn,2,1,"td",53),T(),E(6,54),p(7,kn,2,0,"th",52)(8,In,2,1,"td",53),T(),E(9,55),p(10,En,2,0,"th",52)(11,Tn,3,2,"td",53),T(),E(12,56),p(13,Pn,2,0,"th",52)(14,An,2,1,"td",53),T(),E(15,57),p(16,Fn,2,0,"th",58)(17,On,3,2,"td",53),T(),E(18,59),p(19,Bn,2,0,"th",58)(20,zn,5,1,"td",53),T(),p(21,Vn,1,0,"tr",60)(22,Nn,1,0,"tr",61),o(),f(23,"mat-paginator",62,4),o()),r&2){let e=g();l(),m("dataSource",e.userDataSource),l(20),m("matHeaderRowDef",e.userDisplayedColumns),l(),m("matRowDefColumns",e.userDisplayedColumns),l(),m("pageSizeOptions",$t(5,pr))("pageSize",10)}}function Ln(r,t){r&1&&(n(0,"div",48),f(1,"mat-spinner",49),n(2,"p"),s(3,"Loading users..."),o()())}function qn(r,t){r&1&&(n(0,"th",63),s(1,"Rider"),o())}function jn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);l(),M(i.getUserName(e.rider_id))}}function Gn(r,t){r&1&&(n(0,"th",63),s(1,"Driver"),o())}function Hn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);l(),M(e.driver_id?i.getUserName(e.driver_id):"Not Assigned")}}function Qn(r,t){r&1&&(n(0,"th",63),s(1,"Pickup"),o())}function Wn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;l(),M(e.pickup_location)}}function Xn(r,t){r&1&&(n(0,"th",63),s(1,"Dropoff"),o())}function Jn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;l(),M(e.dropoff_location)}}function Kn(r,t){r&1&&(n(0,"th",63),s(1,"Price"),o())}function Zn(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit;l(),R("$",e.fare||"N/A","")}}function Yn(r,t){r&1&&(n(0,"th",63),s(1,"Status"),o())}function ea(r,t){if(r&1&&(n(0,"td",64)(1,"mat-chip",65),s(2),o()()),r&2){let e=t.$implicit,i=g(2);l(),m("color",i.getStatusColor(e.status)),l(),R(" ",i.getStatusDisplayName(e.status)," ")}}function ta(r,t){r&1&&(n(0,"th",63),s(1,"Created"),o())}function ia(r,t){if(r&1&&(n(0,"td",64),s(1),o()),r&2){let e=t.$implicit,i=g(2);l(),M(i.formatDate(e.created_at))}}function ra(r,t){r&1&&(n(0,"th",66),s(1,"Actions"),o())}function na(r,t){if(r&1){let e=P();n(0,"button",83),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.openDriverSelectionDialog(a.id))}),n(1,"mat-icon"),s(2,"person_add"),o()()}}function aa(r,t){if(r&1){let e=P();n(0,"button",84),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.openDriverSelectionDialog(a.id))}),n(1,"mat-icon"),s(2,"swap_horiz"),o()()}}function oa(r,t){if(r&1){let e=P();n(0,"button",85),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.updateRideStatus(a.id,"canceled"))}),n(1,"mat-icon"),s(2,"cancel"),o()()}}function sa(r,t){if(r&1){let e=P();n(0,"button",86),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.updateRideStatus(a.id,"in-progress"))}),n(1,"mat-icon"),s(2,"play_arrow"),o()()}}function da(r,t){if(r&1){let e=P();n(0,"button",87),S("click",function(){b(e);let a=g().$implicit,d=g(2);return C(d.updateRideStatus(a.id,"completed"))}),n(1,"mat-icon"),s(2,"check_circle"),o()()}}function la(r,t){if(r&1){let e=P();n(0,"td",64),p(1,na,3,0,"button",78)(2,aa,3,0,"button",79)(3,oa,3,0,"button",80)(4,sa,3,0,"button",81)(5,da,3,0,"button",82),n(6,"button",68),S("click",function(){let a=b(e).$implicit,d=g(2);return C(d.viewRideDetails(a.id))}),n(7,"mat-icon"),s(8,"visibility"),o()()()}if(r&2){let e=t.$implicit;l(),m("ngIf",e.status==="requested"),l(),m("ngIf",e.status==="assigned"),l(),m("ngIf",e.status==="requested"||e.status==="assigned"),l(),m("ngIf",e.status==="assigned"),l(),m("ngIf",e.status==="in-progress")}}function ca(r,t){r&1&&f(0,"tr",70)}function ma(r,t){r&1&&f(0,"tr",71)}function pa(r,t){if(r&1&&(n(0,"div")(1,"table",50,5),E(3,72),p(4,qn,2,0,"th",52)(5,jn,2,1,"td",53),T(),E(6,73),p(7,Gn,2,0,"th",52)(8,Hn,2,1,"td",53),T(),E(9,74),p(10,Qn,2,0,"th",52)(11,Wn,2,1,"td",53),T(),E(12,75),p(13,Xn,2,0,"th",52)(14,Jn,2,1,"td",53),T(),E(15,76),p(16,Kn,2,0,"th",52)(17,Zn,2,1,"td",53),T(),E(18,57),p(19,Yn,2,0,"th",52)(20,ea,3,2,"td",53),T(),E(21,56),p(22,ta,2,0,"th",52)(23,ia,2,1,"td",53),T(),E(24,59),p(25,ra,2,0,"th",58)(26,la,9,5,"td",53),T(),p(27,ca,1,0,"tr",60)(28,ma,1,0,"tr",61),o(),f(29,"mat-paginator",77,6),o()),r&2){let e=g();l(),m("dataSource",e.rideDataSource),l(26),m("matHeaderRowDef",e.rideDisplayedColumns),l(),m("matRowDefColumns",e.rideDisplayedColumns),l(),m("length",e.rides.length)("pageSizeOptions",$t(6,pr))("pageSize",5)}}function ua(r,t){r&1&&(n(0,"div",48),f(1,"mat-spinner",49),n(2,"p"),s(3,"Loading rides..."),o()())}function ga(r,t){if(r&1){let e=P();n(0,"div",88)(1,"app-ride-detail",89),S("rideUpdated",function(a){b(e);let d=g();return C(d.onRideUpdated(a))}),o()()}if(r&2){let e=g();l(),m("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}function ha(r,t){if(r&1){let e=P();n(0,"div",90),S("click",function(){b(e);let a=g();return C(a.closeRideDetails())}),n(1,"mat-icon"),s(2,"close"),o()()}}var mr=class r{constructor(t,e,i,a,d){this.userService=t;this.rideService=e;this.statisticsService=i;this.snackBar=a;this.dialog=d}users=[];userDataSource=new jt([]);userDisplayedColumns=["email","full_name","role","created_at","status","actions"];userRoleFilter="";userSearchTerm="";loadingUsers=!1;rides=[];rideDataSource=new jt([]);rideDisplayedColumns=["rider_id","driver_id","pickup_location","dropoff_location","status","created_at","actions"];rideStatusFilter="";rideSearchTerm="";loadingRides=!1;selectedRideId=null;lastRideRefreshTime=new Date;statistics=null;loadingStatistics=!1;userNameCache={};userNameLoadingCache={};refreshRidesInterval;ridesSubscription=null;userSort;userPaginator;rideSort;ridePaginator;dataLoadingComplete=!1;ngOnInit(){return x(this,null,function*(){console.log("Admin Component ngOnInit");try{yield Promise.all([this.loadUsers(),this.initialLoadRides(),this.loadStatistics()]),this.dataLoadingComplete=!0,this.userPaginator&&this.ridePaginator&&this.setupPaginators(),this.setupRideSubscription()}catch(t){console.error("Error loading data:",t),this.snackBar.open("Failed to load data","Close",{duration:3e3})}})}initialLoadRides(){return x(this,null,function*(){this.loadingRides=!0;try{this.rides=yield this.rideService.getAllRides(),this.rideDataSource.data=this.rides,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.lastRideRefreshTime=new Date}catch(t){throw console.error("Error loading rides:",t),t}finally{this.loadingRides=!1}this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator})}setupRideSubscription(){this.ridesSubscription&&this.ridesSubscription.unsubscribe(),this.ridesSubscription=this.rideService.rides$.subscribe(t=>{this.rideDataSource.data=t,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.hasRidesChanged(this.rides,t)&&(this.lastRideRefreshTime=new Date)})}getChangedRides(t,e){let i={hasChanges:!1,changedRides:[],addedRides:[],removedRideIds:[]},a=new Map,d=new Map;t.forEach(c=>a.set(c.id,c)),e.forEach(c=>d.set(c.id,c));for(let c of e)a.has(c.id)?a.get(c.id).updated_at!==c.updated_at&&(i.changedRides.push(c),i.hasChanges=!0):(i.addedRides.push(c),i.hasChanges=!0);for(let c of t)d.has(c.id)||(i.removedRideIds.push(c.id),i.hasChanges=!0);return i}hasRidesChanged(t,e){return this.getChangedRides(t,e).hasChanges}updateChangedRidesOnly(t){let e=this.getChangedRides(this.rides,t);if(!e.hasChanges)return;let i=[...this.rideDataSource.data];if(e.removedRideIds.length>0){let a=i.filter(d=>!e.removedRideIds.includes(d.id));this.rides=a,this.rideDataSource.data=a,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;return}if(e.addedRides.length>0){let a=[...e.addedRides,...i];this.rides=a,this.rideDataSource.data=a,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;return}if(e.changedRides.length>0){let a=new Map;i.forEach((d,c)=>{a.set(d.id,c)}),e.changedRides.forEach(d=>{let c=a.get(d.id);if(c!==void 0){i[c]=d;let h=this.rides.findIndex(v=>v.id===d.id);h!==-1&&(this.rides[h]=d)}}),this.rideDataSource.data=i,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator}}ngAfterViewInit(){this.dataLoadingComplete&&this.setupPaginators()}setupPaginators(){this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator,this.userDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),a=!0,d=!0;if(i.role&&(a=t.role===i.role),i.searchTerm){let c=i.searchTerm.toLowerCase(),h=t.email.toLowerCase().includes(c),v=t.full_name?t.full_name.toLowerCase().includes(c):!1;d=h||v}return a&&d},this.userDataSource.sortingDataAccessor=(t,e)=>{switch(e){case"created_at":return new Date(t.created_at).getTime();case"full_name":return t.full_name?.toLowerCase()||"";default:return t[e]||""}},this.rideDataSource.filterPredicate=(t,e)=>{let i=JSON.parse(e),a=!0,d=!0;if(i.status&&(a=t.status===i.status),i.searchTerm){let c=i.searchTerm.toLowerCase(),h=t.pickup_location.toLowerCase().includes(c),v=t.dropoff_location.toLowerCase().includes(c),D=(this.getUserName(t.rider_id)||"").toLowerCase().includes(c);d=h||v||D}return a&&d},this.rideDataSource.sortingDataAccessor=(t,e)=>{switch(console.log("SORTING ASSESOR ",e),e){case"rider_id":return this.getUserName(t.rider_id).toLowerCase();case"driver_id":return t.driver_id?this.getUserName(t.driver_id).toLowerCase():"zzz";case"created_at":case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;default:return t[e]||""}}}loadUsers(){return x(this,null,function*(){this.loadingUsers=!0;try{this.users=yield this.userService.getAllUsers(),this.userDataSource.data=this.users,this.userDataSource.sort=this.userSort,this.userDataSource.paginator=this.userPaginator,(this.userRoleFilter||this.userSearchTerm)&&this.applyUserFilters()}catch(t){console.error("Error loading users:",t),this.snackBar.open("Failed to load users","Close",{duration:3e3})}finally{this.loadingUsers=!1}})}applyUserFilters(){let t=JSON.stringify({role:this.userRoleFilter,searchTerm:this.userSearchTerm});this.userDataSource.filter=t,this.userDataSource.paginator&&this.dataLoadingComplete&&this.userDataSource.paginator.firstPage()}getRoleDisplayName(t){return t.charAt(0).toUpperCase()+t.slice(1)}approveDriver(t){return x(this,null,function*(){try{if(yield this.userService.approveDriver(t))this.snackBar.open("Driver approved successfully","Close",{duration:3e3});else throw new Error("Failed to approve driver")}catch(e){console.error("Error approving driver:",e),this.snackBar.open("Failed to approve driver","Close",{duration:3e3})}})}openUserDetails(t){this.dialog.open(Ot,{width:"500px",data:t})}getStatusDisplay(t){return t.role==="driver"?t.is_approved?"Approved":"Pending Approval":t.role==="admin"?t.is_approved?"Active":"Inactive":"Active"}loadRides(){return x(this,null,function*(){this.loadingRides=!0;try{let t=yield this.rideService.getAllRides();this.updateChangedRidesOnly(t),(this.rideStatusFilter||this.rideSearchTerm)&&this.applyRideFilters(),this.ridePaginator&&this.dataLoadingComplete&&(this.ridePaginator.length=this.rides.length),this.lastRideRefreshTime=new Date,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator}catch(t){console.error("Error loading rides:",t),this.snackBar.open("Failed to load rides","Close",{duration:3e3})}finally{this.loadingRides=!1}})}applyRideFilters(){let t=JSON.stringify({status:this.rideStatusFilter,searchTerm:this.rideSearchTerm});this.rideDataSource.filter=t,this.rideDataSource.paginator&&this.dataLoadingComplete&&this.rideDataSource.paginator.firstPage()}updateRideStatus(t,e){return x(this,null,function*(){try{if(yield this.rideService.updateRideStatus(t,e))this.snackBar.open(`Ride status updated to ${this.getStatusDisplayName(e)}`,"Close",{duration:3e3});else throw new Error("Failed to update ride status")}catch(i){console.error("Error updating ride status:",i),this.snackBar.open("Failed to update ride status","Close",{duration:3e3})}})}openDriverSelectionDialog(t){this.dialog.open(kt,{width:"500px",data:{drivers:this.users.filter(i=>i.role==="driver"&&i.is_approved)}}).afterClosed().subscribe(i=>x(this,null,function*(){if(i)try{if(yield this.rideService.assignRideToDriver(t,i))this.snackBar.open("Driver assigned successfully","Close",{duration:3e3});else throw new Error("Failed to assign driver")}catch(a){console.error("Error assigning driver:",a),this.snackBar.open("Failed to assign driver","Close",{duration:3e3})}}))}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null}onRideUpdated(t){let e=[...this.rideDataSource.data],i=e.findIndex(a=>a.id===t.id);if(i!==-1){e[i]=t,this.rideDataSource.data=e,this.rideDataSource.sort=this.rideSort,this.rideDataSource.paginator=this.ridePaginator;let a=this.rides.findIndex(d=>d.id===t.id);a!==-1&&(this.rides[a]=t),this.lastRideRefreshTime=new Date}}openCreateRideDialog(){this.dialog.open(zt,{width:"600px"}).afterClosed().subscribe(e=>{e&&this.snackBar.open("Ride created successfully","Close",{duration:3e3})})}loadStatistics(){return x(this,null,function*(){this.loadingStatistics=!0;try{this.statistics=yield this.statisticsService.generateSystemStatistics()}catch(t){console.error("Error loading statistics:",t),this.snackBar.open("Failed to load statistics","Close",{duration:3e3})}finally{this.loadingStatistics=!1}})}getStatusDisplayName(t){return t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}getStatusColor(t){return{requested:"warn",assigned:"primary","in-progress":"accent",completed:"primary",canceled:"warn"}[t]||"primary"}formatDate(t){return new Date(t).toLocaleString()}getUserName(t){return t&&(this.users.find(i=>i.id===t)?.full_name||t)||"N/A"}ngOnDestroy(){this.refreshRidesInterval&&clearInterval(this.refreshRidesInterval),this.ridesSubscription&&this.ridesSubscription.unsubscribe()}static \u0275fac=function(e){return new(e||r)(y(Z),y(le),y(Vt),y($),y(Si))};static \u0275cmp=O({type:r,selectors:[["app-admin"]],viewQuery:function(e,i){if(e&1&&(Y(Sn,5),Y(yn,5),Y(bn,5),Y(Cn,5)),e&2){let a;L(a=q())&&(i.userSort=a.first),L(a=q())&&(i.userPaginator=a.first),L(a=q())&&(i.rideSort=a.first),L(a=q())&&(i.ridePaginator=a.first)}},decls:86,vars:13,consts:[["loadingStats",""],["loadingUsersTemplate",""],["loadingRidesTemplate",""],["userSort","matSort"],["userPaginator",""],["rideSort","matSort"],["ridePaginator",""],[1,"dashboard-container"],[1,"dashboard-title"],["animationDuration","300ms"],["label","Dashboard Overview"],[1,"tab-content"],["class","stats-container",4,"ngIf","ngIfElse"],["label","User Management"],[1,"filters-container"],["appearance","outline"],[3,"ngModelChange","selectionChange","ngModel"],["value",""],["value","rider"],["value","driver"],["value","admin"],["matInput","","placeholder","Search by name or email",3,"ngModelChange","keyup","ngModel"],["matSuffix",""],[4,"ngIf","ngIfElse"],["label","Ride Management"],[1,"refresh-info"],[1,"last-refresh"],["mat-icon-button","","color","primary","matTooltip","Refresh rides manually",3,"click"],[1,"auto-refresh-note"],[1,"action-buttons"],["mat-raised-button","","color","primary",3,"click"],["value","requested"],["value","assigned"],["value","in-progress"],["value","completed"],["value","canceled"],["matInput","","placeholder","Search by location or name",3,"ngModelChange","keyup","ngModel"],["label","Stripe Payment Processing"],["label","Ride Pricing"],["class","ride-detail-overlay",4,"ngIf"],["class","close-overlay",3,"click",4,"ngIf"],[1,"stats-container"],["cols","2","rowHeight","250px","gutterSize","16px"],[1,"stats-card"],[1,"stats-grid"],[1,"stat-item"],[1,"stat-value"],[1,"stat-label"],[1,"loading-container"],["diameter","50"],["mat-table","","matSort","",1,"mat-elevation-z2","full-width",3,"dataSource"],["matColumnDef","email"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","full_name"],["matColumnDef","role"],["matColumnDef","created_at"],["matColumnDef","status"],["mat-header-cell","",4,"matHeaderCellDef"],["matColumnDef","actions"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["showFirstLastButtons","","aria-label","Select page of users",3,"pageSizeOptions","pageSize"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["selected","",3,"color"],["mat-header-cell",""],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","View Details",3,"click"],["mat-icon-button","","color","primary","matTooltip","Approve Driver",3,"click"],["mat-header-row",""],["mat-row",""],["matColumnDef","rider_id"],["matColumnDef","driver_id"],["matColumnDef","pickup_location"],["matColumnDef","dropoff_location"],["matColumnDef","price"],["showFirstLastButtons","","aria-label","Select page of rides",3,"length","pageSizeOptions","pageSize"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click",4,"ngIf"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","Assign Driver",3,"click"],["mat-icon-button","","color","primary","matTooltip","Reassign Driver",3,"click"],["mat-icon-button","","color","accent","matTooltip","Cancel Ride",3,"click"],["mat-icon-button","","color","warn","matTooltip","Start Ride",3,"click"],["mat-icon-button","","color","primary","matTooltip","Complete Ride",3,"click"],[1,"ride-detail-overlay"],[3,"rideUpdated","rideId","onClose"],[1,"close-overlay",3,"click"]],template:function(e,i){if(e&1){let a=P();n(0,"div",7)(1,"h1",8),s(2,"Admin Dashboard"),o(),n(3,"mat-tab-group",9)(4,"mat-tab",10)(5,"div",11),p(6,xn,56,8,"div",12)(7,Mn,4,0,"ng-template",null,0,at),o()(),n(9,"mat-tab",13)(10,"div",11)(11,"mat-card")(12,"mat-card-header")(13,"mat-card-title"),s(14,"User Management"),o()(),n(15,"mat-card-content")(16,"div",14)(17,"mat-form-field",15)(18,"mat-select",16),fe("ngModelChange",function(c){return b(a),he(i.userRoleFilter,c)||(i.userRoleFilter=c),C(c)}),S("selectionChange",function(){return b(a),C(i.applyUserFilters())}),n(19,"mat-option",17),s(20,"All Roles"),o(),n(21,"mat-option",18),s(22,"Rider"),o(),n(23,"mat-option",19),s(24,"Driver"),o(),n(25,"mat-option",20),s(26,"Admin"),o()()(),n(27,"mat-form-field",15)(28,"mat-label"),s(29,"Search"),o(),n(30,"input",21),fe("ngModelChange",function(c){return b(a),he(i.userSearchTerm,c)||(i.userSearchTerm=c),C(c)}),S("keyup",function(){return b(a),C(i.applyUserFilters())}),o(),n(31,"mat-icon",22),s(32,"search"),o()()(),p(33,$n,25,6,"div",23)(34,Ln,4,0,"ng-template",null,1,at),o()()()(),n(36,"mat-tab",24)(37,"div",11)(38,"mat-card")(39,"mat-card-header")(40,"mat-card-title"),s(41,"Ride Management"),o(),n(42,"div",25)(43,"span",26),s(44),o(),n(45,"button",27),S("click",function(){return b(a),C(i.loadRides())}),n(46,"mat-icon"),s(47,"refresh"),o()(),n(48,"span",28),s(49,"(Real-time updates)"),o()(),n(50,"div",29)(51,"button",30),S("click",function(){return b(a),C(i.openCreateRideDialog())}),n(52,"mat-icon"),s(53,"add"),o(),s(54," Create Ride "),o()()(),n(55,"mat-card-content")(56,"div",14)(57,"mat-form-field",15)(58,"mat-select",16),fe("ngModelChange",function(c){return b(a),he(i.rideStatusFilter,c)||(i.rideStatusFilter=c),C(c)}),S("selectionChange",function(){return b(a),C(i.applyRideFilters())}),n(59,"mat-option",17),s(60,"All Statuses"),o(),n(61,"mat-option",31),s(62,"Requested"),o(),n(63,"mat-option",32),s(64,"Assigned"),o(),n(65,"mat-option",33),s(66,"In Progress"),o(),n(67,"mat-option",34),s(68,"Completed"),o(),n(69,"mat-option",35),s(70,"Canceled"),o()()(),n(71,"mat-form-field",15)(72,"input",36),fe("ngModelChange",function(c){return b(a),he(i.rideSearchTerm,c)||(i.rideSearchTerm=c),C(c)}),S("keyup",function(){return b(a),C(i.applyRideFilters())}),o(),n(73,"mat-icon",22),s(74,"search"),o()()(),p(75,pa,31,7,"div",23)(76,ua,4,0,"ng-template",null,2,at),o()()()(),n(78,"mat-tab",37)(79,"div",11),f(80,"app-stripe-payment"),o()(),n(81,"mat-tab",38)(82,"div",11),f(83,"app-ride-pricing"),o()()()(),p(84,ga,2,2,"div",39)(85,ha,3,0,"div",40)}if(e&2){let a=ee(8),d=ee(35),c=ee(77);l(6),m("ngIf",i.statistics)("ngIfElse",a),l(12),ge("ngModel",i.userRoleFilter),l(12),ge("ngModel",i.userSearchTerm),l(3),m("ngIf",!i.loadingUsers)("ngIfElse",d),l(11),R("Last refreshed: ",i.formatDate(i.lastRideRefreshTime.toISOString()),""),l(14),ge("ngModel",i.rideStatusFilter),l(14),ge("ngModel",i.rideSearchTerm),l(3),m("ngIf",!i.loadingRides)("ngIfElse",c),l(9),m("ngIf",i.selectedRideId),l(),m("ngIf",i.selectedRideId)}},dependencies:[z,j,H,re,G,ct,ne,K,Fe,Be,Ue,Oe,Hi,ji,Gi,Ne,ht,_t,yt,vt,ft,bt,wt,St,Ct,xt,Zi,Ji,Ki,er,Yi,de,se,W,Q,pt,X,ae,Ae,Te,N,V,dt,J,oe,te,me,ce,gt,ut,Xi,tr,or,ar,Xt,Dt,Mt,ie,Wi,Bt,Ut],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.dashboard-title[_ngcontent-%COMP%]{margin-bottom:20px;color:#3f51b5;font-weight:500}.tab-content[_ngcontent-%COMP%]{padding:20px 0;overflow-y:auto}.filters-container[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px;margin-bottom:20px;align-items:center}.mat-form-field[_ngcontent-%COMP%]{flex:1;min-width:200px}.table-container[_ngcontent-%COMP%]{overflow-x:auto;margin-top:20px}table[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:16px;color:#0000008a}.stats-container[_ngcontent-%COMP%]{margin-top:20px;margin-bottom:20px}.stats-card[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column}.full-width[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;margin-top:16px;padding:16px}.scrollable-content[_ngcontent-%COMP%]{overflow-y:auto;max-height:calc(100% - 60px);padding-right:8px}mat-card-content[_ngcontent-%COMP%]{overflow-y:auto;flex:1}.stat-item[_ngcontent-%COMP%]{text-align:center;padding:10px;border-radius:4px;background-color:#3f51b51a}.stat-value[_ngcontent-%COMP%]{font-size:24px;font-weight:500;color:#1976d2}.stat-label[_ngcontent-%COMP%]{font-size:14px;color:#0000008a;margin-top:4px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto}@media (max-width: 768px){.filters-container[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.mat-form-field[_ngcontent-%COMP%]{width:100%}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}.mat-grid-tile[_ngcontent-%COMP%]{min-height:260px}.refresh-info[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:auto;font-size:14px;color:#0009}.last-refresh[_ngcontent-%COMP%]{margin-right:8px}.auto-refresh-note[_ngcontent-%COMP%]{margin-left:8px;font-style:italic;font-size:12px}.action-buttons[_ngcontent-%COMP%]{margin-left:20px;display:flex;align-items:center}.close-overlay[_ngcontent-%COMP%]{position:fixed;top:20px;right:20px;z-index:2000;cursor:pointer;background:#fff;border-radius:50%;box-shadow:0 2px 8px #00000026;padding:8px;display:flex;align-items:center;justify-content:center}"]})};export{mr as AdminComponent};
