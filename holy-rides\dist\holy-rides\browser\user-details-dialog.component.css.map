{"version": 3, "sources": ["src/app/features/dashboard/admin/user-details-dialog/user-details-dialog.component.ts"], "sourcesContent": ["\n    .user-details {\n      padding: 16px;\n    }\n\n    .detail-row {\n      display: flex;\n      align-items: center;\n      margin-bottom: 16px;\n    }\n\n    .label {\n      font-weight: 500;\n      min-width: 120px;\n      color: rgba(0, 0, 0, 0.54);\n    }\n\n    .value {\n      flex: 1;\n    }\n\n    mat-slide-toggle {\n      margin-left: 8px;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,iBAAA;;AAGF,CAAA;AACE,eAAA;AACA,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,QAAA;;AAGF;AACE,eAAA;;", "names": []}