{"version": 3, "sources": ["src/app/features/auth/register/register.component.scss"], "sourcesContent": [".register-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 100vh;\r\n  padding: 20px;\r\n\r\n\r\n  .logo-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 30px;\r\n\r\n    .logo {\r\n      width: 100px;\r\n      height: 100px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .app-name {\r\n      font-size: 24px;\r\n      font-weight: 500;\r\n      color: #3f51b5;\r\n      margin: 0;\r\n    }\r\n  }\r\n\r\n  mat-card {\r\n    width: 100%;\r\n    max-width: 400px;\r\n    padding: 20px;\r\n  }\r\n\r\n  mat-form-field {\r\n    width: 100%;\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .button-container {\r\n    margin-top: 24px;\r\n    display: flex;\r\n    justify-content: center;\r\n\r\n    button {\r\n      width: 100%;\r\n      padding: 8px;\r\n    }\r\n  }\r\n\r\n  .links {\r\n    margin-top: 16px;\r\n    text-align: center;\r\n\r\n    a {\r\n      color: #3f51b5;\r\n      text-decoration: none;\r\n\r\n      &:hover {\r\n        text-decoration: underline;\r\n      }\r\n    }\r\n  }\r\n\r\n  .error-message {\r\n    color: #f44336;\r\n    text-align: center;\r\n    margin: 8px 0;\r\n  }\r\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,kBAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA;AACA,WAAA;;AAGA,CATF,mBASE,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CAfJ,mBAeI,CANF,eAME,CAAA;AACE,SAAA;AACA,UAAA;AACA,iBAAA;;AAGF,CArBJ,mBAqBI,CAZF,eAYE,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,UAAA;;AAIJ,CA7BF,mBA6BE;AACE,SAAA;AACA,aAAA;AACA,WAAA;;AAGF,CAnCF,mBAmCE;AACE,SAAA;AACA,iBAAA;;AAGF,CAxCF,mBAwCE,CAAA;AACE,cAAA;AACA,WAAA;AACA,mBAAA;;AAEA,CA7CJ,mBA6CI,CALF,iBAKE;AACE,SAAA;AACA,WAAA;;AAIJ,CAnDF,mBAmDE,CAAA;AACE,cAAA;AACA,cAAA;;AAEA,CAvDJ,mBAuDI,CAJF,MAIE;AACE,SAAA;AACA,mBAAA;;AAEA,CA3DN,mBA2DM,CARJ,MAQI,CAAA;AACE,mBAAA;;AAKN,CAjEF,mBAiEE,CAAA;AACE,SAAA;AACA,cAAA;AACA,UAAA,IAAA;;", "names": []}