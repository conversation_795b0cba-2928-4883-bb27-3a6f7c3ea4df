import {
  MapDisplayComponent
} from "./chunk-ZBUZHDSF.js";
import "./chunk-ZG6RKMCP.js";
import "./chunk-R24BZZME.js";
import {
  CommonModule,
  Component,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵproperty,
  ɵɵtext
} from "./chunk-THPQGTPB.js";
import "./chunk-S35DAJRX.js";

// src/app/shared/components/map-test/map-test.component.ts
var MapTestComponent = class _MapTestComponent {
  static \u0275fac = function MapTestComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MapTestComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MapTestComponent, selectors: [["app-map-test"]], decls: 6, vars: 2, consts: [[3, "origin", "destination"]], template: function MapTestComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card")(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Google Maps Test");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "mat-card-content");
      \u0275\u0275element(5, "app-map-display", 0);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275property("origin", "New York, NY")("destination", "Boston, MA");
    }
  }, dependencies: [
    CommonModule,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MapDisplayComponent
  ], styles: ["\n\nmat-card[_ngcontent-%COMP%] {\n  margin: 16px;\n}\n/*# sourceMappingURL=map-test.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MapTestComponent, [{
    type: Component,
    args: [{ selector: "app-map-test", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatButtonModule,
      MapDisplayComponent
    ], template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Google Maps Test</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-map-display
          [origin]="'New York, NY'"
          [destination]="'Boston, MA'">
        </app-map-display>
      </mat-card-content>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;2573e39561db701f5d4c3fca0b6df259ee9526809b6dd5d8bce55b9deb28eb47;C:/Users/<USER>/code/holy rides/holy-rides/src/app/shared/components/map-test/map-test.component.ts */\nmat-card {\n  margin: 16px;\n}\n/*# sourceMappingURL=map-test.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MapTestComponent, { className: "MapTestComponent", filePath: "src/app/shared/components/map-test/map-test.component.ts", lineNumber: 35 });
})();
export {
  MapTestComponent
};
//# sourceMappingURL=chunk-NJWG5QWV.js.map
