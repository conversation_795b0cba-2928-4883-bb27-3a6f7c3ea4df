{"version": 3, "sources": ["src/app/features/dashboard/admin/driver-payment/driver-payment.component.ts"], "sourcesContent": ["\n    .container {\n      max-width: 800px;\n      margin: 20px auto;\n    }\n    .full-width {\n      width: 100%;\n      margin-bottom: 15px;\n    }\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 40px 0;\n    }\n    .button-container {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 20px;\n    }\n    .ride-details {\n      background-color: #f5f5f5;\n      border-radius: 4px;\n      padding: 15px;\n      margin: 15px 0;\n    }\n    .details-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n      gap: 10px;\n    }\n    .detail-item {\n      margin-bottom: 8px;\n    }\n    .label {\n      font-weight: 500;\n      margin-right: 8px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n    .value {\n      color: rgba(0, 0, 0, 0.87);\n    }\n    .payouts-section {\n      margin-top: 30px;\n    }\n    .payouts-table {\n      width: 100%;\n    }\n    .status-chip {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 16px;\n      font-size: 0.85em;\n      text-transform: capitalize;\n    }\n    .status-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n    .status-paid {\n      background-color: #4caf50;\n      color: white;\n    }\n    .status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n    .payout-section {\n      margin-top: 20px;\n      padding-top: 15px;\n      border-top: 1px solid #e0e0e0;\n    }\n    .payout-controls {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n      margin-top: 10px;\n    }\n    .amount-field, .percentage-field {\n      width: 150px;\n    }\n    .payout-options {\n      margin-top: 15px;\n      display: flex;\n      flex-direction: column;\n      gap: 15px;\n    }\n    .payout-type-group {\n      display: flex;\n      gap: 20px;\n    }\n    .payout-inputs {\n      margin-top: 10px;\n    }\n    .percentage-container {\n      display: flex;\n      align-items: center;\n      gap: 20px;\n    }\n    .calculated-amount {\n      background-color: #f5f5f5;\n      padding: 8px 12px;\n      border-radius: 4px;\n      display: inline-flex;\n      align-items: center;\n    }\n  "], "mappings": ";AACI,CAAA;AACE,aAAA;AACA,UAAA,KAAA;;AAEF,CAAA;AACE,SAAA;AACA,iBAAA;;AAEF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,KAAA;;AAEF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;AAEF,CAAA;AACE,oBAAA;AACA,iBAAA;AACA,WAAA;AACA,UAAA,KAAA;;AAEF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAEF,CAAA;AACE,iBAAA;;AAEF,CAAA;AACE,eAAA;AACA,gBAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEF,CAAA;AACE,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEF,CAAA;AACE,cAAA;;AAEF,CAAA;AACE,SAAA;;AAEF,CAAA;AACE,WAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,kBAAA;;AAEF,CAAA;AACE,oBAAA;AACA,SAAA;;AAEF,CAAA;AACE,oBAAA;AACA,SAAA;;AAEF,CAAA;AACE,oBAAA;AACA,SAAA;;AAEF,CAAA;AACE,cAAA;AACA,eAAA;AACA,cAAA,IAAA,MAAA;;AAEF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,cAAA;;AAEF,CAAA;AAAA,CAAA;AACE,SAAA;;AAEF,CAAA;AACE,cAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA;;AAEF,CAAA;AACE,WAAA;AACA,OAAA;;AAEF,CAAA;AACE,cAAA;;AAEF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEF,CAAA;AACE,oBAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,WAAA;AACA,eAAA;;", "names": []}