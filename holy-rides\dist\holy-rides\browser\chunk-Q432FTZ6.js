import{a as Je,b as Ke,c as et,d as tt,e as it}from"./chunk-JRFUAQJR.js";import{b as Ze}from"./chunk-EMQER2I7.js";import{c as Y,d as W,e as X,g as Fe,j as Be,k as Ue,l as Ne,m as je,n as qe,o as $e,p as ze,q as He,r as Le,s as Ge,t as Qe,u as Ye,v as re,w as We,x as Xe}from"./chunk-4GJZFU7Z.js";import{a as Ie,b as Ve,c as Oe,e as Te}from"./chunk-4XM5VEPX.js";import{a as De,b as ke}from"./chunk-OFTCLERB.js";import{a as Ae}from"./chunk-EACHO2FA.js";import"./chunk-ORDMVBIZ.js";import{a as Q,b as I}from"./chunk-WSXVBUWR.js";import"./chunk-3NZGXQSR.js";import"./chunk-ZN5FMN3P.js";import"./chunk-YTNZ52NK.js";import{a as L,b as G}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{c as fe}from"./chunk-3EEDYH74.js";import{C as Re,F as we,H as Pe,I as Ee,J as V,b as ge,d as h,f as _e,g as he,k as ve,n as Ce,o as be,s as Se,u as xe,w as ye,x as Me}from"./chunk-AG3SD6JT.js";import{Ba as ie,Bc as pe,Bd as H,Dc as ue,Eb as U,Fb as N,Fc as R,Gb as j,Gd as D,Ja as ne,Jd as k,Kb as r,La as l,Lb as x,Ld as O,Mb as q,Md as T,Nd as A,Pa as _,Qd as F,Rd as w,Tb as oe,Wa as M,Wb as $,Yb as z,Z as se,ab as d,ca as ce,g as le,hb as c,la as v,ma as C,qb as n,rb as o,sb as f,tb as b,tc as me,ub as S,va as de,vc as E,wb as y,yb as g,zb as u}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as P,b as B,i as p}from"./chunk-ODN5LVDJ.js";var Z=class i{constructor(t){this.authService=t;this.supabase=t.supabase,this.initializeMockVehicles()}supabase;vehiclesSubject=new le([]);vehicles$=this.vehiclesSubject.asObservable();initializeMockVehicles(){let t=[{id:"1",driver_id:"driver1",make:"Toyota",model:"Camry",year:2020,color:"Silver",license_plate:"ABC123",capacity:4,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"2",driver_id:"driver2",make:"Honda",model:"Accord",year:2019,color:"Black",license_plate:"XYZ789",capacity:5,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}];this.vehiclesSubject.next(t)}getDriverVehicles(t){return p(this,null,function*(){try{return this.vehiclesSubject.value.filter(e=>e.driver_id===t)}catch(e){return console.error("Error fetching driver vehicles:",e),[]}})}addVehicle(t){return p(this,null,function*(){try{let e=B(P({},t),{id:Math.random().toString(36).substring(2,9),created_at:new Date().toISOString(),updated_at:new Date().toISOString()}),a=this.vehiclesSubject.value;return this.vehiclesSubject.next([...a,e]),e}catch(e){return console.error("Error adding vehicle:",e),null}})}updateVehicle(t,e){return p(this,null,function*(){try{let s=this.vehiclesSubject.value.map(m=>m.id===t?B(P(P({},m),e),{updated_at:new Date().toISOString()}):m);return this.vehiclesSubject.next(s),!0}catch(a){return console.error("Error updating vehicle:",a),!1}})}deleteVehicle(t){return p(this,null,function*(){try{let a=this.vehiclesSubject.value.filter(s=>s.id!==t);return this.vehiclesSubject.next(a),!0}catch(e){return console.error("Error deleting vehicle:",e),!1}})}static \u0275fac=function(e){return new(e||i)(ce(V))};static \u0275prov=se({token:i,factory:i.\u0275fac,providedIn:"root"})};function dt(i,t){i&1&&(n(0,"mat-error"),r(1," Full name is required "),o())}function mt(i,t){i&1&&(n(0,"mat-error"),r(1," Phone number is required "),o())}var K=class i{constructor(t,e,a,s){this.fb=t;this.authService=e;this.vehicleService=a;this.snackBar=s;this.profileForm=this.fb.group({full_name:["",h.required],phone:["",h.required]}),this.vehicleForm=this.fb.group({make:["",h.required],model:["",h.required],year:["",[h.required,h.min(1990),h.max(this.currentYear)]],color:["",h.required],license_plate:["",h.required],capacity:[4,[h.required,h.min(1),h.max(10)]]})}profileForm;vehicleForm;vehicles=[];currentUser=null;editingVehicle=null;currentYear=new Date().getFullYear();ngOnInit(){this.loadUserProfile(),this.loadVehicles()}loadUserProfile(){return p(this,null,function*(){try{this.currentUser=yield this.authService.getCurrentUser(),this.currentUser&&this.profileForm.patchValue({full_name:this.currentUser.full_name||"",phone:this.currentUser.phone||""})}catch(t){console.error("Error loading user profile:",t),this.snackBar.open("Failed to load profile","Close",{duration:3e3})}})}loadVehicles(){return p(this,null,function*(){try{this.currentUser&&(this.vehicles=yield this.vehicleService.getDriverVehicles(this.currentUser.id))}catch(t){console.error("Error loading vehicles:",t),this.snackBar.open("Failed to load vehicles","Close",{duration:3e3})}})}updateProfile(){return p(this,null,function*(){if(!this.profileForm.invalid)try{if(yield this.authService.updateProfile(this.profileForm.value))this.snackBar.open("Profile updated successfully","Close",{duration:3e3});else throw new Error("Failed to update profile")}catch(t){console.error("Error updating profile:",t),this.snackBar.open("Failed to update profile","Close",{duration:3e3})}})}editVehicle(t){this.editingVehicle=t,this.vehicleForm.patchValue({make:t.make,model:t.model,year:t.year,color:t.color,license_plate:t.license_plate,capacity:t.capacity})}cancelEdit(){this.editingVehicle=null,this.vehicleForm.reset({capacity:4})}saveVehicle(){return p(this,null,function*(){if(!(this.vehicleForm.invalid||!this.currentUser))try{if(this.editingVehicle)if(yield this.vehicleService.updateVehicle(this.editingVehicle.id,this.vehicleForm.value))this.snackBar.open("Vehicle updated successfully","Close",{duration:3e3}),this.cancelEdit(),this.loadVehicles();else throw new Error("Failed to update vehicle");else{let t=B(P({},this.vehicleForm.value),{driver_id:this.currentUser.id});if(yield this.vehicleService.addVehicle(t))this.snackBar.open("Vehicle added successfully","Close",{duration:3e3}),this.vehicleForm.reset({capacity:4}),this.loadVehicles();else throw new Error("Failed to add vehicle")}}catch(t){console.error("Error saving vehicle:",t),this.snackBar.open("Failed to save vehicle","Close",{duration:3e3})}})}deleteVehicle(t){return p(this,null,function*(){try{if(yield this.vehicleService.deleteVehicle(t))this.snackBar.open("Vehicle deleted successfully","Close",{duration:3e3}),this.loadVehicles();else throw new Error("Failed to delete vehicle")}catch(e){console.error("Error deleting vehicle:",e),this.snackBar.open("Failed to delete vehicle","Close",{duration:3e3})}})}static \u0275fac=function(e){return new(e||i)(_(Se),_(V),_(Z),_(L))};static \u0275cmp=M({type:i,selectors:[["app-driver-profile"]],decls:19,vars:4,consts:[[1,"profile-container"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"full-width"],["matInput","","formControlName","full_name","placeholder","Enter your full name"],[4,"ngIf"],["matInput","","formControlName","phone","placeholder","Enter your phone number"],["mat-raised-button","","color","primary","type","submit",3,"disabled"]],template:function(e,a){if(e&1&&(n(0,"div",0)(1,"mat-card")(2,"mat-card-header")(3,"mat-card-title"),r(4,"Driver Profile"),o()(),n(5,"mat-card-content")(6,"form",1),g("ngSubmit",function(){return a.updateProfile()}),n(7,"mat-form-field",2)(8,"mat-label"),r(9,"Full Name"),o(),f(10,"input",3),d(11,dt,2,0,"mat-error",4),o(),n(12,"mat-form-field",2)(13,"mat-label"),r(14,"Phone Number"),o(),f(15,"input",5),d(16,mt,2,0,"mat-error",4),o(),n(17,"button",6),r(18," Update Profile "),o()()()()()),e&2){let s,m;l(6),c("formGroup",a.profileForm),l(5),c("ngIf",(s=a.profileForm.get("full_name"))==null?null:s.hasError("required")),l(5),c("ngIf",(m=a.profileForm.get("phone"))==null?null:m.hasError("required")),l(),c("disabled",a.profileForm.invalid||a.profileForm.pristine)}},dependencies:[R,E,xe,ve,ge,_e,he,Ce,be,w,O,A,F,T,we,Re,ye,Me,Ee,Pe,k,D,G,Te,I],styles:[".profile-container[_ngcontent-%COMP%]{padding:20px;max-width:800px;margin:0 auto}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.vehicle-card[_ngcontent-%COMP%]{margin-top:20px}.vehicle-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:16px 0;border-bottom:1px solid #eee}.vehicle-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.vehicle-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-weight:500}.vehicle-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;color:#666}.vehicle-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.divider[_ngcontent-%COMP%]{margin:16px 0}.vehicle-form[_ngcontent-%COMP%]{margin-top:16px}.form-row[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:8px}.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{flex:1}.form-actions[_ngcontent-%COMP%]{display:flex;gap:8px;margin-top:16px}.no-vehicles[_ngcontent-%COMP%]{padding:16px 0;color:#666;font-style:italic}"]})};function ut(i,t){if(i&1&&(n(0,"div",18)(1,"p")(2,"strong"),r(3,"Distance:"),o(),r(4),o(),n(5,"p")(6,"strong"),r(7,"Estimated Time:"),o(),r(8),o()()),i&2){let e=u(2);l(4),q(" ",e.ride.distance_miles," miles"),l(4),q(" ",e.ride.duration_minutes," minutes")}}function ft(i,t){if(i&1){let e=y();n(0,"div",1)(1,"mat-card",2)(2,"mat-card-header")(3,"mat-card-title"),r(4,"Navigation"),o(),n(5,"button",3),g("click",function(){v(e);let s=u();return C(s.closeNavigation())}),n(6,"mat-icon"),r(7,"close"),o()()(),n(8,"mat-card-content")(9,"div",4)(10,"div",5)(11,"div",6)(12,"mat-icon",7),r(13,"location_on"),o(),n(14,"div",8)(15,"span",9),r(16,"Pickup Location:"),o(),n(17,"span",10),r(18),o()()(),n(19,"div",6)(20,"mat-icon",11),r(21,"flag"),o(),n(22,"div",8)(23,"span",9),r(24,"Dropoff Location:"),o(),n(25,"span",10),r(26),o()()()(),f(27,"app-map-display",12),d(28,ut,9,2,"div",13),n(29,"div",14)(30,"a",15)(31,"button",16)(32,"mat-icon"),r(33,"navigation"),o(),r(34," Navigate to Pickup "),o()(),n(35,"a",15)(36,"button",17)(37,"mat-icon"),r(38,"navigation"),o(),r(39," Navigate to Dropoff "),o()()()()()()()}if(i&2){let e=u();l(18),x(e.ride.pickup_location),l(8),x(e.ride.dropoff_location),l(),c("origin",e.ride.pickup_location)("destination",e.ride.dropoff_location),l(),c("ngIf",e.ride.distance_miles&&e.ride.duration_minutes),l(2),c("href",e.googleMapsPickupUrl,ne),l(5),c("href",e.googleMapsDropoffUrl,ne)}}var ee=class i{constructor(t){this.locationService=t}ride=null;close=new de;googleMapsPickupUrl="";googleMapsDropoffUrl="";ngOnInit(){this.generateNavigationLinks()}generateNavigationLinks(){this.ride&&(this.googleMapsPickupUrl=this.locationService.getGoogleMapsUrl(this.ride.pickup_location),this.googleMapsDropoffUrl=this.locationService.getGoogleMapsUrl(this.ride.dropoff_location))}closeNavigation(){this.close.emit()}static \u0275fac=function(e){return new(e||i)(_(De))};static \u0275cmp=M({type:i,selectors:[["app-ride-navigation"]],inputs:{ride:"ride"},outputs:{close:"close"},decls:1,vars:1,consts:[["class","navigation-overlay",4,"ngIf"],[1,"navigation-overlay"],[1,"navigation-card"],["mat-icon-button","",1,"close-button",3,"click"],[1,"navigation-details"],[1,"location-info"],[1,"location-item"],[1,"location-icon","pickup"],[1,"location-text"],[1,"location-label"],[1,"location-value"],[1,"location-icon","dropoff"],[3,"origin","destination"],["class","route-info",4,"ngIf"],[1,"navigation-links"],["target","_blank",1,"nav-link",3,"href"],["mat-raised-button","","color","primary"],["mat-raised-button","","color","accent"],[1,"route-info"]],template:function(e,a){e&1&&d(0,ft,40,7,"div",0),e&2&&c("ngIf",a.ride)},dependencies:[R,E,w,O,A,F,T,k,D,H,I,Q,ke],styles:[".navigation-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.navigation-card[_ngcontent-%COMP%]{width:90%;max-width:600px;max-height:80vh;overflow-y:auto}.close-button[_ngcontent-%COMP%]{position:absolute;right:8px;top:8px}.navigation-details[_ngcontent-%COMP%]{padding:16px 0}.location-info[_ngcontent-%COMP%]{margin-bottom:24px}.location-item[_ngcontent-%COMP%]{display:flex;align-items:flex-start;margin-bottom:16px}.location-icon[_ngcontent-%COMP%]{margin-right:16px;color:#3f51b5}.location-icon.pickup[_ngcontent-%COMP%]{color:#4caf50}.location-icon.dropoff[_ngcontent-%COMP%]{color:#f44336}.location-text[_ngcontent-%COMP%]{display:flex;flex-direction:column}.location-label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:4px}.location-value[_ngcontent-%COMP%]{color:#666}.route-info[_ngcontent-%COMP%]{background-color:#f5f5f5;border-radius:4px;padding:16px;margin-bottom:24px}.route-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0}.navigation-links[_ngcontent-%COMP%]{display:flex;justify-content:space-around;flex-wrap:wrap;gap:16px}.nav-link[_ngcontent-%COMP%]{text-decoration:none}"]})};var _t=["availableSort"],ht=["availablePaginator"],vt=["myRidesSort"],Ct=["myRidesPaginator"],bt=()=>[3,5,10,25,100],St=()=>[5,10,25,100];function xt(i,t){i&1&&(n(0,"div",18),f(1,"mat-spinner",19),n(2,"p"),r(3,"Loading rides..."),o()())}function yt(i,t){i&1&&(n(0,"div",20)(1,"p"),r(2,"No available ride requests at this time."),o()())}function Mt(i,t){i&1&&(n(0,"th",32),r(1,"Pickup"),o())}function Rt(i,t){if(i&1&&(n(0,"td",33),r(1),o()),i&2){let e=t.$implicit;l(),x(e.pickup_location)}}function wt(i,t){i&1&&(n(0,"th",32),r(1,"Destination"),o())}function Pt(i,t){if(i&1&&(n(0,"td",33),r(1),o()),i&2){let e=t.$implicit;l(),x(e.dropoff_location)}}function Et(i,t){i&1&&(n(0,"th",32),r(1,"Time"),o())}function Dt(i,t){if(i&1&&(n(0,"td",33),r(1),$(2,"date"),o()),i&2){let e=t.$implicit;l(),x(z(2,1,e.pickup_time,"short"))}}function kt(i,t){i&1&&(n(0,"th",32),r(1,"Fare"),o())}function It(i,t){if(i&1&&(n(0,"td",33),r(1),$(2,"currency"),o()),i&2){let e=t.$implicit;l(),x(e.fare?z(2,1,e.fare*.7,"USD"):"TBD")}}function Vt(i,t){i&1&&(n(0,"th",34),r(1,"Actions"),o())}function Ot(i,t){if(i&1){let e=y();n(0,"td",33)(1,"button",35),g("click",function(){let s=v(e).$implicit,m=u(2);return C(m.acceptRide(s.id))}),r(2," Accept "),o()()}}function Tt(i,t){i&1&&f(0,"tr",36)}function At(i,t){i&1&&f(0,"tr",37)}function Ft(i,t){if(i&1&&(n(0,"table",21,2),b(2,22),d(3,Mt,2,0,"th",23)(4,Rt,2,1,"td",24),S(),b(5,25),d(6,wt,2,0,"th",23)(7,Pt,2,1,"td",24),S(),b(8,26),d(9,Et,2,0,"th",23)(10,Dt,3,4,"td",24),S(),b(11,27),d(12,kt,2,0,"th",23)(13,It,3,4,"td",24),S(),b(14,28),d(15,Vt,2,0,"th",29)(16,Ot,3,0,"td",24),S(),d(17,Tt,1,0,"tr",30)(18,At,1,0,"tr",31),o()),i&2){let e=u();c("dataSource",e.availableDataSource),l(17),c("matHeaderRowDef",e.availableColumns),l(),c("matRowDefColumns",e.availableColumns)}}function Bt(i,t){i&1&&(n(0,"div",18),f(1,"mat-spinner",19),n(2,"p"),r(3,"Loading rides..."),o()())}function Ut(i,t){i&1&&(n(0,"div",20)(1,"p"),r(2,"You don't have any assigned rides."),o()())}function Nt(i,t){i&1&&(n(0,"th",32),r(1,"Pickup"),o())}function jt(i,t){if(i&1&&(n(0,"td",33),r(1),o()),i&2){let e=t.$implicit;l(),x(e.pickup_location)}}function qt(i,t){i&1&&(n(0,"th",32),r(1,"Destination"),o())}function $t(i,t){if(i&1&&(n(0,"td",33),r(1),o()),i&2){let e=t.$implicit;l(),x(e.dropoff_location)}}function zt(i,t){i&1&&(n(0,"th",32),r(1,"Time"),o())}function Ht(i,t){if(i&1&&(n(0,"td",33),r(1),$(2,"date"),o()),i&2){let e=t.$implicit;l(),x(z(2,1,e.pickup_time,"short"))}}function Lt(i,t){i&1&&(n(0,"th",32),r(1,"Status"),o())}function Gt(i,t){if(i&1&&(n(0,"td",33)(1,"span",39),r(2),o()()),i&2){let e=t.$implicit;l(),c("ngClass","status-"+e.status),l(),q(" ",e.status," ")}}function Qt(i,t){i&1&&(n(0,"th",32),r(1,"Fare"),o())}function Yt(i,t){if(i&1&&(n(0,"td",33),r(1),$(2,"currency"),o()),i&2){let e=t.$implicit;l(),x(e.fare?z(2,1,e.fare*.7,"USD"):"TBD")}}function Wt(i,t){i&1&&(n(0,"th",34),r(1,"Actions"),o())}function Xt(i,t){if(i&1){let e=y();n(0,"button",35),g("click",function(){v(e);let s=u().$implicit,m=u(2);return C(m.startRide(s.id))}),r(1," Start Ride "),o()}}function Zt(i,t){if(i&1){let e=y();n(0,"button",44),g("click",function(){v(e);let s=u().$implicit,m=u(2);return C(m.completeRide(s.id))}),r(1," Complete "),o()}}function Jt(i,t){if(i&1){let e=y();n(0,"button",45),g("click",function(){v(e);let s=u().$implicit,m=u(2);return C(m.showNavigation(s))}),n(1,"mat-icon"),r(2,"navigation"),o()()}}function Kt(i,t){if(i&1){let e=y();n(0,"td",33),d(1,Xt,2,0,"button",40)(2,Zt,2,0,"button",41)(3,Jt,3,0,"button",42),n(4,"button",43),g("click",function(){let s=v(e).$implicit,m=u(2);return C(m.viewRideDetails(s.id))}),n(5,"mat-icon"),r(6,"visibility"),o()()()}if(i&2){let e=t.$implicit;l(),c("ngIf",e.status==="assigned"),l(),c("ngIf",e.status==="in-progress"),l(),c("ngIf",e.status!=="completed")}}function ei(i,t){i&1&&f(0,"tr",36)}function ti(i,t){i&1&&f(0,"tr",37)}function ii(i,t){if(i&1&&(n(0,"table",21,3),b(2,22),d(3,Nt,2,0,"th",23)(4,jt,2,1,"td",24),S(),b(5,25),d(6,qt,2,0,"th",23)(7,$t,2,1,"td",24),S(),b(8,26),d(9,zt,2,0,"th",23)(10,Ht,3,4,"td",24),S(),b(11,38),d(12,Lt,2,0,"th",23)(13,Gt,3,2,"td",24),S(),b(14,27),d(15,Qt,2,0,"th",23)(16,Yt,3,4,"td",24),S(),b(17,28),d(18,Wt,2,0,"th",29)(19,Kt,7,3,"td",24),S(),d(20,ei,1,0,"tr",30)(21,ti,1,0,"tr",31),o()),i&2){let e=u();c("dataSource",e.myRidesDataSource),l(20),c("matHeaderRowDef",e.myRidesColumns),l(),c("matRowDefColumns",e.myRidesColumns)}}function ni(i,t){if(i&1){let e=y();n(0,"app-ride-navigation",46),g("close",function(){v(e);let s=u();return C(s.selectedRide=null)}),o()}if(i&2){let e=u();c("ride",e.selectedRide)}}function oi(i,t){if(i&1){let e=y();n(0,"div",47)(1,"app-ride-detail",48),g("rideUpdated",function(s){v(e);let m=u();return C(m.onRideUpdated(s))}),o()()}if(i&2){let e=u();l(),c("rideId",e.selectedRideId)("onClose",e.closeRideDetails.bind(e))}}var te=class i{constructor(t,e,a,s,m){this.rideService=t;this.authService=e;this.messageService=a;this.router=s;this.snackBar=m}currentUser=null;selectedRide=null;selectedRideId=null;loading=!1;ridesSubscription=null;availableRides=ie([]);myRides=ie([]);availableColumns=["pickup_location","dropoff_location","pickup_time","fare","actions"];myRidesColumns=["pickup_location","dropoff_location","pickup_time","status","fare","actions"];availableDataSource=new re([]);myRidesDataSource=new re([]);availableSort;availablePaginator;myRidesSort;myRidesPaginator;ngOnInit(){return p(this,null,function*(){try{yield this.loadCurrentUser(),this.currentUser&&(this.setupRealtimeSubscription(),yield this.loadRides())}catch(t){console.error("Error loading current user:",t),this.snackBar.open("Failed to load user information","Close",{duration:3e3})}})}ngAfterViewInit(){setTimeout(()=>{this.setupDataSources()},0)}ngOnDestroy(){this.ridesSubscription&&this.ridesSubscription.unsubscribe()}loadCurrentUser(){return p(this,null,function*(){try{this.loading=!0,this.currentUser=yield this.authService.getCurrentUser()}catch(t){console.error("Error loading current user:",t),this.snackBar.open("Failed to load user information","Close",{duration:3e3})}finally{this.loading=!1}})}setupRealtimeSubscription(){this.currentUser&&(this.ridesSubscription=this.rideService.rides$.subscribe(t=>{if(t&&t.length>=0){let e=t.filter(s=>s.status==="requested"),a=t.filter(s=>s.driver_id===this.currentUser?.id&&["assigned","in-progress","completed"].includes(s.status));this.availableRides.set(e),this.myRides.set(a),this.availableDataSource.data=e,this.myRidesDataSource.data=a,console.log("Realtime update - Available rides:",e.length,"My rides:",a.length)}}))}setupDataSources(){console.log("Setting up data sources..."),console.log("Available sort:",this.availableSort),console.log("Available paginator:",this.availablePaginator),console.log("My rides sort:",this.myRidesSort),console.log("My rides paginator:",this.myRidesPaginator),this.availableSort&&(this.availableDataSource.sort=this.availableSort,console.log("Connected available rides sort")),this.availablePaginator&&(this.availableDataSource.paginator=this.availablePaginator,console.log("Connected available rides paginator")),this.myRidesSort&&(this.myRidesDataSource.sort=this.myRidesSort,console.log("Connected my rides sort")),this.myRidesPaginator&&(this.myRidesDataSource.paginator=this.myRidesPaginator,console.log("Connected my rides paginator")),this.availableDataSource.sortingDataAccessor=(t,e)=>{switch(console.log("Sorting available rides by:",e,"for item:",t),e){case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;case"pickup_location":case"dropoff_location":return t[e]?.toLowerCase()||"";default:return t[e]||""}},this.myRidesDataSource.sortingDataAccessor=(t,e)=>{switch(console.log("Sorting my rides by:",e,"for item:",t),e){case"pickup_time":return t[e]?new Date(t[e]).getTime():0;case"fare":return t.fare||0;case"pickup_location":case"dropoff_location":case"status":return t[e]?.toLowerCase()||"";default:return t[e]||""}},console.log("Data sources setup complete")}loadRides(){return p(this,null,function*(){if(this.currentUser){console.log("Loading rides..."),this.loading=!0;try{let[t,e]=yield Promise.all([this.rideService.getAvailableRides(),this.rideService.getDriverRides(this.currentUser.id)]);this.availableRides.set(t),this.myRides.set(e),this.availableDataSource.data=t,this.myRidesDataSource.data=e,setTimeout(()=>{this.setupDataSources()},0),console.log("Available Rides:",t),console.log("My Rides:",e)}catch(t){console.error("Error loading rides:",t),this.snackBar.open(t.message||"Failed to load rides","Close",{duration:3e3})}finally{this.loading=!1}}})}acceptRide(t){return p(this,null,function*(){if(this.currentUser)try{yield this.rideService.acceptRide(t,this.currentUser.id),this.snackBar.open("Ride accepted successfully","Close",{duration:3e3})}catch(e){console.error("Error accepting ride:",e),this.snackBar.open(e.message||"Failed to accept ride","Close",{duration:3e3})}})}startRide(t){return p(this,null,function*(){try{yield this.rideService.startRide(t),this.snackBar.open("Ride started successfully","Close",{duration:3e3})}catch(e){console.error("Error starting ride:",e),this.snackBar.open(e.message||"Failed to start ride","Close",{duration:3e3})}})}completeRide(t){return p(this,null,function*(){try{yield this.rideService.completeRide(t),this.snackBar.open("Ride completed successfully","Close",{duration:3e3})}catch(e){console.error("Error completing ride:",e),this.snackBar.open(e.message||"Failed to complete ride","Close",{duration:3e3})}})}showNavigation(t){this.selectedRide=t}closeNavigation(){this.selectedRide=null}openChat(t){return p(this,null,function*(){try{let e=yield this.messageService.getOrCreateThreadForRide(t);yield this.router.navigate(["/dashboard","driver","messages",e.id])}catch(e){console.error("Error opening chat:",e),this.snackBar.open(e.message||"Failed to open chat","Close",{duration:3e3})}})}viewRideDetails(t){this.selectedRideId=t}closeRideDetails(){this.selectedRideId=null}onRideUpdated(t){}static \u0275fac=function(e){return new(e||i)(_(Ie),_(V),_(Ae),_(fe),_(L))};static \u0275cmp=M({type:i,selectors:[["app-ride-assignments"]],viewQuery:function(e,a){if(e&1&&(U(_t,5),U(ht,5),U(vt,5),U(Ct,5)),e&2){let s;N(s=j())&&(a.availableSort=s.first),N(s=j())&&(a.availablePaginator=s.first),N(s=j())&&(a.myRidesSort=s.first),N(s=j())&&(a.myRidesPaginator=s.first)}},decls:34,vars:17,consts:[["availablePaginator",""],["myRidesPaginator",""],["availableSort","matSort"],["myRidesSort","matSort"],[1,"assignments-container"],["label","Available Rides",3,"tabIndex"],[1,"table-container"],[1,"header-with-actions"],[1,"realtime-indicator"],["mat-icon-button","","color","primary","matTooltip","Manual refresh",3,"click"],["class","loading-container",4,"ngIf"],["class","no-rides",4,"ngIf"],["mat-table","","matSort","","class","ride-table",3,"dataSource",4,"ngIf"],["showFirstLastButtons","","aria-label","Select page of available rides",3,"pageSizeOptions","pageSize"],["label","My Rides",3,"tabIndex","disabled"],["showFirstLastButtons","","aria-label","Select page of my rides",3,"pageSizeOptions","pageSize"],[3,"ride","close",4,"ngIf"],["class","ride-detail-overlay",4,"ngIf"],[1,"loading-container"],["diameter","40"],[1,"no-rides"],["mat-table","","matSort","",1,"ride-table",3,"dataSource"],["matColumnDef","pickup_location"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","dropoff_location"],["matColumnDef","pickup_time"],["matColumnDef","fare"],["matColumnDef","actions"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell","","mat-sort-header",""],["mat-cell",""],["mat-header-cell",""],["mat-raised-button","","color","primary",3,"click"],["mat-header-row",""],["mat-row",""],["matColumnDef","status"],[1,"status-chip",3,"ngClass"],["mat-raised-button","","color","primary",3,"click",4,"ngIf"],["mat-raised-button","","color","accent",3,"click",4,"ngIf"],["mat-icon-button","","color","primary",3,"click",4,"ngIf"],["mat-icon-button","","color","primary","matTooltip","View Details",3,"click"],["mat-raised-button","","color","accent",3,"click"],["mat-icon-button","","color","primary",3,"click"],[3,"close","ride"],[1,"ride-detail-overlay"],[3,"rideUpdated","rideId","onClose"]],template:function(e,a){if(e&1){let s=y();n(0,"div",4)(1,"mat-tab-group")(2,"mat-tab",5)(3,"div",6)(4,"div",7)(5,"h3"),r(6,"Available Ride Requests "),n(7,"span",8),r(8,"\u25CF"),o()(),n(9,"button",9),g("click",function(){return v(s),C(a.loadRides())}),n(10,"mat-icon"),r(11,"refresh"),o()()(),d(12,xt,4,0,"div",10)(13,yt,3,0,"div",11)(14,Ft,19,3,"table",12),f(15,"mat-paginator",13,0),o()(),n(17,"mat-tab",14)(18,"div",6)(19,"div",7)(20,"h3"),r(21,"My Assigned Rides "),n(22,"span",8),r(23,"\u25CF"),o()(),n(24,"button",9),g("click",function(){return v(s),C(a.loadRides())}),n(25,"mat-icon"),r(26,"refresh"),o()()(),d(27,Bt,4,0,"div",10)(28,Ut,3,0,"div",11)(29,ii,22,3,"table",12),f(30,"mat-paginator",15,1),o()()(),d(32,ni,1,1,"app-ride-navigation",16)(33,oi,2,2,"div",17),o()}e&2&&(l(2),c("tabIndex",0),l(10),c("ngIf",a.loading),l(),c("ngIf",!a.loading&&a.availableRides().length===0),l(),c("ngIf",!a.loading||a.availableRides().length>0),l(),c("pageSizeOptions",oe(15,bt))("pageSize",3),l(2),c("tabIndex",1)("disabled",a.myRides().length===0),l(10),c("ngIf",a.loading),l(),c("ngIf",!a.loading&&a.myRides().length===0),l(),c("ngIf",!a.loading&&a.myRides().length>0),l(),c("pageSizeOptions",oe(16,St))("pageSize",5),l(2),c("ngIf",a.selectedRide),l(),c("ngIf",a.selectedRideId))},dependencies:[R,me,E,ue,pe,w,X,Y,W,Ye,Ue,je,He,qe,Ne,Le,$e,ze,Ge,Qe,Be,k,D,H,I,Q,G,Ze,Xe,We,Oe,Ve,ee,Fe,et,Je,Ke,it,tt],styles:[".assignments-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto}.table-container[_ngcontent-%COMP%]{margin:20px}.ride-table[_ngcontent-%COMP%]{width:100%}.no-rides[_ngcontent-%COMP%]{padding:20px;text-align:center;color:#666;font-style:italic}.status-chip[_ngcontent-%COMP%]{border-radius:16px;padding:4px 12px;color:#fff;font-weight:500}.status-requested[_ngcontent-%COMP%]{background-color:#ff9800}.status-assigned[_ngcontent-%COMP%]{background-color:#2196f3}.status-in-progress[_ngcontent-%COMP%]{background-color:#673ab7}.status-completed[_ngcontent-%COMP%]{background-color:#4caf50}.status-canceled[_ngcontent-%COMP%]{background-color:#f44336}.header-with-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px;color:#666}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-top:10px}.ride-detail-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.realtime-indicator[_ngcontent-%COMP%]{color:#4caf50;font-size:12px;animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}"]})};var lt=class i{static \u0275fac=function(e){return new(e||i)};static \u0275cmp=M({type:i,selectors:[["app-driver"]],decls:13,vars:0,consts:[[1,"dashboard-container"],[1,"welcome-card"],["label","Ride Assignments"],["label","Driver Profile"]],template:function(e,a){e&1&&(n(0,"div",0)(1,"mat-card",1)(2,"mat-card-header")(3,"mat-card-title"),r(4,"Driver Dashboard"),o()(),n(5,"mat-card-content")(6,"p"),r(7,"Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments."),o()()(),n(8,"mat-tab-group")(9,"mat-tab",2),f(10,"app-ride-assignments"),o(),n(11,"mat-tab",3),f(12,"app-driver-profile"),o()()())},dependencies:[R,w,O,A,F,T,X,Y,W,K,te],styles:[".dashboard-container[_ngcontent-%COMP%]{padding:20px;max-width:1200px;margin:0 auto;background-color:#f5f5f5}.welcome-card[_ngcontent-%COMP%]{margin-bottom:20px}ul[_ngcontent-%COMP%]{list-style-type:none;padding:0;margin:16px 0}li[_ngcontent-%COMP%]{padding:8px 0}"]})};export{lt as DriverComponent};
