import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>pickerInput,
  MatDatepickerModule,
  MatDatepickerToggle,
  loadStripe
} from "./chunk-7DVHA7CA.js";
import {
  MatList,
  MatListItem,
  MatListModule
} from "./chunk-XLQ6RWBY.js";
import {
  MatDialog,
  MatDialogModule,
  MatNativeDateModule,
  NgxMatTimepickerComponent,
  NgxMatTimepickerDirective,
  NgxMatTimepickerModule,
  NgxMatTimepickerToggleComponent,
  NgxMatTimepickerToggleIconDirective
} from "./chunk-BCQ7UHFI.js";
import "./chunk-JURKDGMK.js";
import {
  Mat<PERSON><PERSON>rdion,
  MatCell,
  MatCellDef,
  MatChip,
  MatChipListbox,
  MatChipsModule,
  MatColumnDef,
  MatExpansionModule,
  MatExpansionPanel,
  MatExpansionPanelActionRow,
  MatExpansionPanelDescription,
  MatExpansionPanelHeader,
  MatExpansionPanel<PERSON>itle,
  <PERSON><PERSON>eader<PERSON>ell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTab,
  MatTabGroup,
  MatTable,
  MatTableModule,
  MatTabsModule,
  MatTooltip,
  MatTooltipModule,
  PaymentService,
  RideDetailComponent
} from "./chunk-WNL2OBGV.js";
import {
  MatDivider,
  MatDividerModule,
  MatProgressSpinner,
  MatProgressSpinnerModule,
  RideService
} from "./chunk-PSNUENZ6.js";
import {
  LocationService,
  MapDisplayComponent
} from "./chunk-ZBUZHDSF.js";
import {
  MessageService
} from "./chunk-XQHBKW3P.js";
import "./chunk-ZG6RKMCP.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-R24BZZME.js";
import "./chunk-NVGZCMKL.js";
import "./chunk-GWPOTN5B.js";
import "./chunk-5DER6JXC.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-MSLYEYGK.js";
import "./chunk-LTJSKJGW.js";
import "./chunk-QIPXWAWB.js";
import {
  Router
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MatError,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  MatLabel,
  MatSuffix,
  MaxValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NumberValueAccessor,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  ChangeDetectionStrategy,
  CommonModule,
  Component,
  DatePipe,
  EventEmitter,
  Input,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  MatIconButton,
  NgClass,
  NgForOf,
  NgIf,
  Output,
  ViewChild,
  environment,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵviewQuery
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-S35DAJRX.js";

// src/app/features/dashboard/rider/ride-request/ride-request.component.ts
function RideRequestComponent_mat_error_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Pickup location is required ");
    \u0275\u0275elementEnd();
  }
}
function RideRequestComponent_mat_error_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Dropoff location is required ");
    \u0275\u0275elementEnd();
  }
}
function RideRequestComponent_div_20_div_2_p_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Distance: ", ctx_r1.estimatedDistance, " miles");
  }
}
function RideRequestComponent_div_20_div_2_p_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Duration: ", ctx_r1.estimatedDuration, " minutes");
  }
}
function RideRequestComponent_div_20_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17)(1, "p");
    \u0275\u0275text(2, "Estimated fare: ");
    \u0275\u0275elementStart(3, "strong");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(5, RideRequestComponent_div_20_div_2_p_5_Template, 2, 1, "p", 7)(6, RideRequestComponent_div_20_div_2_p_6_Template, 2, 1, "p", 7);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r1.estimatedFare ? "$" + ctx_r1.estimatedFare.toFixed(2) : "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.estimatedDistance);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.estimatedDuration);
  }
}
function RideRequestComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275element(1, "app-map-display", 15);
    \u0275\u0275template(2, RideRequestComponent_div_20_div_2_Template, 7, 3, "div", 16);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_3_0;
    let tmp_4_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("origin", (tmp_3_0 = ctx_r1.rideForm.get("pickup_location")) == null ? null : tmp_3_0.value)("destination", (tmp_4_0 = ctx_r1.rideForm.get("dropoff_location")) == null ? null : tmp_4_0.value);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.estimatedFare);
  }
}
function RideRequestComponent_mat_error_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Pickup date is required ");
    \u0275\u0275elementEnd();
  }
}
function RideRequestComponent_mat_error_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Pickup time is required ");
    \u0275\u0275elementEnd();
  }
}
var RideRequestComponent = class _RideRequestComponent {
  formBuilder;
  rideService;
  authService;
  locationService;
  paymentService;
  snackBar;
  rideForm;
  loading = false;
  showMap = false;
  estimatedFare = null;
  estimatedDistance = null;
  estimatedDuration = null;
  locationCoordinates = {};
  constructor(formBuilder, rideService, authService, locationService, paymentService, snackBar) {
    this.formBuilder = formBuilder;
    this.rideService = rideService;
    this.authService = authService;
    this.locationService = locationService;
    this.paymentService = paymentService;
    this.snackBar = snackBar;
    this.rideForm = this.formBuilder.group({
      pickup_location: ["", Validators.required],
      dropoff_location: ["", Validators.required],
      //passengers: [1, [Validators.required, Validators.min(1), Validators.max(4)]],
      pickup_date: [/* @__PURE__ */ new Date(), Validators.required],
      pickup_time: ["12:00 PM", Validators.required]
      // notes: ['']
    });
  }
  ngOnInit() {
    this.rideForm.get("pickup_location")?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });
    this.rideForm.get("dropoff_location")?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });
  }
  useCurrentLocation() {
    return __async(this, null, function* () {
      try {
        const coords = yield this.locationService.getCurrentLocation();
        this.rideForm.patchValue({
          pickup_location: `Current Location (${coords.latitude.toFixed(6)}, ${coords.longitude.toFixed(6)})`
        });
        this.locationCoordinates.pickup = coords;
        this.snackBar.open("Current location detected", "Close", { duration: 2e3 });
      } catch (error) {
        this.snackBar.open(error.message || "Failed to get current location", "Close", { duration: 3e3 });
      }
    });
  }
  updateRouteEstimates() {
    return __async(this, null, function* () {
      const pickup = this.rideForm.get("pickup_location")?.value;
      const dropoff = this.rideForm.get("dropoff_location")?.value;
      if (pickup && dropoff) {
        this.showMap = true;
        try {
          const { fare, routeInfo } = yield this.paymentService.estimateFare(pickup, dropoff);
          this.estimatedFare = fare;
          this.estimatedDistance = routeInfo.distance;
          this.estimatedDuration = routeInfo.duration;
        } catch (error) {
          console.error("Error calculating route:", error);
        }
      } else {
        this.showMap = false;
        this.estimatedFare = null;
        this.estimatedDistance = null;
        this.estimatedDuration = null;
      }
    });
  }
  onSubmit() {
    return __async(this, null, function* () {
      if (this.rideForm.invalid)
        return;
      this.loading = true;
      try {
        const user = yield this.authService.getCurrentUser();
        if (!user)
          throw new Error("User not found");
        if (!this.locationCoordinates.pickup) {
          this.locationCoordinates.pickup = yield this.locationService.geocodeAddress(this.rideForm.value.pickup_location);
        }
        if (!this.locationCoordinates.dropoff) {
          this.locationCoordinates.dropoff = yield this.locationService.geocodeAddress(this.rideForm.value.dropoff_location);
        }
        const routeInfo = yield this.locationService.calculateRoute(this.locationCoordinates.pickup, this.locationCoordinates.dropoff);
        console.log(routeInfo);
        const pickupDate = this.rideForm.value.pickup_date;
        const pickupTime = this.rideForm.value.pickup_time;
        const combinedDateTime = new Date(pickupDate);
        const timeParts = pickupTime.match(/(\d+):(\d+)\s?(AM|PM)?/i);
        if (timeParts) {
          let hours = parseInt(timeParts[1], 10);
          const minutes = parseInt(timeParts[2], 10);
          const period = timeParts[3] ? timeParts[3].toUpperCase() : null;
          if (period === "PM" && hours < 12) {
            hours += 12;
          } else if (period === "AM" && hours === 12) {
            hours = 0;
          }
          combinedDateTime.setHours(hours, minutes, 0, 0);
        }
        const ride = __spreadProps(__spreadValues({}, this.rideForm.value), {
          rider_id: user.id,
          status: "requested",
          pickup_time: combinedDateTime.toISOString(),
          // Add location coordinates
          pickup_latitude: this.locationCoordinates.pickup?.latitude,
          pickup_longitude: this.locationCoordinates.pickup?.longitude,
          dropoff_latitude: this.locationCoordinates.dropoff?.latitude,
          dropoff_longitude: this.locationCoordinates.dropoff?.longitude,
          // Add route information
          distance_miles: routeInfo.distance,
          duration_minutes: routeInfo.duration,
          // Add fare
          fare: this.estimatedFare || (yield this.paymentService.estimateFare(this.rideForm.value.pickup_location, this.rideForm.value.dropoff_location))
        });
        yield this.rideService.createRide(ride);
        this.snackBar.open("Ride requested successfully!", "Close", { duration: 3e3 });
        this.rideForm.reset({
          passengers: 1,
          pickup_date: /* @__PURE__ */ new Date(),
          pickup_time: "12:00 PM"
        });
        this.showMap = false;
        this.estimatedFare = null;
        this.estimatedDistance = null;
        this.estimatedDuration = null;
        this.locationCoordinates = {};
      } catch (error) {
        this.snackBar.open(error.message || "Failed to request ride", "Close", { duration: 3e3 });
      } finally {
        this.loading = false;
      }
    });
  }
  static \u0275fac = function RideRequestComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RideRequestComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(RideService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(LocationService), \u0275\u0275directiveInject(PaymentService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RideRequestComponent, selectors: [["app-ride-request"]], decls: 42, vars: 12, consts: [["picker", ""], ["timepicker", ""], [3, "ngSubmit", "formGroup"], [1, "location-fields"], ["appearance", "outline"], ["matInput", "", "formControlName", "pickup_location", "placeholder", "Enter pickup location"], ["mat-icon-button", "", "matSuffix", "", "type", "button", "title", "Use current location", 3, "click"], [4, "ngIf"], ["matInput", "", "formControlName", "dropoff_location", "placeholder", "Enter dropoff location"], ["matInput", "", "formControlName", "pickup_date", 3, "matDatepicker"], ["matSuffix", "", 3, "for"], ["matInput", "", "formControlName", "pickup_time", 3, "ngxMatTimepicker"], ["ngxMatTimepickerToggleIcon", ""], [1, "button-container"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], [3, "origin", "destination"], ["class", "fare-estimate", 4, "ngIf"], [1, "fare-estimate"]], template: function RideRequestComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275elementStart(0, "mat-card")(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Request a Ride");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "mat-card-content")(5, "form", 2);
      \u0275\u0275listener("ngSubmit", function RideRequestComponent_Template_form_ngSubmit_5_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.onSubmit());
      });
      \u0275\u0275elementStart(6, "div", 3)(7, "mat-form-field", 4)(8, "mat-label");
      \u0275\u0275text(9, "Pickup Location");
      \u0275\u0275elementEnd();
      \u0275\u0275element(10, "input", 5);
      \u0275\u0275elementStart(11, "button", 6);
      \u0275\u0275listener("click", function RideRequestComponent_Template_button_click_11_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.useCurrentLocation());
      });
      \u0275\u0275elementStart(12, "mat-icon");
      \u0275\u0275text(13, "my_location");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(14, RideRequestComponent_mat_error_14_Template, 2, 0, "mat-error", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "mat-form-field", 4)(16, "mat-label");
      \u0275\u0275text(17, "Dropoff Location");
      \u0275\u0275elementEnd();
      \u0275\u0275element(18, "input", 8);
      \u0275\u0275template(19, RideRequestComponent_mat_error_19_Template, 2, 0, "mat-error", 7);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(20, RideRequestComponent_div_20_Template, 3, 3, "div", 7);
      \u0275\u0275elementStart(21, "mat-form-field", 4)(22, "mat-label");
      \u0275\u0275text(23, "Pickup Date");
      \u0275\u0275elementEnd();
      \u0275\u0275element(24, "input", 9)(25, "mat-datepicker-toggle", 10)(26, "mat-datepicker", null, 0);
      \u0275\u0275template(28, RideRequestComponent_mat_error_28_Template, 2, 0, "mat-error", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "mat-form-field", 4)(30, "mat-label");
      \u0275\u0275text(31, "Pick a time");
      \u0275\u0275elementEnd();
      \u0275\u0275element(32, "input", 11);
      \u0275\u0275elementStart(33, "ngx-mat-timepicker-toggle", 10)(34, "mat-icon", 12);
      \u0275\u0275text(35, "keyboard_arrow_down");
      \u0275\u0275elementEnd()();
      \u0275\u0275element(36, "ngx-mat-timepicker", null, 1);
      \u0275\u0275template(38, RideRequestComponent_mat_error_38_Template, 2, 0, "mat-error", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "div", 13)(40, "button", 14);
      \u0275\u0275text(41);
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      let tmp_3_0;
      let tmp_4_0;
      let tmp_5_0;
      let tmp_8_0;
      let tmp_11_0;
      const picker_r3 = \u0275\u0275reference(27);
      const timepicker_r4 = \u0275\u0275reference(37);
      \u0275\u0275advance(5);
      \u0275\u0275property("formGroup", ctx.rideForm);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", (tmp_3_0 = ctx.rideForm.get("pickup_location")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["required"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_4_0 = ctx.rideForm.get("dropoff_location")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showMap && ((tmp_5_0 = ctx.rideForm.get("pickup_location")) == null ? null : tmp_5_0.value) && ((tmp_5_0 = ctx.rideForm.get("dropoff_location")) == null ? null : tmp_5_0.value));
      \u0275\u0275advance(4);
      \u0275\u0275property("matDatepicker", picker_r3);
      \u0275\u0275advance();
      \u0275\u0275property("for", picker_r3);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", (tmp_8_0 = ctx.rideForm.get("pickup_date")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors["required"]);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngxMatTimepicker", timepicker_r4);
      \u0275\u0275advance();
      \u0275\u0275property("for", timepicker_r4);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_11_0 = ctx.rideForm.get("pickup_time")) == null ? null : tmp_11_0.errors == null ? null : tmp_11_0.errors["required"]);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.rideForm.invalid || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.loading ? "Requesting..." : "Request Ride", " ");
    }
  }, dependencies: [
    CommonModule,
    NgIf,
    ReactiveFormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    FormGroupDirective,
    FormControlName,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatError,
    MatSuffix,
    MatInputModule,
    MatInput,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatDatepickerModule,
    MatDatepicker,
    MatDatepickerInput,
    MatDatepickerToggle,
    MatNativeDateModule,
    MatIconModule,
    MatIcon,
    MapDisplayComponent,
    NgxMatTimepickerModule,
    NgxMatTimepickerComponent,
    NgxMatTimepickerToggleComponent,
    NgxMatTimepickerDirective,
    NgxMatTimepickerToggleIconDirective
  ], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  margin: 20px;\n}\nform[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  max-width: 600px;\n  margin: 0 auto;\n}\n.location-fields[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.button-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin-top: 16px;\n}\ntextarea[_ngcontent-%COMP%] {\n  min-height: 100px;\n}\n.fare-estimate[_ngcontent-%COMP%] {\n  background-color: #f5f5f5;\n  padding: 16px;\n  border-radius: 4px;\n  margin-top: 16px;\n  margin-bottom: 16px;\n}\n.fare-estimate[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\n/*# sourceMappingURL=ride-request.component.css.map */"], changeDetection: 0 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RideRequestComponent, [{
    type: Component,
    args: [{ selector: "app-ride-request", standalone: true, changeDetection: ChangeDetectionStrategy.OnPush, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatDatepickerModule,
      MatNativeDateModule,
      MatIconModule,
      MapDisplayComponent,
      NgxMatTimepickerModule
    ], template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Request a Ride</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="rideForm" (ngSubmit)="onSubmit()">
          <div class="location-fields">
            <mat-form-field appearance="outline">
              <mat-label>Pickup Location</mat-label>
              <input matInput formControlName="pickup_location" placeholder="Enter pickup location">
              <button mat-icon-button matSuffix type="button" (click)="useCurrentLocation()" title="Use current location">
                <mat-icon>my_location</mat-icon>
              </button>
              <mat-error *ngIf="rideForm.get('pickup_location')?.errors?.['required']">
                Pickup location is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Dropoff Location</mat-label>
              <input matInput formControlName="dropoff_location" placeholder="Enter dropoff location">
              <mat-error *ngIf="rideForm.get('dropoff_location')?.errors?.['required']">
                Dropoff location is required
              </mat-error>
            </mat-form-field>
          </div>

          <div *ngIf="showMap && rideForm.get('pickup_location')?.value && rideForm.get('dropoff_location')?.value">
            <app-map-display
              [origin]="rideForm.get('pickup_location')?.value"
              [destination]="rideForm.get('dropoff_location')?.value">
            </app-map-display>

            <div *ngIf="estimatedFare" class="fare-estimate">
              <p>Estimated fare: <strong>{{estimatedFare ? '$' + estimatedFare.toFixed(2) : ''}}</strong></p>
              <p *ngIf="estimatedDistance">Distance: {{estimatedDistance}} miles</p>
              <p *ngIf="estimatedDuration">Duration: {{estimatedDuration}} minutes</p>
            </div>
          </div>
<!--
          <mat-form-field appearance="outline">
            <mat-label>Number of Passengers</mat-label>
            <input matInput type="number" formControlName="passengers" min="1" max="4">
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['required']">
              Number of passengers is required
            </mat-error>
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['min']">
              Must have at least 1 passenger
            </mat-error>
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['max']">
              Maximum 4 passengers allowed
            </mat-error>
          </mat-form-field> -->

          <mat-form-field appearance="outline">
            <mat-label>Pickup Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="pickup_date">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="rideForm.get('pickup_date')?.errors?.['required']">
              Pickup date is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Pick a time</mat-label>
            <input matInput [ngxMatTimepicker]="timepicker" formControlName="pickup_time">
            <ngx-mat-timepicker-toggle matSuffix [for]="timepicker">
              <mat-icon ngxMatTimepickerToggleIcon>keyboard_arrow_down</mat-icon>
            </ngx-mat-timepicker-toggle>
            <ngx-mat-timepicker #timepicker></ngx-mat-timepicker>
            <mat-error *ngIf="rideForm.get('pickup_time')?.errors?.['required']">
              Pickup time is required
            </mat-error>
          </mat-form-field>

          <!-- <mat-form-field appearance="outline">
            <mat-label>Special Notes</mat-label>
            <textarea matInput formControlName="notes" placeholder="Any special requirements?"></textarea>
          </mat-form-field> -->

          <div class="button-container">
            <button mat-raised-button color="primary" type="submit" [disabled]="rideForm.invalid || loading">
              {{ loading ? 'Requesting...' : 'Request Ride' }}
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;5a5e64ac02d5b03a619f532b8a7335aaff007ba695d0d4f976fe3bd35d8272c9;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/ride-request/ride-request.component.ts */\n:host {\n  display: block;\n  margin: 20px;\n}\nform {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  max-width: 600px;\n  margin: 0 auto;\n}\n.location-fields {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.button-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 16px;\n}\ntextarea {\n  min-height: 100px;\n}\n.fare-estimate {\n  background-color: #f5f5f5;\n  padding: 16px;\n  border-radius: 4px;\n  margin-top: 16px;\n  margin-bottom: 16px;\n}\n.fare-estimate p {\n  margin: 8px 0;\n}\n/*# sourceMappingURL=ride-request.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: RideService }, { type: AuthService }, { type: LocationService }, { type: PaymentService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RideRequestComponent, { className: "RideRequestComponent", filePath: "src/app/features/dashboard/rider/ride-request/ride-request.component.ts", lineNumber: 170 });
})();

// src/app/features/dashboard/rider/payment-methods/payment-methods.component.ts
function PaymentMethodsComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 2);
    \u0275\u0275element(1, "mat-spinner", 3);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading payment methods...");
    \u0275\u0275elementEnd()();
  }
}
function PaymentMethodsComponent_div_6_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 16)(1, "p");
    \u0275\u0275text(2, "You don't have any payment methods yet.");
    \u0275\u0275elementEnd()();
  }
}
function PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_span_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 25);
    \u0275\u0275text(1, "Default");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 26);
    \u0275\u0275listener("click", function PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_button_11_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const method_r4 = \u0275\u0275nextContext().$implicit;
      const ctx_r4 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r4.setDefaultPaymentMethod(method_r4.id));
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "star_outline");
    \u0275\u0275elementEnd()();
  }
}
function PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-list-item")(1, "div", 18)(2, "div", 19)(3, "mat-icon");
    \u0275\u0275text(4, "credit_card");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "span");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "span", 20);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_span_9_Template, 2, 0, "span", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 22);
    \u0275\u0275template(11, PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_button_11_Template, 3, 0, "button", 23);
    \u0275\u0275elementStart(12, "button", 24);
    \u0275\u0275listener("click", function PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_Template_button_click_12_listener() {
      const method_r4 = \u0275\u0275restoreView(_r2).$implicit;
      const ctx_r4 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r4.removePaymentMethod(method_r4.id));
    });
    \u0275\u0275elementStart(13, "mat-icon");
    \u0275\u0275text(14, "delete");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const method_r4 = ctx.$implicit;
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate2("", method_r4.brand, " \u2022\u2022\u2022\u2022 ", method_r4.last4, "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2("Expires ", method_r4.expMonth, "/", method_r4.expYear, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", method_r4.isDefault);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !method_r4.isDefault);
  }
}
function PaymentMethodsComponent_div_6_mat_list_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-list");
    \u0275\u0275template(1, PaymentMethodsComponent_div_6_mat_list_2_mat_list_item_1_Template, 15, 6, "mat-list-item", 17);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r4 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r4.paymentMethods);
  }
}
function PaymentMethodsComponent_div_6_mat_divider_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "mat-divider", 27);
  }
}
function PaymentMethodsComponent_div_6_mat_error_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Card number is required ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Invalid card number ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Expiration month is required ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Month must be between 1 and 12 ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Expiration year is required ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r4 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" Year must be ", ctx_r4.currentYear, " or later ");
  }
}
function PaymentMethodsComponent_div_6_mat_error_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " CVV is required ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_mat_error_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " CVV must be 3 or 4 digits ");
    \u0275\u0275elementEnd();
  }
}
function PaymentMethodsComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275template(1, PaymentMethodsComponent_div_6_div_1_Template, 3, 0, "div", 4)(2, PaymentMethodsComponent_div_6_mat_list_2_Template, 2, 1, "mat-list", 1)(3, PaymentMethodsComponent_div_6_mat_divider_3_Template, 1, 0, "mat-divider", 5);
    \u0275\u0275elementStart(4, "div", 6)(5, "h3");
    \u0275\u0275text(6, "Add New Payment Method");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "form", 7);
    \u0275\u0275listener("ngSubmit", function PaymentMethodsComponent_div_6_Template_form_ngSubmit_7_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r4 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r4.addPaymentMethod());
    });
    \u0275\u0275elementStart(8, "mat-form-field", 8)(9, "mat-label");
    \u0275\u0275text(10, "Card Number");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 9);
    \u0275\u0275template(12, PaymentMethodsComponent_div_6_mat_error_12_Template, 2, 0, "mat-error", 1)(13, PaymentMethodsComponent_div_6_mat_error_13_Template, 2, 0, "mat-error", 1);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "div", 10)(15, "mat-form-field", 8)(16, "mat-label");
    \u0275\u0275text(17, "Expiration Month");
    \u0275\u0275elementEnd();
    \u0275\u0275element(18, "input", 11);
    \u0275\u0275template(19, PaymentMethodsComponent_div_6_mat_error_19_Template, 2, 0, "mat-error", 1)(20, PaymentMethodsComponent_div_6_mat_error_20_Template, 2, 0, "mat-error", 1);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "mat-form-field", 8)(22, "mat-label");
    \u0275\u0275text(23, "Expiration Year");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "input", 12);
    \u0275\u0275template(25, PaymentMethodsComponent_div_6_mat_error_25_Template, 2, 0, "mat-error", 1)(26, PaymentMethodsComponent_div_6_mat_error_26_Template, 2, 1, "mat-error", 1);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "mat-form-field", 8)(28, "mat-label");
    \u0275\u0275text(29, "CVV");
    \u0275\u0275elementEnd();
    \u0275\u0275element(30, "input", 13);
    \u0275\u0275template(31, PaymentMethodsComponent_div_6_mat_error_31_Template, 2, 0, "mat-error", 1)(32, PaymentMethodsComponent_div_6_mat_error_32_Template, 2, 0, "mat-error", 1);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "div", 14)(34, "button", 15);
    \u0275\u0275text(35);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    let tmp_5_0;
    let tmp_6_0;
    let tmp_7_0;
    let tmp_8_0;
    let tmp_9_0;
    let tmp_10_0;
    let tmp_11_0;
    let tmp_12_0;
    const ctx_r4 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r4.paymentMethods.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r4.paymentMethods.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r4.paymentMethods.length > 0);
    \u0275\u0275advance(4);
    \u0275\u0275property("formGroup", ctx_r4.paymentForm);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_5_0 = ctx_r4.paymentForm.get("cardNumber")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_6_0 = ctx_r4.paymentForm.get("cardNumber")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors["pattern"]);
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", (tmp_7_0 = ctx_r4.paymentForm.get("expMonth")) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_8_0 = ctx_r4.paymentForm.get("expMonth")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors["min"]) || ((tmp_8_0 = ctx_r4.paymentForm.get("expMonth")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors["max"]));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_9_0 = ctx_r4.paymentForm.get("expYear")) == null ? null : tmp_9_0.errors == null ? null : tmp_9_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_10_0 = ctx_r4.paymentForm.get("expYear")) == null ? null : tmp_10_0.errors == null ? null : tmp_10_0.errors["min"]);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (tmp_11_0 = ctx_r4.paymentForm.get("cvv")) == null ? null : tmp_11_0.errors == null ? null : tmp_11_0.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_12_0 = ctx_r4.paymentForm.get("cvv")) == null ? null : tmp_12_0.errors == null ? null : tmp_12_0.errors["pattern"]);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r4.paymentForm.invalid || ctx_r4.submitting);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r4.submitting ? "Adding..." : "Add Payment Method", " ");
  }
}
var PaymentMethodsComponent = class _PaymentMethodsComponent {
  formBuilder;
  authService;
  snackBar;
  paymentMethods = [];
  paymentForm;
  loading = false;
  submitting = false;
  currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  constructor(formBuilder, authService, snackBar) {
    this.formBuilder = formBuilder;
    this.authService = authService;
    this.snackBar = snackBar;
    this.paymentForm = this.formBuilder.group({
      cardNumber: ["", [Validators.required, Validators.pattern(/^[0-9]{16}$/)]],
      expMonth: ["", [Validators.required, Validators.min(1), Validators.max(12)]],
      expYear: ["", [Validators.required, Validators.min(this.currentYear)]],
      cvv: ["", [Validators.required, Validators.pattern(/^[0-9]{3,4}$/)]]
    });
  }
  ngOnInit() {
    this.loadPaymentMethods();
  }
  loadPaymentMethods() {
    this.loading = true;
    setTimeout(() => {
      this.paymentMethods = [
        {
          id: "pm_1",
          brand: "Visa",
          last4: "4242",
          expMonth: 12,
          expYear: 2025,
          isDefault: true
        },
        {
          id: "pm_2",
          brand: "Mastercard",
          last4: "5555",
          expMonth: 10,
          expYear: 2024,
          isDefault: false
        }
      ];
      this.loading = false;
    }, 1e3);
  }
  addPaymentMethod() {
    if (this.paymentForm.invalid)
      return;
    this.submitting = true;
    setTimeout(() => {
      const formValue = this.paymentForm.value;
      const newMethod = {
        id: `pm_${Math.random().toString(36).substring(2, 9)}`,
        brand: this.getCardBrand(formValue.cardNumber),
        last4: formValue.cardNumber.slice(-4),
        expMonth: formValue.expMonth,
        expYear: formValue.expYear,
        isDefault: this.paymentMethods.length === 0
        // Make default if it's the first one
      };
      this.paymentMethods.push(newMethod);
      this.paymentForm.reset();
      this.submitting = false;
      this.snackBar.open("Payment method added successfully", "Close", { duration: 3e3 });
    }, 1500);
  }
  setDefaultPaymentMethod(id) {
    this.paymentMethods = this.paymentMethods.map((method) => __spreadProps(__spreadValues({}, method), {
      isDefault: method.id === id
    }));
    this.snackBar.open("Default payment method updated", "Close", { duration: 3e3 });
  }
  removePaymentMethod(id) {
    const isDefault = this.paymentMethods.find((m) => m.id === id)?.isDefault;
    this.paymentMethods = this.paymentMethods.filter((method) => method.id !== id);
    if (isDefault && this.paymentMethods.length > 0) {
      this.paymentMethods[0].isDefault = true;
    }
    this.snackBar.open("Payment method removed", "Close", { duration: 3e3 });
  }
  getCardBrand(cardNumber) {
    const firstDigit = cardNumber.charAt(0);
    switch (firstDigit) {
      case "4":
        return "Visa";
      case "5":
        return "Mastercard";
      case "3":
        return "Amex";
      case "6":
        return "Discover";
      default:
        return "Card";
    }
  }
  static \u0275fac = function PaymentMethodsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PaymentMethodsComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PaymentMethodsComponent, selectors: [["app-payment-methods"]], decls: 7, vars: 2, consts: [["class", "loading-container", 4, "ngIf"], [4, "ngIf"], [1, "loading-container"], ["diameter", "40"], ["class", "no-methods", 4, "ngIf"], ["class", "divider", 4, "ngIf"], [1, "add-payment-section"], [3, "ngSubmit", "formGroup"], ["appearance", "outline"], ["matInput", "", "formControlName", "cardNumber", "placeholder", "1234 5678 9012 3456"], [1, "form-row"], ["matInput", "", "type", "number", "formControlName", "expMonth", "placeholder", "MM", "min", "1", "max", "12"], ["matInput", "", "type", "number", "formControlName", "expYear", "placeholder", "YYYY", "min", "2023"], ["matInput", "", "formControlName", "cvv", "placeholder", "123"], [1, "button-container"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], [1, "no-methods"], [4, "ngFor", "ngForOf"], [1, "payment-method-item"], [1, "payment-method-info"], [1, "expiry"], ["class", "default-badge", 4, "ngIf"], [1, "payment-method-actions"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Set as default", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "warn", "matTooltip", "Remove", 3, "click"], [1, "default-badge"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Set as default", 3, "click"], [1, "divider"]], template: function PaymentMethodsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "mat-card")(1, "mat-card-header")(2, "mat-card-title");
      \u0275\u0275text(3, "Payment Methods");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "mat-card-content");
      \u0275\u0275template(5, PaymentMethodsComponent_div_5_Template, 4, 0, "div", 0)(6, PaymentMethodsComponent_div_6_Template, 36, 14, "div", 1);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NumberValueAccessor, NgControlStatus, NgControlStatusGroup, MinValidator, MaxValidator, FormGroupDirective, FormControlName, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, MatFormFieldModule, MatFormField, MatLabel, MatError, MatInputModule, MatInput, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MatDividerModule, MatDivider, MatListModule, MatList, MatListItem, MatProgressSpinnerModule, MatProgressSpinner], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  margin: 20px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n.no-methods[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 20px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.payment-method-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  padding: 8px 0;\n}\n.payment-method-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.expiry[_ngcontent-%COMP%] {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 0.9em;\n  margin-left: 8px;\n}\n.default-badge[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.8em;\n  margin-left: 8px;\n}\n.divider[_ngcontent-%COMP%] {\n  margin: 20px 0;\n}\n.add-payment-section[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\nform[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  max-width: 500px;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n}\n.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.button-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 16px;\n}\n/*# sourceMappingURL=payment-methods.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PaymentMethodsComponent, [{
    type: Component,
    args: [{ selector: "app-payment-methods", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatIconModule,
      MatDividerModule,
      MatListModule,
      MatProgressSpinnerModule
    ], template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Payment Methods</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading payment methods...</p>
        </div>

        <div *ngIf="!loading">
          <div *ngIf="paymentMethods.length === 0" class="no-methods">
            <p>You don't have any payment methods yet.</p>
          </div>

          <mat-list *ngIf="paymentMethods.length > 0">
            <mat-list-item *ngFor="let method of paymentMethods">
              <div class="payment-method-item">
                <div class="payment-method-info">
                  <mat-icon>credit_card</mat-icon>
                  <span>{{ method.brand }} \u2022\u2022\u2022\u2022 {{ method.last4 }}</span>
                  <span class="expiry">Expires {{ method.expMonth }}/{{ method.expYear }}</span>
                  <span *ngIf="method.isDefault" class="default-badge">Default</span>
                </div>
                <div class="payment-method-actions">
                  <button mat-icon-button color="primary" *ngIf="!method.isDefault"
                          (click)="setDefaultPaymentMethod(method.id)" matTooltip="Set as default">
                    <mat-icon>star_outline</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="removePaymentMethod(method.id)" matTooltip="Remove">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </mat-list-item>
          </mat-list>

          <mat-divider *ngIf="paymentMethods.length > 0" class="divider"></mat-divider>

          <div class="add-payment-section">
            <h3>Add New Payment Method</h3>
            <form [formGroup]="paymentForm" (ngSubmit)="addPaymentMethod()">
              <mat-form-field appearance="outline">
                <mat-label>Card Number</mat-label>
                <input matInput formControlName="cardNumber" placeholder="1234 5678 9012 3456">
                <mat-error *ngIf="paymentForm.get('cardNumber')?.errors?.['required']">
                  Card number is required
                </mat-error>
                <mat-error *ngIf="paymentForm.get('cardNumber')?.errors?.['pattern']">
                  Invalid card number
                </mat-error>
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Expiration Month</mat-label>
                  <input matInput type="number" formControlName="expMonth" placeholder="MM" min="1" max="12">
                  <mat-error *ngIf="paymentForm.get('expMonth')?.errors?.['required']">
                    Expiration month is required
                  </mat-error>
                  <mat-error *ngIf="paymentForm.get('expMonth')?.errors?.['min'] || paymentForm.get('expMonth')?.errors?.['max']">
                    Month must be between 1 and 12
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Expiration Year</mat-label>
                  <input matInput type="number" formControlName="expYear" placeholder="YYYY" min="2023">
                  <mat-error *ngIf="paymentForm.get('expYear')?.errors?.['required']">
                    Expiration year is required
                  </mat-error>
                  <mat-error *ngIf="paymentForm.get('expYear')?.errors?.['min']">
                    Year must be {{ currentYear }} or later
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline">
                <mat-label>CVV</mat-label>
                <input matInput formControlName="cvv" placeholder="123">
                <mat-error *ngIf="paymentForm.get('cvv')?.errors?.['required']">
                  CVV is required
                </mat-error>
                <mat-error *ngIf="paymentForm.get('cvv')?.errors?.['pattern']">
                  CVV must be 3 or 4 digits
                </mat-error>
              </mat-form-field>

              <div class="button-container">
                <button mat-raised-button color="primary" type="submit" [disabled]="paymentForm.invalid || submitting">
                  {{ submitting ? 'Adding...' : 'Add Payment Method' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;c42cbcf459037746894c832f2624a383e492c112e33ca374e4a65e987f11fd30;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/payment-methods/payment-methods.component.ts */\n:host {\n  display: block;\n  margin: 20px;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n.no-methods {\n  text-align: center;\n  padding: 20px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.payment-method-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  padding: 8px 0;\n}\n.payment-method-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.expiry {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 0.9em;\n  margin-left: 8px;\n}\n.default-badge {\n  background-color: #4caf50;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 0.8em;\n  margin-left: 8px;\n}\n.divider {\n  margin: 20px 0;\n}\n.add-payment-section {\n  margin-top: 20px;\n}\nform {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  max-width: 500px;\n}\n.form-row {\n  display: flex;\n  gap: 16px;\n}\n.form-row mat-form-field {\n  flex: 1;\n}\n.button-container {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 16px;\n}\n/*# sourceMappingURL=payment-methods.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PaymentMethodsComponent, { className: "PaymentMethodsComponent", filePath: "src/app/features/dashboard/rider/payment-methods/payment-methods.component.ts", lineNumber: 219 });
})();

// src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts
var _c0 = ["cardElement"];
function RidePaymentComponent_mat_card_0_mat_divider_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "mat-divider", 13);
  }
}
function RidePaymentComponent_mat_card_0_div_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "p");
    \u0275\u0275text(2, "Loading payment form...");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "mat-spinner", 15);
    \u0275\u0275elementEnd();
  }
}
function RidePaymentComponent_mat_card_0_div_39_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.cardError);
  }
}
function RidePaymentComponent_mat_card_0_div_39_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24);
    \u0275\u0275element(1, "mat-spinner", 25);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Processing your payment...");
    \u0275\u0275elementEnd()();
  }
}
function RidePaymentComponent_mat_card_0_div_39_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 16)(1, "h3");
    \u0275\u0275text(2, "Payment Information");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p", 17);
    \u0275\u0275text(4, "Please enter your card details to complete the payment.");
    \u0275\u0275elementEnd();
    \u0275\u0275element(5, "div", 18, 0);
    \u0275\u0275template(7, RidePaymentComponent_mat_card_0_div_39_div_7_Template, 2, 1, "div", 19);
    \u0275\u0275elementStart(8, "div", 20)(9, "button", 21);
    \u0275\u0275listener("click", function RidePaymentComponent_mat_card_0_div_39_Template_button_click_9_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.processPayment());
    });
    \u0275\u0275elementStart(10, "mat-icon");
    \u0275\u0275text(11, "payment");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, RidePaymentComponent_mat_card_0_div_39_div_13_Template, 4, 0, "div", 22);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", ctx_r1.cardError);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.processing || !ctx_r1.cardComplete);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.processing ? "Processing..." : "Pay Now", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.processing);
  }
}
function RidePaymentComponent_mat_card_0_div_40_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24);
    \u0275\u0275element(1, "mat-spinner", 25);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Processing your request...");
    \u0275\u0275elementEnd()();
  }
}
function RidePaymentComponent_mat_card_0_div_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 20)(1, "button", 26);
    \u0275\u0275listener("click", function RidePaymentComponent_mat_card_0_div_40_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.requestRefund());
    });
    \u0275\u0275elementStart(2, "mat-icon");
    \u0275\u0275text(3, "money_off");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, RidePaymentComponent_mat_card_0_div_40_div_5_Template, 4, 0, "div", 22);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.processing);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.processing ? "Processing..." : "Request Refund", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.processing);
  }
}
function RidePaymentComponent_mat_card_0_div_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27)(1, "h3");
    \u0275\u0275text(2, "Payment Result");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 28);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngClass", ctx_r1.paymentResult.success ? "success-message" : "error-message");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.paymentResult.success ? "Payment successful!" : "Payment failed: " + (ctx_r1.paymentResult.error == null ? null : ctx_r1.paymentResult.error.message), " ");
  }
}
function RidePaymentComponent_mat_card_0_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-card")(1, "mat-card-header")(2, "mat-card-title");
    \u0275\u0275text(3, "Ride Payment");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "mat-card-content")(5, "div", 2)(6, "div", 3)(7, "span", 4);
    \u0275\u0275text(8, "Pickup:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 5);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "div", 3)(12, "span", 4);
    \u0275\u0275text(13, "Destination:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "span", 5);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(16, "div", 3)(17, "span", 4);
    \u0275\u0275text(18, "Date:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "span", 5);
    \u0275\u0275text(20);
    \u0275\u0275pipe(21, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 3)(23, "span", 4);
    \u0275\u0275text(24, "Status:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "span", 6);
    \u0275\u0275text(26);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "div", 3)(28, "span", 4);
    \u0275\u0275text(29, "Payment Status:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "span", 6);
    \u0275\u0275text(31);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(32, "div", 7)(33, "span", 4);
    \u0275\u0275text(34, "Amount:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(35, "span", 5);
    \u0275\u0275text(36);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(37, RidePaymentComponent_mat_card_0_mat_divider_37_Template, 1, 0, "mat-divider", 8)(38, RidePaymentComponent_mat_card_0_div_38_Template, 4, 0, "div", 9)(39, RidePaymentComponent_mat_card_0_div_39_Template, 14, 4, "div", 10)(40, RidePaymentComponent_mat_card_0_div_40_Template, 6, 3, "div", 11)(41, RidePaymentComponent_mat_card_0_div_41_Template, 5, 2, "div", 12);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r1.ride.pickup_location);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.ride.dropoff_location);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(21, 13, ctx_r1.ride.pickup_time, "medium"));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngClass", "status-" + ctx_r1.ride.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.ride.status);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngClass", "payment-" + (ctx_r1.ride.payment_status || "pending"));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.ride.payment_status || "pending", " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate("$" + ctx_r1.getDisplayAmount());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.canPay());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.canPay() && !ctx_r1.sdkLoaded);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.canPay() && ctx_r1.sdkLoaded);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.canRequestRefund());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.paymentResult);
  }
}
var RidePaymentComponent = class _RidePaymentComponent {
  paymentService;
  rideService;
  snackBar;
  authService;
  ride;
  cardElement;
  paymentCompleted = new EventEmitter();
  processing = false;
  stripe;
  card;
  sdkLoaded = false;
  cardError = "";
  cardComplete = false;
  paymentResult = null;
  constructor(paymentService, rideService, snackBar, authService) {
    this.paymentService = paymentService;
    this.rideService = rideService;
    this.snackBar = snackBar;
    this.authService = authService;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      if (this.ride && !this.ride.amount && !this.ride.fare) {
        this.estimateFare();
      }
      this.loadStripeScript();
      const stripe = yield this._loadStripe(environment.stripePublishableKey);
    });
  }
  ngAfterViewInit() {
    this.initializeCard();
  }
  _loadStripe(key) {
    return __async(this, null, function* () {
      const stripe = yield loadStripe(key);
      this.sdkLoaded = true;
    });
  }
  loadStripeScript() {
    if (window.Stripe) {
      this.initializeStripe();
      return;
    }
    const script = document.createElement("script");
    script.src = "https://js.stripe.com/v3/";
    script.async = true;
    script.onload = () => {
      this.initializeStripe();
    };
    document.body.appendChild(script);
  }
  initializeStripe() {
    if (!window.Stripe) {
      this.snackBar.open("Stripe SDK not available", "Close", { duration: 3e3 });
      return;
    }
    try {
      this.stripe = window.Stripe(environment.stripePublishableKey);
      setTimeout(() => this.initializeCard(), 100);
    } catch (error) {
      console.error("Error initializing Stripe:", error);
      this.snackBar.open("Error initializing Stripe payments. Check your credentials.", "Close", { duration: 5e3 });
    }
  }
  initializeCard() {
    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {
      setTimeout(() => this.initializeCard(), 100);
      return;
    }
    try {
      const elements = this.stripe.elements();
      this.card = elements.create("card", {
        style: {
          base: {
            iconColor: "#666EE8",
            color: "#31325F",
            fontWeight: 400,
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSize: "16px",
            "::placeholder": {
              color: "#CFD7E0"
            }
          }
        }
      });
      this.card.mount(this.cardElement.nativeElement);
      this.card.on("change", (event) => {
        this.cardError = event.error ? event.error.message : "";
        this.cardComplete = event.complete;
      });
      this.sdkLoaded = true;
    } catch (error) {
      console.error("Error initializing Stripe card:", error);
      this.snackBar.open("Error initializing Stripe card form", "Close", { duration: 5e3 });
    }
  }
  calculateAmount() {
    return 15;
  }
  estimateFare() {
    return __async(this, null, function* () {
      if (!this.ride)
        return;
      try {
        const { fare } = yield this.paymentService.estimateFare(this.ride.pickup_location, this.ride.dropoff_location);
        yield this.rideService.updateRide(this.ride.id, { fare });
      } catch (error) {
        console.error("Error estimating fare:", error);
      }
    });
  }
  canPay() {
    if (!this.ride)
      return false;
    return this.ride.status === "completed" && (!this.ride.payment_status || this.ride.payment_status === "pending" || this.ride.payment_status === "failed");
  }
  canRequestRefund() {
    if (!this.ride)
      return false;
    return this.ride.payment_status === "paid" || this.ride.payment_status === "completed";
  }
  processPayment() {
    return __async(this, null, function* () {
      if (!this.ride || !this.canPay() || !this.stripe || !this.card)
        return;
      this.processing = true;
      this.paymentResult = null;
      try {
        const amount = this.ride.amount || this.ride.fare || this.calculateAmount();
        const { paymentMethod, error: paymentMethodError } = yield this.stripe.createPaymentMethod({
          type: "card",
          card: this.card
        });
        if (paymentMethodError) {
          throw paymentMethodError;
        }
        if (paymentMethodError) {
          throw paymentMethodError;
        }
        let payment = {
          amount: amount * 100,
          // Stripe uses cents
          currency: "usd",
          description: "Customer pamyment for ride",
          payment_method: paymentMethod.id
        };
        console.log(payment);
        const { data, error } = yield this.authService.supabase.functions.invoke("stripe", {
          body: payment
        });
        if (error) {
          console.error("Error creating payment intent:", error);
          throw new Error(`Failed to create payment intent: ${error.message}`);
        }
        console.log("Payment intent created:", data);
        if (!data || !data.client_secret) {
          throw new Error("No client secret returned from payment intent creation");
        }
        const clientSecret = data.client_secret;
        const { error: confirmError, paymentIntent } = yield this.stripe.confirmCardPayment(clientSecret, {
          payment_method: paymentMethod.id
        });
        if (confirmError) {
          throw confirmError;
        }
        this.paymentResult = {
          success: true,
          paymentIntent
        };
        yield this.rideService.updateRide(this.ride.id, {
          payment_status: "paid",
          payment_id: paymentIntent.id,
          amount
        });
        this.snackBar.open("Payment processed successfully!", "Close", { duration: 3e3 });
        this.paymentCompleted.emit();
      } catch (error) {
        console.error("Error processing payment:", error);
        this.paymentResult = {
          success: false,
          error: {
            message: error.message || "An unknown error occurred"
          }
        };
        this.snackBar.open(`Payment error: ${error.message}`, "Close", { duration: 5e3 });
      } finally {
        this.processing = false;
      }
    });
  }
  getDisplayAmount() {
    if (!this.ride)
      return "0";
    return (this.ride.amount || this.ride.fare || this.calculateAmount()).toString();
  }
  requestRefund() {
    return __async(this, null, function* () {
      if (!this.ride || !this.canRequestRefund())
        return;
      this.processing = true;
      this.paymentResult = null;
      try {
        const success = yield this.paymentService.processRefund(this.ride.id);
        if (success) {
          this.paymentResult = {
            success: true,
            refund: true
          };
          this.snackBar.open("Refund processed successfully!", "Close", { duration: 3e3 });
          this.paymentCompleted.emit();
        } else {
          this.paymentResult = {
            success: false,
            refund: true,
            error: {
              message: "Failed to process refund"
            }
          };
          this.snackBar.open("Refund request failed. Please try again.", "Close", { duration: 3e3 });
        }
      } catch (error) {
        console.error("Error processing refund:", error);
        this.paymentResult = {
          success: false,
          refund: true,
          error: {
            message: error.message || "An unknown error occurred"
          }
        };
        this.snackBar.open(`Refund error: ${error.message}`, "Close", { duration: 5e3 });
      } finally {
        this.processing = false;
      }
    });
  }
  static \u0275fac = function RidePaymentComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RidePaymentComponent)(\u0275\u0275directiveInject(PaymentService), \u0275\u0275directiveInject(RideService), \u0275\u0275directiveInject(MatSnackBar), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RidePaymentComponent, selectors: [["app-ride-payment"]], viewQuery: function RidePaymentComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.cardElement = _t.first);
    }
  }, inputs: { ride: "ride" }, outputs: { paymentCompleted: "paymentCompleted" }, decls: 1, vars: 1, consts: [["cardElement", ""], [4, "ngIf"], [1, "payment-details"], [1, "detail-row"], [1, "label"], [1, "value"], [1, "value", "status-badge", 3, "ngClass"], [1, "detail-row", "amount"], ["class", "section-divider", 4, "ngIf"], ["class", "sdk-status", 4, "ngIf"], ["class", "stripe-payment-form", 4, "ngIf"], ["class", "payment-actions", 4, "ngIf"], ["class", "payment-result", 4, "ngIf"], [1, "section-divider"], [1, "sdk-status"], ["diameter", "30"], [1, "stripe-payment-form"], [1, "payment-instruction"], [1, "card-element"], ["class", "card-errors", 4, "ngIf"], [1, "payment-actions"], ["mat-raised-button", "", "color", "primary", 3, "click", "disabled"], ["class", "processing-indicator", 4, "ngIf"], [1, "card-errors"], [1, "processing-indicator"], ["diameter", "24"], ["mat-raised-button", "", "color", "warn", 3, "click", "disabled"], [1, "payment-result"], [3, "ngClass"]], template: function RidePaymentComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275template(0, RidePaymentComponent_mat_card_0_Template, 42, 16, "mat-card", 1);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.ride);
    }
  }, dependencies: [
    CommonModule,
    NgClass,
    NgIf,
    DatePipe,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatButtonModule,
    MatButton,
    MatIconModule,
    MatIcon,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatDialogModule,
    MatSnackBarModule,
    MatDividerModule,
    MatDivider
  ], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  margin: 20px;\n}\n.payment-details[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n.detail-row[_ngcontent-%COMP%] {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: center;\n}\n.label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  width: 120px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.value[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.amount[_ngcontent-%COMP%] {\n  font-size: 1.2em;\n  font-weight: 500;\n  margin-top: 16px;\n}\n.amount[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\n  color: #3f51b5;\n}\n.status-badge[_ngcontent-%COMP%] {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  text-transform: capitalize;\n  font-size: 0.9em;\n}\n.status-requested[_ngcontent-%COMP%] {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.status-assigned[_ngcontent-%COMP%] {\n  background-color: #2196f3;\n  color: white;\n}\n.status-in-progress[_ngcontent-%COMP%] {\n  background-color: #ff9800;\n  color: white;\n}\n.status-completed[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n}\n.status-canceled[_ngcontent-%COMP%] {\n  background-color: #f44336;\n  color: white;\n}\n.payment-pending[_ngcontent-%COMP%] {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.payment-paid[_ngcontent-%COMP%], \n.payment-completed[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n}\n.payment-failed[_ngcontent-%COMP%] {\n  background-color: #f44336;\n  color: white;\n}\n.payment-refunded[_ngcontent-%COMP%] {\n  background-color: #9e9e9e;\n  color: white;\n}\n.payment-actions[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  align-items: flex-start;\n  margin-top: 16px;\n}\n.processing-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 8px;\n}\n.section-divider[_ngcontent-%COMP%] {\n  margin: 24px 0;\n}\n.stripe-payment-form[_ngcontent-%COMP%] {\n  margin-top: 24px;\n}\n.payment-instruction[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.card-element[_ngcontent-%COMP%] {\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  padding: 12px;\n  background-color: white;\n  margin-bottom: 16px;\n}\n.card-errors[_ngcontent-%COMP%] {\n  color: #f44336;\n  font-size: 0.9em;\n  margin-bottom: 16px;\n}\n.sdk-status[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n  margin: 24px 0;\n}\n.payment-result[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #f5f5f5;\n}\n.success-message[_ngcontent-%COMP%] {\n  color: #4caf50;\n  font-weight: 500;\n}\n.error-message[_ngcontent-%COMP%] {\n  color: #f44336;\n  font-weight: 500;\n}\n/*# sourceMappingURL=ride-payment.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RidePaymentComponent, [{
    type: Component,
    args: [{ selector: "app-ride-payment", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatProgressSpinnerModule,
      MatDialogModule,
      MatSnackBarModule,
      MatDividerModule
    ], template: `
    <mat-card *ngIf="ride">
      <mat-card-header>
        <mat-card-title>Ride Payment</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="payment-details">
          <div class="detail-row">
            <span class="label">Pickup:</span>
            <span class="value">{{ ride.pickup_location }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Destination:</span>
            <span class="value">{{ ride.dropoff_location }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Date:</span>
            <span class="value">{{ ride.pickup_time | date:'medium' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Status:</span>
            <span class="value status-badge" [ngClass]="'status-' + ride.status">{{ ride.status }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Payment Status:</span>
            <span class="value status-badge" [ngClass]="'payment-' + (ride.payment_status || 'pending')">
              {{ ride.payment_status || 'pending' }}
            </span>
          </div>
          <div class="detail-row amount">
            <span class="label">Amount:</span>
            <span class="value">{{ '$' + getDisplayAmount() }}</span>
          </div>
        </div>

        <mat-divider *ngIf="canPay()" class="section-divider"></mat-divider>

        <div *ngIf="canPay() && !sdkLoaded" class="sdk-status">
          <p>Loading payment form...</p>
          <mat-spinner diameter="30"></mat-spinner>
        </div>

        <div *ngIf="canPay() && sdkLoaded" class="stripe-payment-form">
          <h3>Payment Information</h3>
          <p class="payment-instruction">Please enter your card details to complete the payment.</p>

          <div #cardElement class="card-element"></div>
          <div class="card-errors" *ngIf="cardError">{{ cardError }}</div>

          <div class="payment-actions">
            <button mat-raised-button color="primary"
                    [disabled]="processing || !cardComplete"
                    (click)="processPayment()">
              <mat-icon>payment</mat-icon>
              {{ processing ? 'Processing...' : 'Pay Now' }}
            </button>

            <div *ngIf="processing" class="processing-indicator">
              <mat-spinner diameter="24"></mat-spinner>
              <span>Processing your payment...</span>
            </div>
          </div>
        </div>

        <div class="payment-actions" *ngIf="canRequestRefund()">
          <button mat-raised-button color="warn"
                  [disabled]="processing"
                  (click)="requestRefund()">
            <mat-icon>money_off</mat-icon>
            {{ processing ? 'Processing...' : 'Request Refund' }}
          </button>

          <div *ngIf="processing" class="processing-indicator">
            <mat-spinner diameter="24"></mat-spinner>
            <span>Processing your request...</span>
          </div>
        </div>

        <div class="payment-result" *ngIf="paymentResult">
          <h3>Payment Result</h3>
          <div [ngClass]="paymentResult.success ? 'success-message' : 'error-message'">
            {{ paymentResult.success ? 'Payment successful!' : 'Payment failed: ' + paymentResult.error?.message }}
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `, styles: ["/* angular:styles/component:scss;585630d1f347d5230f39b2b1141dd830fe87151c178ef1e51c5060224ff77451;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts */\n:host {\n  display: block;\n  margin: 20px;\n}\n.payment-details {\n  margin-bottom: 20px;\n}\n.detail-row {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: center;\n}\n.label {\n  font-weight: 500;\n  width: 120px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.value {\n  flex: 1;\n}\n.amount {\n  font-size: 1.2em;\n  font-weight: 500;\n  margin-top: 16px;\n}\n.amount .value {\n  color: #3f51b5;\n}\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  text-transform: capitalize;\n  font-size: 0.9em;\n}\n.status-requested {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.status-assigned {\n  background-color: #2196f3;\n  color: white;\n}\n.status-in-progress {\n  background-color: #ff9800;\n  color: white;\n}\n.status-completed {\n  background-color: #4caf50;\n  color: white;\n}\n.status-canceled {\n  background-color: #f44336;\n  color: white;\n}\n.payment-pending {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.payment-paid,\n.payment-completed {\n  background-color: #4caf50;\n  color: white;\n}\n.payment-failed {\n  background-color: #f44336;\n  color: white;\n}\n.payment-refunded {\n  background-color: #9e9e9e;\n  color: white;\n}\n.payment-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  align-items: flex-start;\n  margin-top: 16px;\n}\n.processing-indicator {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-top: 8px;\n}\n.section-divider {\n  margin: 24px 0;\n}\n.stripe-payment-form {\n  margin-top: 24px;\n}\n.payment-instruction {\n  margin-bottom: 16px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.card-element {\n  border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  padding: 12px;\n  background-color: white;\n  margin-bottom: 16px;\n}\n.card-errors {\n  color: #f44336;\n  font-size: 0.9em;\n  margin-bottom: 16px;\n}\n.sdk-status {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n  margin: 24px 0;\n}\n.payment-result {\n  margin-top: 24px;\n  padding: 16px;\n  border-radius: 4px;\n  background-color: #f5f5f5;\n}\n.success-message {\n  color: #4caf50;\n  font-weight: 500;\n}\n.error-message {\n  color: #f44336;\n  font-weight: 500;\n}\n/*# sourceMappingURL=ride-payment.component.css.map */\n"] }]
  }], () => [{ type: PaymentService }, { type: RideService }, { type: MatSnackBar }, { type: AuthService }], { ride: [{
    type: Input
  }], cardElement: [{
    type: ViewChild,
    args: ["cardElement"]
  }], paymentCompleted: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RidePaymentComponent, { className: "RidePaymentComponent", filePath: "src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts", lineNumber: 280 });
})();

// src/app/features/dashboard/rider/rider.component.ts
function RiderComponent_th_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Pickup");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r1 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r1.pickup_location);
  }
}
function RiderComponent_th_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Dropoff");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r2.dropoff_location);
  }
}
function RiderComponent_th_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Time");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r3 = ctx.$implicit;
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r3.formatDate(ride_r3.pickup_time));
  }
}
function RiderComponent_th_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Status");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23)(1, "mat-chip-listbox")(2, "mat-chip");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ride_r5 = ctx.$implicit;
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r3.getStatusClass(ride_r5.status));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r3.formatStatus(ride_r5.status), " ");
  }
}
function RiderComponent_th_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Payment");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_22_mat_chip_listbox_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-chip-listbox")(1, "mat-chip");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ride_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275classMap("payment-status-" + ride_r6.payment_status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ride_r6.payment_status, " ");
  }
}
function RiderComponent_td_22_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "-");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275template(1, RiderComponent_td_22_mat_chip_listbox_1_Template, 3, 3, "mat-chip-listbox", 24)(2, RiderComponent_td_22_span_2_Template, 2, 0, "span", 24);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r6 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r6.payment_status);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ride_r6.payment_status);
  }
}
function RiderComponent_th_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Fare");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r7 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ride_r7.amount || ride_r7.fare ? "$" + (ride_r7.amount || ride_r7.fare).toFixed(2) : "-", " ");
  }
}
function RiderComponent_th_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 22);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_td_28_button_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 28);
    \u0275\u0275listener("click", function RiderComponent_td_28_button_1_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r9);
      const ride_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.cancelRide(ride_r10.id));
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "cancel");
    \u0275\u0275elementEnd()();
  }
}
function RiderComponent_td_28_button_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 29);
    \u0275\u0275listener("click", function RiderComponent_td_28_button_2_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r11);
      const ride_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.viewPayment(ride_r10));
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "payment");
    \u0275\u0275elementEnd()();
  }
}
function RiderComponent_td_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 23);
    \u0275\u0275template(1, RiderComponent_td_28_button_1_Template, 3, 0, "button", 25)(2, RiderComponent_td_28_button_2_Template, 3, 0, "button", 26);
    \u0275\u0275elementStart(3, "button", 27);
    \u0275\u0275listener("click", function RiderComponent_td_28_Template_button_click_3_listener() {
      const ride_r10 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.viewRideDetails(ride_r10.id));
    });
    \u0275\u0275elementStart(4, "mat-icon");
    \u0275\u0275text(5, "visibility");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ride_r10 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r10.status === "requested");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r10.status === "completed");
  }
}
function RiderComponent_tr_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 30);
  }
}
function RiderComponent_tr_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 31);
  }
}
function RiderComponent_mat_expansion_panel_33_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 28);
    \u0275\u0275listener("click", function RiderComponent_mat_expansion_panel_33_button_9_Template_button_click_0_listener($event) {
      \u0275\u0275restoreView(_r13);
      const ride_r14 = \u0275\u0275nextContext().$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      ctx_r3.cancelRide(ride_r14.id);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "cancel");
    \u0275\u0275elementEnd()();
  }
}
function RiderComponent_mat_expansion_panel_33_button_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 29);
    \u0275\u0275listener("click", function RiderComponent_mat_expansion_panel_33_button_10_Template_button_click_0_listener($event) {
      \u0275\u0275restoreView(_r15);
      const ride_r14 = \u0275\u0275nextContext().$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      ctx_r3.viewPayment(ride_r14);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "payment");
    \u0275\u0275elementEnd()();
  }
}
function RiderComponent_mat_expansion_panel_33_span_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r14 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r14.payment_status);
  }
}
function RiderComponent_mat_expansion_panel_33_span_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "-");
    \u0275\u0275elementEnd();
  }
}
function RiderComponent_mat_expansion_panel_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-expansion-panel")(1, "mat-expansion-panel-header")(2, "mat-panel-title");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-panel-description")(5, "div", 32)(6, "span", 33);
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, RiderComponent_mat_expansion_panel_33_button_9_Template, 3, 0, "button", 25)(10, RiderComponent_mat_expansion_panel_33_button_10_Template, 3, 0, "button", 26);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(11, "div", 34)(12, "p")(13, "strong");
    \u0275\u0275text(14, "To:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "p")(17, "strong");
    \u0275\u0275text(18, "Time:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(19);
    \u0275\u0275pipe(20, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "p")(22, "strong");
    \u0275\u0275text(23, "Status:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(24);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "p")(26, "strong");
    \u0275\u0275text(27, "Payment:");
    \u0275\u0275elementEnd();
    \u0275\u0275template(28, RiderComponent_mat_expansion_panel_33_span_28_Template, 2, 1, "span", 24)(29, RiderComponent_mat_expansion_panel_33_span_29_Template, 2, 0, "span", 24);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "p")(31, "strong");
    \u0275\u0275text(32, "Fare:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(33);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(34, "mat-action-row")(35, "button", 27);
    \u0275\u0275listener("click", function RiderComponent_mat_expansion_panel_33_Template_button_click_35_listener() {
      const ride_r14 = \u0275\u0275restoreView(_r12).$implicit;
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.viewRideDetails(ride_r14.id));
    });
    \u0275\u0275elementStart(36, "mat-icon");
    \u0275\u0275text(37, "visibility");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ride_r14 = ctx.$implicit;
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ride_r14.pickup_location, " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(8, 10, ride_r14.pickup_time, "shortTime"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ride_r14.status === "requested");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r14.status === "completed");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ride_r14.dropoff_location, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(20, 13, ride_r14.pickup_time, "short"), "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r3.formatStatus(ride_r14.status), "");
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ride_r14.payment_status);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ride_r14.payment_status);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ride_r14.amount || ride_r14.fare ? "$" + (ride_r14.amount || ride_r14.fare).toFixed(2) : "-", "");
  }
}
function RiderComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 35)(1, "app-ride-payment", 36);
    \u0275\u0275listener("paymentCompleted", function RiderComponent_div_34_Template_app_ride_payment_paymentCompleted_1_listener() {
      \u0275\u0275restoreView(_r16);
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.closePayment());
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "button", 37);
    \u0275\u0275listener("click", function RiderComponent_div_34_Template_button_click_2_listener() {
      \u0275\u0275restoreView(_r16);
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.closePayment());
    });
    \u0275\u0275elementStart(3, "mat-icon");
    \u0275\u0275text(4, "close");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ride", ctx_r3.selectedRide);
  }
}
function RiderComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 38)(1, "app-ride-detail", 39);
    \u0275\u0275listener("paymentRequested", function RiderComponent_div_35_Template_app_ride_detail_paymentRequested_1_listener($event) {
      \u0275\u0275restoreView(_r17);
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.viewPayment($event));
    })("rideUpdated", function RiderComponent_div_35_Template_app_ride_detail_rideUpdated_1_listener($event) {
      \u0275\u0275restoreView(_r17);
      const ctx_r3 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r3.onRideUpdated($event));
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("rideId", ctx_r3.selectedRideId)("onClose", ctx_r3.closeRideDetails.bind(ctx_r3));
  }
}
var RiderComponent = class _RiderComponent {
  authService;
  rideService;
  messageService;
  paymentService;
  router;
  dialog;
  snackBar;
  rides = [];
  displayedColumns = ["pickup_location", "dropoff_location", "pickup_time", "status", "payment_status", "fare", "actions"];
  currentUser = null;
  selectedRide = null;
  selectedRideId = null;
  ridesSubscription = null;
  constructor(authService, rideService, messageService, paymentService, router, dialog, snackBar) {
    this.authService = authService;
    this.rideService = rideService;
    this.messageService = messageService;
    this.paymentService = paymentService;
    this.router = router;
    this.dialog = dialog;
    this.snackBar = snackBar;
  }
  ngOnInit() {
    return __async(this, null, function* () {
      this.currentUser = yield this.authService.getCurrentUser();
      if (this.currentUser) {
        yield this.loadUserRides();
        this.ridesSubscription = this.rideService.rides$.subscribe((rides) => {
          if (this.currentUser) {
            this.rides = rides.filter((ride) => ride.rider_id === this.currentUser.id);
          }
        });
      }
    });
  }
  ngOnDestroy() {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }
  loadUserRides() {
    return __async(this, null, function* () {
      if (this.currentUser) {
        try {
          this.rides = yield this.rideService.getUserRides(this.currentUser.id);
        } catch (error) {
          console.error("Error loading user rides:", error);
          this.snackBar.open("Failed to load rides", "Close", { duration: 3e3 });
        }
      }
    });
  }
  cancelRide(rideId) {
    return __async(this, null, function* () {
      try {
        const success = yield this.rideService.cancelRide(rideId);
        if (success) {
          this.snackBar.open("Ride canceled successfully", "Close", { duration: 3e3 });
          yield this.loadUserRides();
        } else {
          throw new Error("Failed to cancel ride");
        }
      } catch (error) {
        console.error("Error canceling ride:", error);
        this.snackBar.open("Failed to cancel ride", "Close", { duration: 3e3 });
      }
    });
  }
  formatDate(dateString) {
    return new Date(dateString).toLocaleString();
  }
  formatStatus(status) {
    return status.split("-").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" ");
  }
  getStatusClass(status) {
    return `status-chip status-${status}`;
  }
  openChat(rideId) {
    return __async(this, null, function* () {
      try {
        const thread = yield this.messageService.getOrCreateThreadForRide(rideId);
        this.router.navigate(["/dashboard", "rider", "messages", thread.id]);
      } catch (error) {
        console.error("Error opening chat:", error);
        this.snackBar.open("Failed to open chat", "Close", { duration: 3e3 });
      }
    });
  }
  viewPayment(ride) {
    this.selectedRide = ride;
  }
  closePayment() {
    this.selectedRide = null;
    this.loadUserRides();
  }
  viewRideDetails(rideId) {
    this.selectedRideId = rideId;
  }
  closeRideDetails() {
    this.selectedRideId = null;
    this.loadUserRides();
  }
  onRideUpdated(_ride) {
    this.loadUserRides();
  }
  trackByRideId(_index, item) {
    return item.id;
  }
  static \u0275fac = function RiderComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RiderComponent)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(RideService), \u0275\u0275directiveInject(MessageService), \u0275\u0275directiveInject(PaymentService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(MatDialog), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RiderComponent, selectors: [["app-rider"]], decls: 36, vars: 7, consts: [[1, "dashboard-container"], ["label", "Request Ride"], ["label", "Ride History"], [1, "table-container"], [1, "desktop-view"], ["mat-table", "", 1, "ride-table", 3, "dataSource"], ["matColumnDef", "pickup_location"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "dropoff_location"], ["matColumnDef", "pickup_time"], ["matColumnDef", "status"], ["matColumnDef", "payment_status"], ["matColumnDef", "fare"], ["matColumnDef", "actions"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], [1, "mobile-view"], ["multi", ""], [4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "payment-overlay", 4, "ngIf"], ["class", "ride-detail-overlay", 4, "ngIf"], ["mat-header-cell", ""], ["mat-cell", ""], [4, "ngIf"], ["mat-icon-button", "", "color", "warn", "matTooltip", "Cancel Ride", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "accent", "matTooltip", "View Payment", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "primary", "matTooltip", "View Details", 3, "click"], ["mat-icon-button", "", "color", "warn", "matTooltip", "Cancel Ride", 3, "click"], ["mat-icon-button", "", "color", "accent", "matTooltip", "View Payment", 3, "click"], ["mat-header-row", ""], ["mat-row", ""], [1, "ride-actions-header"], [1, "ride-time"], [1, "ride-details"], [1, "payment-overlay"], [3, "paymentCompleted", "ride"], ["mat-icon-button", "", 1, "close-payment-button", 3, "click"], [1, "ride-detail-overlay"], [3, "paymentRequested", "rideUpdated", "rideId", "onClose"]], template: function RiderComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-tab-group")(2, "mat-tab", 1);
      \u0275\u0275element(3, "app-ride-request");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "mat-tab", 2)(5, "div", 3)(6, "div", 4)(7, "table", 5);
      \u0275\u0275elementContainerStart(8, 6);
      \u0275\u0275template(9, RiderComponent_th_9_Template, 2, 0, "th", 7)(10, RiderComponent_td_10_Template, 2, 1, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(11, 9);
      \u0275\u0275template(12, RiderComponent_th_12_Template, 2, 0, "th", 7)(13, RiderComponent_td_13_Template, 2, 1, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(14, 10);
      \u0275\u0275template(15, RiderComponent_th_15_Template, 2, 0, "th", 7)(16, RiderComponent_td_16_Template, 2, 1, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(17, 11);
      \u0275\u0275template(18, RiderComponent_th_18_Template, 2, 0, "th", 7)(19, RiderComponent_td_19_Template, 4, 3, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(20, 12);
      \u0275\u0275template(21, RiderComponent_th_21_Template, 2, 0, "th", 7)(22, RiderComponent_td_22_Template, 3, 2, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(23, 13);
      \u0275\u0275template(24, RiderComponent_th_24_Template, 2, 0, "th", 7)(25, RiderComponent_td_25_Template, 2, 1, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275elementContainerStart(26, 14);
      \u0275\u0275template(27, RiderComponent_th_27_Template, 2, 0, "th", 7)(28, RiderComponent_td_28_Template, 6, 2, "td", 8);
      \u0275\u0275elementContainerEnd();
      \u0275\u0275template(29, RiderComponent_tr_29_Template, 1, 0, "tr", 15)(30, RiderComponent_tr_30_Template, 1, 0, "tr", 16);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(31, "div", 17)(32, "mat-accordion", 18);
      \u0275\u0275template(33, RiderComponent_mat_expansion_panel_33_Template, 38, 16, "mat-expansion-panel", 19);
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275template(34, RiderComponent_div_34_Template, 5, 1, "div", 20)(35, RiderComponent_div_35_Template, 2, 2, "div", 21);
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("dataSource", ctx.rides);
      \u0275\u0275advance(22);
      \u0275\u0275property("matHeaderRowDef", ctx.displayedColumns);
      \u0275\u0275advance();
      \u0275\u0275property("matRowDefColumns", ctx.displayedColumns);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngForOf", ctx.rides)("ngForTrackBy", ctx.trackByRideId);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedRide);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedRideId);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    DatePipe,
    MatCardModule,
    MatTabsModule,
    MatTab,
    MatTabGroup,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatChipsModule,
    MatChip,
    MatChipListbox,
    MatButtonModule,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatSnackBarModule,
    MatTooltipModule,
    MatTooltip,
    MatDialogModule,
    MatExpansionModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelActionRow,
    MatExpansionPanelHeader,
    MatExpansionPanelTitle,
    MatExpansionPanelDescription,
    RideRequestComponent,
    RidePaymentComponent,
    RideDetailComponent
  ], styles: ["\n\n.dashboard-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: #f5f5f5;\n}\n.table-container[_ngcontent-%COMP%] {\n  margin: 20px;\n}\n.ride-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.status-chip[_ngcontent-%COMP%] {\n  border-radius: 16px;\n  padding: 4px 12px;\n  color: white;\n  font-weight: 500;\n}\n.status-requested[_ngcontent-%COMP%] {\n  background-color: #ff9800;\n}\n.status-assigned[_ngcontent-%COMP%] {\n  background-color: #2196f3;\n}\n.status-in-progress[_ngcontent-%COMP%] {\n  background-color: #673ab7;\n}\n.status-completed[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n}\n.status-canceled[_ngcontent-%COMP%] {\n  background-color: #f44336;\n}\n.payment-status-pending[_ngcontent-%COMP%] {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.payment-status-paid[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n}\n.payment-status-failed[_ngcontent-%COMP%] {\n  background-color: #f44336;\n  color: white;\n}\n.payment-status-refunded[_ngcontent-%COMP%] {\n  background-color: #9e9e9e;\n  color: white;\n}\n.payment-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.close-payment-button[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background-color: white;\n}\n.ride-detail-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.mobile-view[_ngcontent-%COMP%] {\n  display: none;\n}\n.desktop-view[_ngcontent-%COMP%] {\n  display: block;\n}\n.ride-actions-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.ride-time[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n@media (max-width: 600px) {\n  .desktop-view[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .mobile-view[_ngcontent-%COMP%] {\n    display: block;\n  }\n  .dashboard-container[_ngcontent-%COMP%] {\n    padding: 0;\n  }\n  .table-container[_ngcontent-%COMP%] {\n    margin: 0;\n  }\n  .mat-tab-body-content[_ngcontent-%COMP%] {\n    overflow: hidden;\n  }\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\n  font-size: 14px;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-panel-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-panel-description[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n  align-items: center;\n}\n.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%] {\n  padding: 0 24px 16px;\n  font-size: 14px;\n}\n.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n  padding: 8px 12px 8px 24px;\n}\n/*# sourceMappingURL=rider.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RiderComponent, [{
    type: Component,
    args: [{ selector: "app-rider", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatTabsModule,
      MatTableModule,
      MatChipsModule,
      MatButtonModule,
      MatIconModule,
      MatSnackBarModule,
      MatTooltipModule,
      MatDialogModule,
      MatExpansionModule,
      RideRequestComponent,
      PaymentMethodsComponent,
      RidePaymentComponent,
      RideDetailComponent
    ], template: `
    <div class="dashboard-container">
      <mat-tab-group>
        <mat-tab label="Request Ride">
          <app-ride-request></app-ride-request>
        </mat-tab>

        <!-- <mat-tab label="Payment Methods">
          <app-payment-methods></app-payment-methods>
        </mat-tab> -->

        <mat-tab label="Ride History">
          <div class="table-container">
            <!-- Desktop View -->
            <div class="desktop-view">
              <table mat-table [dataSource]="rides" class="ride-table">
                <ng-container matColumnDef="pickup_location">
                  <th mat-header-cell *matHeaderCellDef>Pickup</th>
                  <td mat-cell *matCellDef="let ride">{{ride.pickup_location}}</td>
                </ng-container>

                <ng-container matColumnDef="dropoff_location">
                  <th mat-header-cell *matHeaderCellDef>Dropoff</th>
                  <td mat-cell *matCellDef="let ride">{{ride.dropoff_location}}</td>
                </ng-container>

                <ng-container matColumnDef="pickup_time">
                  <th mat-header-cell *matHeaderCellDef>Time</th>
                  <td mat-cell *matCellDef="let ride">{{formatDate(ride.pickup_time)}}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let ride">
                    <mat-chip-listbox>
                      <mat-chip [class]="getStatusClass(ride.status)">
                        {{formatStatus(ride.status)}}
                      </mat-chip>
                    </mat-chip-listbox>
                  </td>
                </ng-container>

                <ng-container matColumnDef="payment_status">
                  <th mat-header-cell *matHeaderCellDef>Payment</th>
                  <td mat-cell *matCellDef="let ride">
                    <mat-chip-listbox *ngIf="ride.payment_status">
                      <mat-chip [class]="'payment-status-' + ride.payment_status">
                        {{ride.payment_status}}
                      </mat-chip>
                    </mat-chip-listbox>
                    <span *ngIf="!ride.payment_status">-</span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="fare">
                  <th mat-header-cell *matHeaderCellDef>Fare</th>
                  <td mat-cell *matCellDef="let ride">
                    {{(ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare).toFixed(2) : '-'}}
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let ride">
                    <button mat-icon-button color="warn" *ngIf="ride.status === 'requested'"
                            (click)="cancelRide(ride.id)" matTooltip="Cancel Ride">
                      <mat-icon>cancel</mat-icon>
                    </button>
                    <!-- <button mat-icon-button color="primary" *ngIf="ride.driver_id"
                            (click)="openChat(ride.id)" matTooltip="Message Driver">
                      <mat-icon>chat</mat-icon>
                    </button> -->
                    <button mat-icon-button color="accent" *ngIf="ride.status === 'completed'"
                            (click)="viewPayment(ride)" matTooltip="View Payment">
                      <mat-icon>payment</mat-icon>
                    </button>
                    <button mat-icon-button color="primary"
                            (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>

            <!-- Mobile View -->
            <div class="mobile-view">
              <mat-accordion multi>
                <mat-expansion-panel *ngFor="let ride of rides; trackBy: trackByRideId">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      {{ ride.pickup_location }}
                    </mat-panel-title>
                    <mat-panel-description>
                      <div class="ride-actions-header">
                        <span class="ride-time">{{ ride.pickup_time | date:'shortTime' }}</span>
                        <button mat-icon-button color="warn" *ngIf="ride.status === 'requested'"
                                (click)="cancelRide(ride.id); $event.stopPropagation()" matTooltip="Cancel Ride">
                          <mat-icon>cancel</mat-icon>
                        </button>
                        <button mat-icon-button color="accent" *ngIf="ride.status === 'completed'"
                                (click)="viewPayment(ride); $event.stopPropagation()" matTooltip="View Payment">
                          <mat-icon>payment</mat-icon>
                        </button>
                      </div>
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  <div class="ride-details">
                    <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                    <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>
                    <p><strong>Status:</strong> {{ formatStatus(ride.status) }}</p>
                    <p><strong>Payment:</strong>
                      <span *ngIf="ride.payment_status">{{ ride.payment_status }}</span>
                      <span *ngIf="!ride.payment_status">-</span>
                    </p>
                    <p><strong>Fare:</strong> {{ (ride.amount || ride.fare) ? '$' + (ride.amount || ride.fare)!.toFixed(2) : '-' }}</p>
                  </div>
                  <mat-action-row>
                    <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                  </mat-action-row>
                </mat-expansion-panel>
              </mat-accordion>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>

    <div *ngIf="selectedRide" class="payment-overlay">
      <app-ride-payment
        [ride]="selectedRide"
        (paymentCompleted)="closePayment()">
      </app-ride-payment>
      <button mat-icon-button class="close-payment-button" (click)="closePayment()">
        <mat-icon>close</mat-icon>
      </button>
    </div>

    <div *ngIf="selectedRideId" class="ride-detail-overlay">
      <app-ride-detail
        [rideId]="selectedRideId"
        [onClose]="closeRideDetails.bind(this)"
        (paymentRequested)="viewPayment($event)"
        (rideUpdated)="onRideUpdated($event)">
      </app-ride-detail>
    </div>
  `, styles: ["/* angular:styles/component:scss;d6d8978c381d420c36f7a7f38d29257c945376f9cb3183931611c0994ab686a2;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/rider.component.ts */\n.dashboard-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: #f5f5f5;\n}\n.table-container {\n  margin: 20px;\n}\n.ride-table {\n  width: 100%;\n}\n.status-chip {\n  border-radius: 16px;\n  padding: 4px 12px;\n  color: white;\n  font-weight: 500;\n}\n.status-requested {\n  background-color: #ff9800;\n}\n.status-assigned {\n  background-color: #2196f3;\n}\n.status-in-progress {\n  background-color: #673ab7;\n}\n.status-completed {\n  background-color: #4caf50;\n}\n.status-canceled {\n  background-color: #f44336;\n}\n.payment-status-pending {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.payment-status-paid {\n  background-color: #4caf50;\n  color: white;\n}\n.payment-status-failed {\n  background-color: #f44336;\n  color: white;\n}\n.payment-status-refunded {\n  background-color: #9e9e9e;\n  color: white;\n}\n.payment-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.close-payment-button {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n  background-color: white;\n}\n.ride-detail-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.mobile-view {\n  display: none;\n}\n.desktop-view {\n  display: block;\n}\n.ride-actions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.ride-time {\n  font-size: 12px;\n  color: #666;\n}\n@media (max-width: 600px) {\n  .desktop-view {\n    display: none;\n  }\n  .mobile-view {\n    display: block;\n  }\n  .dashboard-container {\n    padding: 0;\n  }\n  .table-container {\n    margin: 0;\n  }\n  .mat-tab-body-content {\n    overflow: hidden;\n  }\n}\n.mobile-view .mat-expansion-panel {\n  margin: 8px 0;\n}\n.mobile-view .mat-expansion-panel-header {\n  font-size: 14px;\n}\n.mobile-view .mat-panel-title {\n  font-weight: 500;\n}\n.mobile-view .mat-panel-description {\n  justify-content: flex-end;\n  align-items: center;\n}\n.mobile-view .ride-details {\n  padding: 0 24px 16px;\n  font-size: 14px;\n}\n.mobile-view .ride-details p {\n  margin: 4px 0;\n}\n.mobile-view .mat-action-row {\n  justify-content: flex-end;\n  padding: 8px 12px 8px 24px;\n}\n/*# sourceMappingURL=rider.component.css.map */\n"] }]
  }], () => [{ type: AuthService }, { type: RideService }, { type: MessageService }, { type: PaymentService }, { type: Router }, { type: MatDialog }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RiderComponent, { className: "RiderComponent", filePath: "src/app/features/dashboard/rider/rider.component.ts", lineNumber: 361 });
})();
export {
  RiderComponent
};
//# sourceMappingURL=chunk-K67P7DIZ.js.map
