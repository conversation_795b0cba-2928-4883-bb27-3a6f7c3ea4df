{"version": 3, "sources": ["src/app/core/services/user.service.ts", "src/app/core/services/sms.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { BehaviorSubject, Observable, from, map } from 'rxjs';\nimport { User, UserFilter, UserRole } from '../models/user.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private supabase: SupabaseClient;\n  private usersSubject = new BehaviorSubject<User[]>([]);\n  users$ = this.usersSubject.asObservable();\n\n  constructor(private authService: AuthService) {\n    this.supabase = authService.supabase;\n  }\n\n  async getAllUsers(): Promise<User[]> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        throw error;\n      }\n\n      this.usersSubject.next(data);\n      return data;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      return [];\n    }\n  }\n\n  async getUsersByRole(role: UserRole): Promise<User[]> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .eq('role', role)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        throw error;\n      }\n\n      return data;\n    } catch (error) {\n      console.error(`Error fetching ${role}s:`, error);\n      return [];\n    }\n  }\n\n  async approveDriver(userId: string): Promise<boolean> {\n    try {\n      const { error } = await this.supabase\n        .from('profiles')\n        .update({\n          is_approved: \"TRUE\",\n        //  is_active: true // Automatically activate when approved\n        })\n        .eq('id', userId)\n        ;\n\n      if (error) {\n        throw error;\n      }\n\n      // Update the local users list\n      const currentUsers = this.usersSubject.value;\n      const updatedUsers = currentUsers.map(user =>\n        user.id === userId ? { ...user, is_approved: true, is_active: true } : user\n      );\n      this.usersSubject.next(updatedUsers);\n\n      return true;\n    } catch (error) {\n      console.error('Error approving driver:', error);\n      return false;\n    }\n  }\n\n  async updateUserStatus(userId: string, isActive: boolean): Promise<boolean> {\n    try {\n      // First get the user's current data\n      const { data: user, error: userError } = await this.supabase\n        .from('profiles')\n        .select('role, is_approved')\n        .eq('id', userId)\n        .single();\n\n      if (userError) throw userError;\n\n      // If trying to activate a rider/driver who isn't approved, prevent it\n      if (isActive && user.role !== 'admin' && !user.is_approved) {\n        throw new Error('Cannot activate unapproved user');\n      }\n\n      const { error } = await this.supabase\n        .from('profiles')\n        .update({\n          is_approved: isActive,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', userId);\n\n      if (error) throw error;\n\n      // Update the local users list\n      const currentUsers = this.usersSubject.value;\n      const updatedUsers = currentUsers.map(u =>\n        u.id === userId ? { ...u, is_approved: isActive } : u\n      );\n      this.usersSubject.next(updatedUsers);\n\n      return true;\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      return false;\n    }\n  }\n\n  async getUserById(userId: string): Promise<User | null> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      return data;\n    } catch (error) {\n      console.error('Error fetching user by ID:', error);\n      return null;\n    }\n  }\n\n  filterUsers(users: User[], filter: UserFilter): User[] {\n    return users.filter(user => {\n      // Filter by role if specified\n      if (filter.role && user.role !== filter.role) {\n        return false;\n      }\n\n      // Filter by approval status if specified and user is a driver\n      if (filter.isApproved !== undefined && user.role === 'driver') {\n        if (user.is_approved !== filter.isApproved) {\n          return false;\n        }\n      }\n\n      // Filter by search term if specified\n      if (filter.searchTerm) {\n        const searchTerm = filter.searchTerm.toLowerCase();\n        const fullName = user.full_name?.toLowerCase() || '';\n        const email = user.email.toLowerCase();\n        const phone = user.phone?.toLowerCase() || '';\n\n        if (!fullName.includes(searchTerm) &&\n            !email.includes(searchTerm) &&\n            !phone.includes(searchTerm)) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { UserService } from './user.service';\nimport { Ride } from '../models/ride.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmsService {\n  private supabase: SupabaseClient;\n\n  constructor(\n    private userService: UserService,\n    private authService: AuthService\n  ) {\n    this.supabase = authService.supabase;\n  }\n\n  /**\n   * Send an SMS notification to a user via Supabase Twilio lambda function\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @returns Promise resolving to the message SID if successful\n   */\n  async sendSms(to: string, body: string): Promise<string> {\n    try {\n      // Format phone number if needed (ensure it has the + prefix)\n\n      const formattedPhone = this.formatPhoneNumber(to);\n\n      // Call the Supabase lambda function to send the SMS\n      const { data, error } = await this.supabase.functions.invoke('twilio', {\n        body: {\n          to: formattedPhone,\n          message: body,\n          from: \"+17272025413\"\n        }\n      });\n\n      if (error) {\n        console.error('Error calling Twilio lambda function:', error);\n        throw error;\n      }\n\n      if (!data || !data.sid) {\n        throw new Error('No message SID returned from Twilio lambda function');\n      }\n\n      console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);\n      return data.sid;\n    } catch (error) {\n      console.error('Error sending SMS:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send ride assignment notifications to both rider and driver\n   * @param ride The ride that was assigned\n   * @param driverId The ID of the driver assigned to the ride\n   */\n  async sendRideAssignmentNotifications(ride: Ride, driverId: string): Promise<void> {\n    try {\n      // Get rider and driver information\n      const [rider, driver] = await Promise.all([\n        this.userService.getUserById(ride.rider_id),\n        this.userService.getUserById(driverId)\n      ]);\n\n      if (!rider || !driver) {\n        throw new Error('Could not find rider or driver information');\n      }\n\n      // Check if phone numbers are available\n      if (!rider.phone || !driver.phone) {\n        console.warn('Phone number missing for rider or driver. SMS notification skipped.');\n        return;\n      }\n\n      // Format pickup time for better readability\n      const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n\n      // Send enhanced notification to rider\n      await this.sendEnhancedDriverAssignedNotification(ride, driver);\n\n      // Send notification to driver with more detailed information\n      const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || 'your rider'} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;\n\n      // Send driver notification\n      const results = await Promise.allSettled([\n        this.sendSmsWithRetry(driver.phone, driverMessage)\n      ]);\n\n      // Check results and log any failures\n      const [driverResult] = results;\n\n      if (driverResult.status === 'rejected') {\n        console.error('Failed to send SMS to driver:', driverResult.reason);\n      } else {\n        console.log('Ride assignment notification sent successfully to driver');\n      }\n    } catch (error) {\n      console.error('Error sending ride assignment notifications:', error);\n      // Don't throw the error - we don't want to break the ride assignment process\n      // if SMS sending fails\n    }\n  }\n\n  /**\n   * Send SMS with retry logic for better reliability\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @param maxRetries Maximum number of retry attempts\n   * @returns Promise resolving to the message SID if successful\n   */\n  private async sendSmsWithRetry(to: string, body: string, maxRetries = 2): Promise<string> {\n    let lastError: any;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        // Wait a bit before retrying (exponential backoff)\n        if (attempt > 0) {\n          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));\n        }\n\n        return await this.sendSms(\"+1\"+to, body);\n      } catch (error) {\n        lastError = error;\n        console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);\n      }\n    }\n\n    // If we get here, all attempts failed\n    throw lastError || new Error('Failed to send SMS after multiple attempts');\n  }\n\n  /**\n   * Send ride status update notifications to both rider and driver\n   * @param ride The ride that had its status updated\n   * @param newStatus The new status of the ride\n   */\n  async sendRideStatusUpdateNotifications(ride: Ride, newStatus: string): Promise<void> {\n    try {\n      // Skip if the ride doesn't have both rider and driver assigned\n      if (!ride.rider_id || !ride.driver_id) {\n        console.warn('Ride is missing rider or driver ID. Status update notification skipped.');\n        return;\n      }\n\n      // Get rider and driver information\n      const [rider, driver] = await Promise.all([\n        this.userService.getUserById(ride.rider_id),\n        this.userService.getUserById(ride.driver_id)\n      ]);\n\n      if (!rider || !driver) {\n        throw new Error('Could not find rider or driver information');\n      }\n\n      // Check if phone numbers are available\n      if (!rider.phone || !driver.phone) {\n        console.warn('Phone number missing for rider or driver. Status update notification skipped.');\n        return;\n      }\n\n      // Create appropriate messages based on the new status\n      let riderMessage = '';\n      let driverMessage = '';\n\n      switch (newStatus) {\n        case 'in-progress':\n          riderMessage = `Your ride has started. Your driver ${driver.full_name || 'is'} on the way to ${ride.dropoff_location}.`;\n          driverMessage = `You have started the ride with ${rider.full_name || 'your rider'}. Destination: ${ride.dropoff_location}.`;\n          break;\n        case 'completed':\n          riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;\n          driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;\n          break;\n        case 'canceled':\n          riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;\n          driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;\n          break;\n        default:\n          // Don't send notifications for other status changes\n          return;\n      }\n\n      // Send both messages in parallel\n      const results = await Promise.allSettled([\n        this.sendSmsWithRetry(rider.phone, riderMessage),\n        this.sendSmsWithRetry(driver.phone, driverMessage)\n      ]);\n\n      // Check results and log any failures\n      const [riderResult, driverResult] = results;\n\n      if (riderResult.status === 'rejected') {\n        console.error('Failed to send status update SMS to rider:', riderResult.reason);\n      }\n\n      if (driverResult.status === 'rejected') {\n        console.error('Failed to send status update SMS to driver:', driverResult.reason);\n      }\n\n      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {\n        console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);\n      }\n    } catch (error) {\n      console.error('Error sending ride status update notifications:', error);\n      // Don't throw the error - we don't want to break the status update process\n      // if SMS sending fails\n    }\n  }\n\n  /**\n   * Send ride booking confirmation to the rider\n   * @param ride The ride that was booked\n   */\n  async sendRideBookingConfirmation(ride: Ride): Promise<void> {\n    try {\n      // Get rider information\n      const rider = await this.userService.getUserById(ride.rider_id);\n\n      if (!rider || !rider.phone) {\n        console.warn('Rider not found or phone number missing. Booking confirmation SMS skipped.');\n        return;\n      }\n\n      // Format pickup time\n      const pickupDate = new Date(ride.pickup_time);\n      const isASAP = pickupDate.getTime() <= Date.now() + (30 * 60 * 1000); // Within 30 minutes\n      const pickupTimeStr = isASAP ? 'ASAP' : pickupDate.toLocaleString();\n      const pickupDateStr = pickupDate.toLocaleDateString();\n\n      // Format fare estimate\n      const fareStr = ride.fare ? `$${ride.fare.toFixed(2)}` : 'TBD';\n\n      const message = `Your ride has been booked!\nPickup: ${ride.pickup_location}\nDropoff: ${ride.dropoff_location}\nPick up date: ${pickupDateStr}\nPickup Time: ${pickupTimeStr}\nFare Estimate: ${fareStr}\nDriver will be assigned shortly. You'll receive updates as your ride details are confirmed.\nMsg & data rates may apply. Message frequency varies.\nReply STOP to unsubscribe. View our policy at: https://bookholyrides.com/?p=1163`;\n\n      await this.sendSmsWithRetry(rider.phone, message);\n      console.log('Ride booking confirmation sent to rider');\n    } catch (error) {\n      console.error('Error sending ride booking confirmation:', error);\n      // Don't throw - we don't want to break ride creation if SMS fails\n    }\n  }\n\n  /**\n   * Send general ride booking notification to all drivers\n   * @param ride The ride that was booked\n   */\n  async sendRideBookingNotificationToDrivers(ride: Ride): Promise<void> {\n    try {\n      // Get all approved drivers\n      const drivers = await this.userService.getUsersByRole('driver');\n      const approvedDrivers = drivers.filter(driver => driver.is_approved && driver.phone);\n\n      if (approvedDrivers.length === 0) {\n        console.warn('No approved drivers with phone numbers found. Driver notification skipped.');\n        return;\n      }\n\n      const message = `A new trip has been booked! Login now at https://app.bookholyrides.com to view the ride details in the Holy Rides app.`;\n\n      // Send to all drivers in parallel\n      const results = await Promise.allSettled(\n        approvedDrivers.map(driver => this.sendSmsWithRetry(driver.phone!, message))\n      );\n\n      // Log results\n      const successful = results.filter(r => r.status === 'fulfilled').length;\n      const failed = results.filter(r => r.status === 'rejected').length;\n\n      console.log(`Ride booking notification sent to ${successful} drivers, ${failed} failed`);\n    } catch (error) {\n      console.error('Error sending ride booking notifications to drivers:', error);\n      // Don't throw - we don't want to break ride creation if SMS fails\n    }\n  }\n\n  /**\n   * Send enhanced driver assigned notification to rider\n   * @param ride The ride with assigned driver\n   * @param driver The assigned driver information\n   */\n  async sendEnhancedDriverAssignedNotification(ride: Ride, driver: any): Promise<void> {\n    try {\n      // Get rider information\n      const rider = await this.userService.getUserById(ride.rider_id);\n\n      if (!rider || !rider.phone) {\n        console.warn('Rider not found or phone number missing. Driver assigned notification skipped.');\n        return;\n      }\n\n      // Note: Vehicle info and license number would need to be added to driver profile\n      // For now, using placeholder values\n      const vehicleInfo = driver.vehicle_info || 'Vehicle details will be provided';\n      const licenseNumber = driver.license_number || 'License details will be provided';\n      const eta = 'ETA will be calculated'; // This would need real-time calculation\n\n      const message = `Great news — a driver has been assigned to your trip! 🚘\nDriver Name: ${driver.full_name || 'Driver'}\nVehicle: ${vehicleInfo}\nLicense Number: ${licenseNumber}\nETA: ${eta}\nMsg & data rates may apply. Reply STOP to unsubscribe.`;\n\n      await this.sendSmsWithRetry(rider.phone, message);\n      console.log('Enhanced driver assigned notification sent to rider');\n    } catch (error) {\n      console.error('Error sending enhanced driver assigned notification:', error);\n      // Don't throw - we don't want to break ride assignment if SMS fails\n    }\n  }\n\n  /**\n   * Send ride cancellation notifications\n   * @param ride The cancelled ride\n   */\n  async sendRideCancellationNotifications(ride: Ride): Promise<void> {\n    try {\n      const notifications: Promise<string>[] = [];\n\n      // Send to rider\n      const rider = await this.userService.getUserById(ride.rider_id);\n      if (rider && rider.phone) {\n        const riderMessage = `Your ride from ${ride.pickup_location} to ${ride.dropoff_location} has been cancelled. You can book a new ride at https://app.bookholyrides.com\nMsg & data rates may apply. Reply STOP to unsubscribe.`;\n        notifications.push(this.sendSmsWithRetry(rider.phone, riderMessage));\n      }\n\n      // Send to assigned driver if any\n      if (ride.driver_id) {\n        const driver = await this.userService.getUserById(ride.driver_id);\n        if (driver && driver.phone) {\n          const driverMessage = `The ride to ${ride.dropoff_location} has been cancelled. Please check your dashboard for new ride opportunities at https://app.bookholyrides.com\nMsg & data rates may apply. Reply STOP to unsubscribe.`;\n          notifications.push(this.sendSmsWithRetry(driver.phone, driverMessage));\n        }\n      }\n\n      // Send all notifications in parallel\n      if (notifications.length > 0) {\n        const results = await Promise.allSettled(notifications);\n        const successful = results.filter(r => r.status === 'fulfilled').length;\n        const failed = results.filter(r => r.status === 'rejected').length;\n        console.log(`Ride cancellation notifications: ${successful} sent, ${failed} failed`);\n      }\n    } catch (error) {\n      console.error('Error sending ride cancellation notifications:', error);\n      // Don't throw - we don't want to break ride cancellation if SMS fails\n    }\n  }\n\n  /**\n   * Send ride completion notification to rider with payment instructions\n   * @param ride The completed ride\n   */\n  async sendRideCompletionNotification(ride: Ride): Promise<void> {\n    try {\n      // Get rider information\n      const rider = await this.userService.getUserById(ride.rider_id);\n\n      if (!rider || !rider.phone) {\n        console.warn('Rider not found or phone number missing. Ride completion notification skipped.');\n        return;\n      }\n\n      // Format fare amount\n      const fareAmount = ride.fare ? `$${ride.fare.toFixed(2)}` : '$0.00';\n\n      const message = `Your ride is complete! 🚘\nThanks for riding with Holy Rides. Please complete payment now in the amount of ${fareAmount} using one of the following methods:\nCash app: https://cash.app/$HolyRides24\nSquare: https://square.link/u/o9zzuiAv?src=sheet\nMsg & data rates may apply. Reply STOP to unsubscribe.`;\n\n      await this.sendSmsWithRetry(rider.phone, message);\n      console.log('Ride completion notification sent to rider');\n    } catch (error) {\n      console.error('Error sending ride completion notification:', error);\n      // Don't throw - we don't want to break ride completion if SMS fails\n    }\n  }\n\n  /**\n   * Send payment confirmation notification to rider\n   * @param ride The ride that was paid for\n   * @param paymentAmount The amount that was paid\n   */\n  async sendPaymentConfirmationNotification(ride: Ride, paymentAmount: number): Promise<void> {\n    try {\n      // Get rider information\n      const rider = await this.userService.getUserById(ride.rider_id);\n\n      if (!rider || !rider.phone) {\n        console.warn('Rider not found or phone number missing. Payment confirmation notification skipped.');\n        return;\n      }\n\n      // Format pickup time\n      const pickupDate = new Date(ride.pickup_time);\n      const isASAP = pickupDate.getTime() <= Date.now() + (30 * 60 * 1000); // Within 30 minutes\n      const pickupTimeStr = isASAP ? 'ASAP' : pickupDate.toLocaleString();\n      const pickupDateStr = pickupDate.toLocaleDateString();\n\n      // Format payment amount\n      const amountStr = `$${paymentAmount.toFixed(2)}`;\n\n      const message = `Thank you! 🎉\nWe've received your payment of ${amountStr} for your recent ride.\nPickup: ${ride.pickup_location}\nDropoff: ${ride.dropoff_location}\nPick up date: ${pickupDateStr}\nPickup Time: ${pickupTimeStr}\n\nThanks for riding with Holy Rides. See you next time!\nMsg & data rates may apply. Reply STOP to unsubscribe.`;\n\n      await this.sendSmsWithRetry(rider.phone, message);\n      console.log('Payment confirmation notification sent to rider');\n    } catch (error) {\n      console.error('Error sending payment confirmation notification:', error);\n      // Don't throw - we don't want to break payment processing if SMS fails\n    }\n  }\n\n  /**\n   * Send admin notification when a ride status changes to started or completed\n   * @param ride The ride that had its status updated\n   * @param newStatus The new status of the ride ('in-progress' or 'completed')\n   */\n  async sendAdminRideStatusNotification(ride: Ride, newStatus: string): Promise<void> {\n    try {\n      const adminPhoneNumber = '**********';\n\n      // Get driver and rider information\n      const [driver, rider] = await Promise.all([\n        ride.driver_id ? this.userService.getUserById(ride.driver_id) : null,\n        this.userService.getUserById(ride.rider_id)\n      ]);\n\n      // Format pickup time\n      const pickupTime = new Date(ride.pickup_time).toLocaleString();\n\n      // Create status-specific message\n      let statusText = '';\n      if (newStatus === 'in-progress') {\n        statusText = 'STARTED';\n      } else if (newStatus === 'completed') {\n        statusText = 'COMPLETED';\n      } else {\n        return; // Only send notifications for started and completed rides\n      }\n\n      const driverName = driver?.full_name || 'Unknown Driver';\n      const riderName = rider?.full_name || 'Unknown Rider';\n\n      const message = `🚘 RIDE ${statusText}\nDriver: ${driverName}\nRider: ${riderName}\nFrom: ${ride.pickup_location}\nTo: ${ride.dropoff_location}\nPickup Time: ${pickupTime}\n${ride.fare ? `Fare: $${ride.fare.toFixed(2)}` : ''}`;\n\n      await this.sendSmsWithRetry(adminPhoneNumber, message);\n      console.log(`Admin notification sent for ride ${statusText.toLowerCase()}`);\n    } catch (error) {\n      console.error('Error sending admin ride status notification:', error);\n      // Don't throw - we don't want to break the ride status update if SMS fails\n    }\n  }\n\n  /**\n   * Format phone number to ensure it has the international format with + prefix\n   * @param phoneNumber The phone number to format\n   * @returns Formatted phone number\n   */\n  private formatPhoneNumber(phoneNumber: string): string {\n    // If the phone number already starts with +, return it as is\n    if (phoneNumber.startsWith('+')) {\n      return phoneNumber;\n    }\n\n    // If it starts with a country code without +, add the +\n    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {\n      return `+${phoneNumber}`;\n    }\n\n    // Otherwise, assume it's a US number without country code\n    // and add +1 (you can modify this based on your target country)\n    return `+1${phoneNumber.replace(/\\D/g, '')}`;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AASM,IAAO,cAAP,MAAO,aAAW;EAKF;EAJZ;EACA,eAAe,IAAI,gBAAwB,CAAA,CAAE;EACrD,SAAS,KAAK,aAAa,aAAY;EAEvC,YAAoB,aAAwB;AAAxB,SAAA,cAAA;AAClB,SAAK,WAAW,YAAY;EAC9B;EAEM,cAAW;;AACf,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,GAAG,EACV,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI,OAAO;AACT,gBAAM;QACR;AAEA,aAAK,aAAa,KAAK,IAAI;AAC3B,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,eAAO,CAAA;MACT;IACF;;EAEM,eAAe,MAAc;;AACjC,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,GAAG,EACV,GAAG,QAAQ,IAAI,EACf,MAAM,cAAc,EAAE,WAAW,MAAK,CAAE;AAE3C,YAAI,OAAO;AACT,gBAAM;QACR;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,kBAAkB,IAAI,MAAM,KAAK;AAC/C,eAAO,CAAA;MACT;IACF;;EAEM,cAAc,QAAc;;AAChC,UAAI;AACF,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,SAC1B,KAAK,UAAU,EACf,OAAO;UACN,aAAa;;SAEd,EACA,GAAG,MAAM,MAAM;AAGlB,YAAI,OAAO;AACT,gBAAM;QACR;AAGA,cAAM,eAAe,KAAK,aAAa;AACvC,cAAM,eAAe,aAAa,IAAI,UACpC,KAAK,OAAO,SAAS,iCAAK,OAAL,EAAW,aAAa,MAAM,WAAW,KAAI,KAAK,IAAI;AAE7E,aAAK,aAAa,KAAK,YAAY;AAEnC,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,eAAO;MACT;IACF;;EAEM,iBAAiB,QAAgB,UAAiB;;AACtD,UAAI;AAEF,cAAM,EAAE,MAAM,MAAM,OAAO,UAAS,IAAK,MAAM,KAAK,SACjD,KAAK,UAAU,EACf,OAAO,mBAAmB,EAC1B,GAAG,MAAM,MAAM,EACf,OAAM;AAET,YAAI;AAAW,gBAAM;AAGrB,YAAI,YAAY,KAAK,SAAS,WAAW,CAAC,KAAK,aAAa;AAC1D,gBAAM,IAAI,MAAM,iCAAiC;QACnD;AAEA,cAAM,EAAE,MAAK,IAAK,MAAM,KAAK,SAC1B,KAAK,UAAU,EACf,OAAO;UACN,aAAa;UACb,aAAY,oBAAI,KAAI,GAAG,YAAW;SACnC,EACA,GAAG,MAAM,MAAM;AAElB,YAAI;AAAO,gBAAM;AAGjB,cAAM,eAAe,KAAK,aAAa;AACvC,cAAM,eAAe,aAAa,IAAI,OACpC,EAAE,OAAO,SAAS,iCAAK,IAAL,EAAQ,aAAa,SAAQ,KAAK,CAAC;AAEvD,aAAK,aAAa,KAAK,YAAY;AAEnC,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,+BAA+B,KAAK;AAClD,eAAO;MACT;IACF;;EAEM,YAAY,QAAc;;AAC9B,UAAI;AACF,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAChC,KAAK,UAAU,EACf,OAAO,GAAG,EACV,GAAG,MAAM,MAAM,EACf,OAAM;AAET,YAAI,OAAO;AACT,gBAAM;QACR;AAEA,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,eAAO;MACT;IACF;;EAEA,YAAY,OAAe,QAAkB;AAC3C,WAAO,MAAM,OAAO,UAAO;AAEzB,UAAI,OAAO,QAAQ,KAAK,SAAS,OAAO,MAAM;AAC5C,eAAO;MACT;AAGA,UAAI,OAAO,eAAe,UAAa,KAAK,SAAS,UAAU;AAC7D,YAAI,KAAK,gBAAgB,OAAO,YAAY;AAC1C,iBAAO;QACT;MACF;AAGA,UAAI,OAAO,YAAY;AACrB,cAAM,aAAa,OAAO,WAAW,YAAW;AAChD,cAAM,WAAW,KAAK,WAAW,YAAW,KAAM;AAClD,cAAM,QAAQ,KAAK,MAAM,YAAW;AACpC,cAAM,QAAQ,KAAK,OAAO,YAAW,KAAM;AAE3C,YAAI,CAAC,SAAS,SAAS,UAAU,KAC7B,CAAC,MAAM,SAAS,UAAU,KAC1B,CAAC,MAAM,SAAS,UAAU,GAAG;AAC/B,iBAAO;QACT;MACF;AAEA,aAAO;IACT,CAAC;EACH;;qCArKW,cAAW,mBAAA,WAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;;;ACCK,IAAO,aAAP,MAAO,YAAU;EAIX;EACA;EAJF;EAER,YACU,aACA,aAAwB;AADxB,SAAA,cAAA;AACA,SAAA,cAAA;AAER,SAAK,WAAW,YAAY;EAC9B;;;;;;;EAQM,QAAQ,IAAY,MAAY;;AACpC,UAAI;AAGF,cAAM,iBAAiB,KAAK,kBAAkB,EAAE;AAGhD,cAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,SAAS,UAAU,OAAO,UAAU;UACrE,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,MAAM;;SAET;AAED,YAAI,OAAO;AACT,kBAAQ,MAAM,yCAAyC,KAAK;AAC5D,gBAAM;QACR;AAEA,YAAI,CAAC,QAAQ,CAAC,KAAK,KAAK;AACtB,gBAAM,IAAI,MAAM,qDAAqD;QACvE;AAEA,gBAAQ,IAAI,4BAA4B,EAAE,UAAU,KAAK,GAAG,EAAE;AAC9D,eAAO,KAAK;MACd,SAAS,OAAO;AACd,gBAAQ,MAAM,sBAAsB,KAAK;AACzC,cAAM;MACR;IACF;;;;;;;EAOM,gCAAgC,MAAY,UAAgB;;AAChE,UAAI;AAEF,cAAM,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;UACxC,KAAK,YAAY,YAAY,KAAK,QAAQ;UAC1C,KAAK,YAAY,YAAY,QAAQ;SACtC;AAED,YAAI,CAAC,SAAS,CAAC,QAAQ;AACrB,gBAAM,IAAI,MAAM,4CAA4C;QAC9D;AAGA,YAAI,CAAC,MAAM,SAAS,CAAC,OAAO,OAAO;AACjC,kBAAQ,KAAK,qEAAqE;AAClF;QACF;AAGA,cAAM,aAAa,IAAI,KAAK,KAAK,WAAW,EAAE,mBAAmB,CAAA,GAAI;UACnE,MAAM;UACN,QAAQ;SACT;AAGD,cAAM,KAAK,uCAAuC,MAAM,MAAM;AAG9D,cAAM,gBAAgB,8CAA8C,MAAM,aAAa,YAAY,OAAO,KAAK,eAAe,OAAO,UAAU,oBAAoB,KAAK,gBAAgB,kBAAkB,MAAM,KAAK;AAGrN,cAAM,UAAU,MAAM,QAAQ,WAAW;UACvC,KAAK,iBAAiB,OAAO,OAAO,aAAa;SAClD;AAGD,cAAM,CAAC,YAAY,IAAI;AAEvB,YAAI,aAAa,WAAW,YAAY;AACtC,kBAAQ,MAAM,iCAAiC,aAAa,MAAM;QACpE,OAAO;AACL,kBAAQ,IAAI,0DAA0D;QACxE;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,gDAAgD,KAAK;MAGrE;IACF;;;;;;;;;EASc,iBAAiB,IAAY,MAAc,aAAa,GAAC;;AACrE,UAAI;AAEJ,eAAS,UAAU,GAAG,WAAW,YAAY,WAAW;AACtD,YAAI;AAEF,cAAI,UAAU,GAAG;AACf,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,MAAO,KAAK,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;UACnF;AAEA,iBAAO,MAAM,KAAK,QAAQ,OAAK,IAAI,IAAI;QACzC,SAAS,OAAO;AACd,sBAAY;AACZ,kBAAQ,KAAK,uBAAuB,UAAU,CAAC,IAAI,aAAa,CAAC,YAAY,KAAK;QACpF;MACF;AAGA,YAAM,aAAa,IAAI,MAAM,4CAA4C;IAC3E;;;;;;;EAOM,kCAAkC,MAAY,WAAiB;;AACnE,UAAI;AAEF,YAAI,CAAC,KAAK,YAAY,CAAC,KAAK,WAAW;AACrC,kBAAQ,KAAK,yEAAyE;AACtF;QACF;AAGA,cAAM,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;UACxC,KAAK,YAAY,YAAY,KAAK,QAAQ;UAC1C,KAAK,YAAY,YAAY,KAAK,SAAS;SAC5C;AAED,YAAI,CAAC,SAAS,CAAC,QAAQ;AACrB,gBAAM,IAAI,MAAM,4CAA4C;QAC9D;AAGA,YAAI,CAAC,MAAM,SAAS,CAAC,OAAO,OAAO;AACjC,kBAAQ,KAAK,+EAA+E;AAC5F;QACF;AAGA,YAAI,eAAe;AACnB,YAAI,gBAAgB;AAEpB,gBAAQ,WAAW;UACjB,KAAK;AACH,2BAAe,sCAAsC,OAAO,aAAa,IAAI,kBAAkB,KAAK,gBAAgB;AACpH,4BAAgB,kCAAkC,MAAM,aAAa,YAAY,kBAAkB,KAAK,gBAAgB;AACxH;UACF,KAAK;AACH,2BAAe,gBAAgB,KAAK,gBAAgB;AACpD,4BAAgB,kCAAkC,KAAK,gBAAgB;AACvE;UACF,KAAK;AACH,2BAAe;AACf,4BAAgB,eAAe,KAAK,gBAAgB;AACpD;UACF;AAEE;QACJ;AAGA,cAAM,UAAU,MAAM,QAAQ,WAAW;UACvC,KAAK,iBAAiB,MAAM,OAAO,YAAY;UAC/C,KAAK,iBAAiB,OAAO,OAAO,aAAa;SAClD;AAGD,cAAM,CAAC,aAAa,YAAY,IAAI;AAEpC,YAAI,YAAY,WAAW,YAAY;AACrC,kBAAQ,MAAM,8CAA8C,YAAY,MAAM;QAChF;AAEA,YAAI,aAAa,WAAW,YAAY;AACtC,kBAAQ,MAAM,+CAA+C,aAAa,MAAM;QAClF;AAEA,YAAI,YAAY,WAAW,eAAe,aAAa,WAAW,aAAa;AAC7E,kBAAQ,IAAI,uBAAuB,SAAS,4DAA4D;QAC1G;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,mDAAmD,KAAK;MAGxE;IACF;;;;;;EAMM,4BAA4B,MAAU;;AAC1C,UAAI;AAEF,cAAM,QAAQ,MAAM,KAAK,YAAY,YAAY,KAAK,QAAQ;AAE9D,YAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B,kBAAQ,KAAK,4EAA4E;AACzF;QACF;AAGA,cAAM,aAAa,IAAI,KAAK,KAAK,WAAW;AAC5C,cAAM,SAAS,WAAW,QAAO,KAAM,KAAK,IAAG,IAAM,KAAK,KAAK;AAC/D,cAAM,gBAAgB,SAAS,SAAS,WAAW,eAAc;AACjE,cAAM,gBAAgB,WAAW,mBAAkB;AAGnD,cAAM,UAAU,KAAK,OAAO,IAAI,KAAK,KAAK,QAAQ,CAAC,CAAC,KAAK;AAEzD,cAAM,UAAU;UACZ,KAAK,eAAe;WACnB,KAAK,gBAAgB;gBAChB,aAAa;eACd,aAAa;iBACX,OAAO;;;;AAKlB,cAAM,KAAK,iBAAiB,MAAM,OAAO,OAAO;AAChD,gBAAQ,IAAI,yCAAyC;MACvD,SAAS,OAAO;AACd,gBAAQ,MAAM,4CAA4C,KAAK;MAEjE;IACF;;;;;;EAMM,qCAAqC,MAAU;;AACnD,UAAI;AAEF,cAAM,UAAU,MAAM,KAAK,YAAY,eAAe,QAAQ;AAC9D,cAAM,kBAAkB,QAAQ,OAAO,YAAU,OAAO,eAAe,OAAO,KAAK;AAEnF,YAAI,gBAAgB,WAAW,GAAG;AAChC,kBAAQ,KAAK,4EAA4E;AACzF;QACF;AAEA,cAAM,UAAU;AAGhB,cAAM,UAAU,MAAM,QAAQ,WAC5B,gBAAgB,IAAI,YAAU,KAAK,iBAAiB,OAAO,OAAQ,OAAO,CAAC,CAAC;AAI9E,cAAM,aAAa,QAAQ,OAAO,OAAK,EAAE,WAAW,WAAW,EAAE;AACjE,cAAM,SAAS,QAAQ,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE;AAE5D,gBAAQ,IAAI,qCAAqC,UAAU,aAAa,MAAM,SAAS;MACzF,SAAS,OAAO;AACd,gBAAQ,MAAM,wDAAwD,KAAK;MAE7E;IACF;;;;;;;EAOM,uCAAuC,MAAY,QAAW;;AAClE,UAAI;AAEF,cAAM,QAAQ,MAAM,KAAK,YAAY,YAAY,KAAK,QAAQ;AAE9D,YAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B,kBAAQ,KAAK,gFAAgF;AAC7F;QACF;AAIA,cAAM,cAAc,OAAO,gBAAgB;AAC3C,cAAM,gBAAgB,OAAO,kBAAkB;AAC/C,cAAM,MAAM;AAEZ,cAAM,UAAU;eACP,OAAO,aAAa,QAAQ;WAChC,WAAW;kBACJ,aAAa;OACxB,GAAG;;AAGJ,cAAM,KAAK,iBAAiB,MAAM,OAAO,OAAO;AAChD,gBAAQ,IAAI,qDAAqD;MACnE,SAAS,OAAO;AACd,gBAAQ,MAAM,wDAAwD,KAAK;MAE7E;IACF;;;;;;EAMM,kCAAkC,MAAU;;AAChD,UAAI;AACF,cAAM,gBAAmC,CAAA;AAGzC,cAAM,QAAQ,MAAM,KAAK,YAAY,YAAY,KAAK,QAAQ;AAC9D,YAAI,SAAS,MAAM,OAAO;AACxB,gBAAM,eAAe,kBAAkB,KAAK,eAAe,OAAO,KAAK,gBAAgB;;AAEvF,wBAAc,KAAK,KAAK,iBAAiB,MAAM,OAAO,YAAY,CAAC;QACrE;AAGA,YAAI,KAAK,WAAW;AAClB,gBAAM,SAAS,MAAM,KAAK,YAAY,YAAY,KAAK,SAAS;AAChE,cAAI,UAAU,OAAO,OAAO;AAC1B,kBAAM,gBAAgB,eAAe,KAAK,gBAAgB;;AAE1D,0BAAc,KAAK,KAAK,iBAAiB,OAAO,OAAO,aAAa,CAAC;UACvE;QACF;AAGA,YAAI,cAAc,SAAS,GAAG;AAC5B,gBAAM,UAAU,MAAM,QAAQ,WAAW,aAAa;AACtD,gBAAM,aAAa,QAAQ,OAAO,OAAK,EAAE,WAAW,WAAW,EAAE;AACjE,gBAAM,SAAS,QAAQ,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE;AAC5D,kBAAQ,IAAI,oCAAoC,UAAU,UAAU,MAAM,SAAS;QACrF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,kDAAkD,KAAK;MAEvE;IACF;;;;;;EAMM,+BAA+B,MAAU;;AAC7C,UAAI;AAEF,cAAM,QAAQ,MAAM,KAAK,YAAY,YAAY,KAAK,QAAQ;AAE9D,YAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B,kBAAQ,KAAK,gFAAgF;AAC7F;QACF;AAGA,cAAM,aAAa,KAAK,OAAO,IAAI,KAAK,KAAK,QAAQ,CAAC,CAAC,KAAK;AAE5D,cAAM,UAAU;kFAC4D,UAAU;;;;AAKtF,cAAM,KAAK,iBAAiB,MAAM,OAAO,OAAO;AAChD,gBAAQ,IAAI,4CAA4C;MAC1D,SAAS,OAAO;AACd,gBAAQ,MAAM,+CAA+C,KAAK;MAEpE;IACF;;;;;;;EAOM,oCAAoC,MAAY,eAAqB;;AACzE,UAAI;AAEF,cAAM,QAAQ,MAAM,KAAK,YAAY,YAAY,KAAK,QAAQ;AAE9D,YAAI,CAAC,SAAS,CAAC,MAAM,OAAO;AAC1B,kBAAQ,KAAK,qFAAqF;AAClG;QACF;AAGA,cAAM,aAAa,IAAI,KAAK,KAAK,WAAW;AAC5C,cAAM,SAAS,WAAW,QAAO,KAAM,KAAK,IAAG,IAAM,KAAK,KAAK;AAC/D,cAAM,gBAAgB,SAAS,SAAS,WAAW,eAAc;AACjE,cAAM,gBAAgB,WAAW,mBAAkB;AAGnD,cAAM,YAAY,IAAI,cAAc,QAAQ,CAAC,CAAC;AAE9C,cAAM,UAAU;iCACW,SAAS;UAChC,KAAK,eAAe;WACnB,KAAK,gBAAgB;gBAChB,aAAa;eACd,aAAa;;;;AAKtB,cAAM,KAAK,iBAAiB,MAAM,OAAO,OAAO;AAChD,gBAAQ,IAAI,iDAAiD;MAC/D,SAAS,OAAO;AACd,gBAAQ,MAAM,oDAAoD,KAAK;MAEzE;IACF;;;;;;;EAOM,gCAAgC,MAAY,WAAiB;;AACjE,UAAI;AACF,cAAM,mBAAmB;AAGzB,cAAM,CAAC,QAAQ,KAAK,IAAI,MAAM,QAAQ,IAAI;UACxC,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,SAAS,IAAI;UAChE,KAAK,YAAY,YAAY,KAAK,QAAQ;SAC3C;AAGD,cAAM,aAAa,IAAI,KAAK,KAAK,WAAW,EAAE,eAAc;AAG5D,YAAI,aAAa;AACjB,YAAI,cAAc,eAAe;AAC/B,uBAAa;QACf,WAAW,cAAc,aAAa;AACpC,uBAAa;QACf,OAAO;AACL;QACF;AAEA,cAAM,aAAa,QAAQ,aAAa;AACxC,cAAM,YAAY,OAAO,aAAa;AAEtC,cAAM,UAAU,kBAAW,UAAU;UACjC,UAAU;SACX,SAAS;QACV,KAAK,eAAe;MACtB,KAAK,gBAAgB;eACZ,UAAU;EACvB,KAAK,OAAO,UAAU,KAAK,KAAK,QAAQ,CAAC,CAAC,KAAK,EAAE;AAE7C,cAAM,KAAK,iBAAiB,kBAAkB,OAAO;AACrD,gBAAQ,IAAI,oCAAoC,WAAW,YAAW,CAAE,EAAE;MAC5E,SAAS,OAAO;AACd,gBAAQ,MAAM,iDAAiD,KAAK;MAEtE;IACF;;;;;;;EAOQ,kBAAkB,aAAmB;AAE3C,QAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,aAAO;IACT;AAGA,QAAI,YAAY,MAAM,8BAA8B,GAAG;AACrD,aAAO,IAAI,WAAW;IACxB;AAIA,WAAO,KAAK,YAAY,QAAQ,OAAO,EAAE,CAAC;EAC5C;;qCAjfW,aAAU,mBAAA,WAAA,GAAA,mBAAA,WAAA,CAAA;EAAA;4EAAV,aAAU,SAAV,YAAU,WAAA,YAFT,OAAM,CAAA;;;sEAEP,YAAU,CAAA;UAHtB;WAAW;MACV,YAAY;KACb;;;", "names": []}