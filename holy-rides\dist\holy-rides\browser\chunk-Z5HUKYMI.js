import "./chunk-JURKDGMK.js";
import {
  SmsService
} from "./chunk-NVGZCMKL.js";
import {
  MatOption,
  MatSelect,
  MatSelectModule
} from "./chunk-GWPOTN5B.js";
import "./chunk-5DER6JXC.js";
import "./chunk-LTJSKJGW.js";
import "./chunk-QIPXWAWB.js";
import {
  Router,
  RouterLink
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MatError,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  MatLabel,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  CommonModule,
  Component,
  MatButton,
  MatButtonModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import {
  __async
} from "./chunk-S35DAJRX.js";

// src/app/features/auth/register/register.component.ts
function RegisterComponent_mat_error_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Full name is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Email is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Please enter a valid email");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Password is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Password must be at least 6 characters");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Password confirmation is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Passwords do not match");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_error_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Phone number is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_mat_option_39_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-option", 16);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const role_r1 = ctx.$implicit;
    \u0275\u0275property("value", role_r1.value);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", role_r1.label, " ");
  }
}
function RegisterComponent_mat_error_40_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, "Role is required");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.error);
  }
}
var RegisterComponent = class _RegisterComponent {
  formBuilder;
  router;
  authService;
  smsService;
  registerForm;
  error = "";
  loading = false;
  roles = [
    { value: "rider", label: "Rider" },
    { value: "driver", label: "Driver" }
  ];
  constructor(formBuilder, router, authService, smsService) {
    this.formBuilder = formBuilder;
    this.router = router;
    this.authService = authService;
    this.smsService = smsService;
    this.registerForm = this.formBuilder.group({
      full_name: ["", [Validators.required]],
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(6)]],
      confirmPassword: ["", [Validators.required]],
      phone: ["", [Validators.required]],
      role: ["", [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }
  passwordMatchValidator(g) {
    return g.get("password")?.value === g.get("confirmPassword")?.value ? null : { mismatch: true };
  }
  /**
   * Generate welcome message based on user role
   */
  getWelcomeMessage(role) {
    const baseMessage = `Welcome to the Holy Rides Transportation Family!
Thank you for signing up. You've opted in to receive text messages for important ride updates, promotions, and account notifications.`;
    const roleSpecificMessage = role === "driver" ? "Go ahead and log in now to start accepting rides at https://app.bookholyrides.com" : "Go ahead and log in now to book your first ride at https://app.bookholyrides.com";
    const footer = `
Msg & data rates may apply. Message frequency varies.
To stop receiving messages, reply STOP. For help, reply HELP.
View our policy at: https://bookholyrides.com/?p=1163`;
    return `${baseMessage} ${roleSpecificMessage}${footer}`;
  }
  /**
   * Send welcome SMS to the newly registered user
   */
  sendWelcomeSms(phone, role) {
    return __async(this, null, function* () {
      try {
        const welcomeMessage = this.getWelcomeMessage(role);
        yield this.smsService.sendSms(phone, welcomeMessage);
        console.log("Welcome SMS sent successfully");
      } catch (error) {
        console.error("Failed to send welcome SMS:", error);
      }
    });
  }
  onSubmit() {
    return __async(this, null, function* () {
      if (this.registerForm.invalid) {
        return;
      }
      this.loading = true;
      this.error = "";
      try {
        const { error: registerError } = yield this.authService.register(this.registerForm.value.email, this.registerForm.value.password, this.registerForm.value.role, this.registerForm.value.phone, this.registerForm.value.full_name);
        if (registerError) {
          this.error = registerError.message;
          return;
        }
        yield new Promise((resolve) => setTimeout(resolve, 1e3));
        if (this.registerForm.value.phone) {
          yield this.sendWelcomeSms(this.registerForm.value.phone, this.registerForm.value.role);
        }
        const { error: loginError } = yield this.authService.login(this.registerForm.value.email, this.registerForm.value.password);
        if (loginError) {
          this.error = loginError.message;
          return;
        }
        yield this.router.navigate(["/auth/profile"]);
      } catch (err) {
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    });
  }
  static \u0275fac = function RegisterComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RegisterComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(SmsService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RegisterComponent, selectors: [["app-register"]], decls: 48, vars: 14, consts: [[1, "register-container"], [3, "ngSubmit", "formGroup"], ["appearance", "outline"], ["matInput", "", "type", "text", "formControlName", "full_name", "placeholder", "Enter your full name"], [4, "ngIf"], ["matInput", "", "type", "email", "formControlName", "email", "placeholder", "Enter your email"], ["matInput", "", "type", "password", "formControlName", "password", "placeholder", "Enter your password"], ["matInput", "", "type", "password", "formControlName", "confirmPassword", "placeholder", "Confirm your password"], ["matInput", "", "type", "tel", "formControlName", "phone", "placeholder", "Enter your phone number"], ["formControlName", "role"], [3, "value", 4, "ngFor", "ngForOf"], ["class", "error-message", 4, "ngIf"], [1, "button-container"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"], [1, "links"], ["routerLink", "/auth/login"], [3, "value"], [1, "error-message"]], template: function RegisterComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Register");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "form", 1);
      \u0275\u0275listener("ngSubmit", function RegisterComponent_Template_form_ngSubmit_6_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(7, "mat-form-field", 2)(8, "mat-label");
      \u0275\u0275text(9, "Full Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(10, "input", 3);
      \u0275\u0275template(11, RegisterComponent_mat_error_11_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "mat-form-field", 2)(13, "mat-label");
      \u0275\u0275text(14, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(15, "input", 5);
      \u0275\u0275template(16, RegisterComponent_mat_error_16_Template, 2, 0, "mat-error", 4)(17, RegisterComponent_mat_error_17_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "mat-form-field", 2)(19, "mat-label");
      \u0275\u0275text(20, "Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(21, "input", 6);
      \u0275\u0275template(22, RegisterComponent_mat_error_22_Template, 2, 0, "mat-error", 4)(23, RegisterComponent_mat_error_23_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "mat-form-field", 2)(25, "mat-label");
      \u0275\u0275text(26, "Confirm Password");
      \u0275\u0275elementEnd();
      \u0275\u0275element(27, "input", 7);
      \u0275\u0275template(28, RegisterComponent_mat_error_28_Template, 2, 0, "mat-error", 4)(29, RegisterComponent_mat_error_29_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "mat-form-field", 2)(31, "mat-label");
      \u0275\u0275text(32, "Phone Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(33, "input", 8);
      \u0275\u0275template(34, RegisterComponent_mat_error_34_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "mat-form-field", 2)(36, "mat-label");
      \u0275\u0275text(37, "Role");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "mat-select", 9);
      \u0275\u0275template(39, RegisterComponent_mat_option_39_Template, 2, 2, "mat-option", 10);
      \u0275\u0275elementEnd();
      \u0275\u0275template(40, RegisterComponent_mat_error_40_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275template(41, RegisterComponent_div_41_Template, 2, 1, "div", 11);
      \u0275\u0275elementStart(42, "div", 12)(43, "button", 13);
      \u0275\u0275text(44);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(45, "div", 14)(46, "a", 15);
      \u0275\u0275text(47, "Already have an account? Login");
      \u0275\u0275elementEnd()()()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_2_0;
      let tmp_3_0;
      let tmp_4_0;
      let tmp_5_0;
      let tmp_6_0;
      let tmp_8_0;
      let tmp_10_0;
      \u0275\u0275advance(6);
      \u0275\u0275property("formGroup", ctx.registerForm);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.registerForm.get("full_name")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors["required"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_2_0 = ctx.registerForm.get("email")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_3_0 = ctx.registerForm.get("email")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors["email"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_4_0 = ctx.registerForm.get("password")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_5_0 = ctx.registerForm.get("password")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors["minlength"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_6_0 = ctx.registerForm.get("confirmPassword")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.registerForm.errors == null ? null : ctx.registerForm.errors["mismatch"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_8_0 = ctx.registerForm.get("phone")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors["required"]);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngForOf", ctx.roles);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (tmp_10_0 = ctx.registerForm.get("role")) == null ? null : tmp_10_0.errors == null ? null : tmp_10_0.errors["required"]);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.registerForm.invalid || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.loading ? "Registering..." : "Register", " ");
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, MatFormFieldModule, MatFormField, MatLabel, MatError, MatInputModule, MatInput, MatButtonModule, MatButton, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, MatSelectModule, MatSelect, MatOption, RouterLink], styles: ["\n\n.register-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 20px;\n}\n.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n}\n.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\n  width: 100px;\n  height: 100px;\n  margin-bottom: 10px;\n}\n.register-container[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 500;\n  color: #3f51b5;\n  margin: 0;\n}\n.register-container[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.register-container[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.register-container[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 8px;\n}\n.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%] {\n  margin-top: 16px;\n  text-align: center;\n}\n.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\n  color: #3f51b5;\n  text-decoration: none;\n}\n.register-container[_ngcontent-%COMP%]   .links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\n  text-decoration: underline;\n}\n.register-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\n  color: #f44336;\n  text-align: center;\n  margin: 8px 0;\n}\n/*# sourceMappingURL=register.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RegisterComponent, [{
    type: Component,
    args: [{ selector: "app-register", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatCardModule,
      MatSelectModule,
      RouterLink
    ], template: `<div class="register-container">\r
  <!-- <div class="logo-container">\r
    <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">\r
\r
  </div> -->\r
  <mat-card>\r
    <mat-card-header>\r
      <mat-card-title>Register</mat-card-title>\r
    </mat-card-header>\r
    <mat-card-content>\r
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">\r
        <mat-form-field appearance="outline">\r
          <mat-label>Full Name</mat-label>\r
          <input matInput type="text" formControlName="full_name" placeholder="Enter your full name">\r
          <mat-error *ngIf="registerForm.get('full_name')?.errors?.['required']">Full name is required</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Email</mat-label>\r
          <input matInput type="email" formControlName="email" placeholder="Enter your email">\r
          <mat-error *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</mat-error>\r
          <mat-error *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Password</mat-label>\r
          <input matInput type="password" formControlName="password" placeholder="Enter your password">\r
          <mat-error *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</mat-error>\r
          <mat-error *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Confirm Password</mat-label>\r
          <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm your password">\r
          <mat-error *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Password confirmation is required</mat-error>\r
          <mat-error *ngIf="registerForm.errors?.['mismatch']">Passwords do not match</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Phone Number</mat-label>\r
          <input matInput type="tel" formControlName="phone" placeholder="Enter your phone number">\r
          <mat-error *ngIf="registerForm.get('phone')?.errors?.['required']">Phone number is required</mat-error>\r
        </mat-form-field>\r
\r
        <mat-form-field appearance="outline">\r
          <mat-label>Role</mat-label>\r
          <mat-select formControlName="role">\r
            <mat-option *ngFor="let role of roles" [value]="role.value">\r
              {{role.label}}\r
            </mat-option>\r
          </mat-select>\r
          <mat-error *ngIf="registerForm.get('role')?.errors?.['required']">Role is required</mat-error>\r
        </mat-form-field>\r
\r
        <div class="error-message" *ngIf="error">{{ error }}</div>\r
\r
        <div class="button-container">\r
          <button mat-raised-button color="primary" type="submit" [disabled]="registerForm.invalid || loading">\r
            {{ loading ? 'Registering...' : 'Register' }}\r
          </button>\r
        </div>\r
\r
        <div class="links">\r
          <a routerLink="/auth/login">Already have an account? Login</a>\r
        </div>\r
      </form>\r
    </mat-card-content>\r
  </mat-card>\r
</div>\r
`, styles: ["/* src/app/features/auth/register/register.component.scss */\n.register-container {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  min-height: 100vh;\n  padding: 20px;\n}\n.register-container .logo-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-bottom: 30px;\n}\n.register-container .logo-container .logo {\n  width: 100px;\n  height: 100px;\n  margin-bottom: 10px;\n}\n.register-container .logo-container .app-name {\n  font-size: 24px;\n  font-weight: 500;\n  color: #3f51b5;\n  margin: 0;\n}\n.register-container mat-card {\n  width: 100%;\n  max-width: 400px;\n  padding: 20px;\n}\n.register-container mat-form-field {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.register-container .button-container {\n  margin-top: 24px;\n  display: flex;\n  justify-content: center;\n}\n.register-container .button-container button {\n  width: 100%;\n  padding: 8px;\n}\n.register-container .links {\n  margin-top: 16px;\n  text-align: center;\n}\n.register-container .links a {\n  color: #3f51b5;\n  text-decoration: none;\n}\n.register-container .links a:hover {\n  text-decoration: underline;\n}\n.register-container .error-message {\n  color: #f44336;\n  text-align: center;\n  margin: 8px 0;\n}\n/*# sourceMappingURL=register.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: Router }, { type: AuthService }, { type: SmsService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RegisterComponent, { className: "RegisterComponent", filePath: "src/app/features/auth/register/register.component.ts", lineNumber: 29 });
})();
export {
  RegisterComponent
};
//# sourceMappingURL=chunk-Z5HUKYMI.js.map
