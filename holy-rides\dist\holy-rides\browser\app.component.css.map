{"version": 3, "sources": ["src/app/app.component.scss"], "sourcesContent": [".spacer {\r\n  flex: 1 1 auto;\r\n}\r\n\r\nmat-toolbar {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.logo-container {\r\n  display: flex;\r\n  align-items: center;\r\n  text-decoration: none;\r\n  color: white;\r\n  cursor: pointer;\r\n}\r\n\r\n.logo {\r\n  height: 40px;\r\n  margin-right: 10px;\r\n  filter: brightness(0) invert(1); /* This will make the logo white */\r\n}\r\n\r\n.notification-prompt {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 999;\r\n  padding: 16px;\r\n}\r\n\r\n.notification-prompt mat-card {\r\n  background-color: white;\r\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.notification-prompt h3 {\r\n  margin: 0 0 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.notification-prompt p {\r\n  margin: 0 0 16px;\r\n  color: rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.notification-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}"], "mappings": ";AAAA,CAAA;AACE,QAAA,EAAA,EAAA;;AAGF;AACE,iBAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,SAAA;AACA,UAAA;;AAGF,CAAA;AACE,UAAA;AACA,gBAAA;AACA,UAAA,WAAA,GAAA,OAAA;;AAGF,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,WAAA;AACA,WAAA;;AAGF,CATA,oBASA;AACE,oBAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAdA,oBAcA;AACE,UAAA,EAAA,EAAA;AACA,aAAA;;AAGF,CAnBA,oBAmBA;AACE,UAAA,EAAA,EAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;;", "names": []}