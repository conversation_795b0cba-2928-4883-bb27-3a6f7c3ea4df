{"version": 3, "sources": ["src/app/features/dashboard/rider/ride-request/ride-request.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    form {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      max-width: 600px;\n      margin: 0 auto;\n    }\n\n    .location-fields {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n    }\n\n    .button-container {\n      display: flex;\n      justify-content: center;\n      margin-top: 16px;\n    }\n\n    textarea {\n      min-height: 100px;\n    }\n\n    .fare-estimate {\n      background-color: #f5f5f5;\n      padding: 16px;\n      border-radius: 4px;\n      margin-top: 16px;\n      margin-bottom: 16px;\n    }\n\n    .fare-estimate p {\n      margin: 8px 0;\n    }\n  "], "mappings": ";AACI;AACE,WAAA;AACA,UAAA;;AAGF;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,aAAA;AACA,UAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;AAGF;AACE,cAAA;;AAGF,CAAA;AACE,oBAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA;AACA,iBAAA;;AAGF,CARA,cAQA;AACE,UAAA,IAAA;;", "names": []}