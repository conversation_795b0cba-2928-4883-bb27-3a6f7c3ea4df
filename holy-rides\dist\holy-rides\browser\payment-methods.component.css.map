{"version": 3, "sources": ["src/app/features/dashboard/rider/payment-methods/payment-methods.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .no-methods {\n      text-align: center;\n      padding: 20px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .payment-method-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      width: 100%;\n      padding: 8px 0;\n    }\n\n    .payment-method-info {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .expiry {\n      color: rgba(0, 0, 0, 0.6);\n      font-size: 0.9em;\n      margin-left: 8px;\n    }\n\n    .default-badge {\n      background-color: #4caf50;\n      color: white;\n      padding: 2px 8px;\n      border-radius: 12px;\n      font-size: 0.8em;\n      margin-left: 8px;\n    }\n\n    .divider {\n      margin: 20px 0;\n    }\n\n    .add-payment-section {\n      margin-top: 20px;\n    }\n\n    form {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      max-width: 500px;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 16px;\n    }\n\n    .form-row mat-form-field {\n      flex: 1;\n    }\n\n    .button-container {\n      display: flex;\n      justify-content: flex-end;\n      margin-top: 16px;\n    }\n  "], "mappings": ";AACI;AACE,WAAA;AACA,UAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,SAAA;AACA,WAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAAA;AACE,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,oBAAA;AACA,SAAA;AACA,WAAA,IAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAGF,CAAA;AACE,UAAA,KAAA;;AAGF,CAAA;AACE,cAAA;;AAGF;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,aAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CALA,SAKA;AACE,QAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,cAAA;;", "names": []}