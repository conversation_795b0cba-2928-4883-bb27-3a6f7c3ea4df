import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MatPaginatorModule,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ort<PERSON><PERSON>er,
  MatSortModule
} from "./chunk-DLJTV7TI.js";
import {
  MatBadgeModule
} from "./chunk-22KA3K3Q.js";
import {
  Mat<PERSON><PERSON>rdion,
  Mat<PERSON>ell,
  MatCellDef,
  MatChipsModule,
  MatColumnDef,
  MatExpansionModule,
  MatExpansionPanel,
  MatExpansionPanelActionRow,
  MatExpansionPanelDescription,
  MatExpansionPanelHeader,
  MatExpansionPanelTitle,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow,
  MatHeaderRowDef,
  MatRow,
  MatRowDef,
  MatTab,
  MatTabGroup,
  MatTable,
  MatTableDataSource,
  MatTableModule,
  MatTabsModule,
  MatTooltip,
  MatTooltipModule,
  PaymentService,
  RideDetailComponent
} from "./chunk-WNL2OBGV.js";
import {
  MatDividerModule,
  Mat<PERSON><PERSON>ress<PERSON>pin<PERSON>,
  Mat<PERSON>rogressSpinnerModule,
  RideService
} from "./chunk-PSNUENZ6.js";
import {
  LocationService,
  MapDisplayComponent
} from "./chunk-ZBUZHDSF.js";
import {
  MessageService
} from "./chunk-XQHBKW3P.js";
import "./chunk-ZG6RKMCP.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-R24BZZME.js";
import "./chunk-NVGZCMKL.js";
import "./chunk-GWPOTN5B.js";
import "./chunk-5DER6JXC.js";
import {
  MatSnackBar,
  MatSnackBarModule
} from "./chunk-MSLYEYGK.js";
import "./chunk-LTJSKJGW.js";
import "./chunk-QIPXWAWB.js";
import {
  Router
} from "./chunk-MM3GCGFU.js";
import {
  AuthService,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MatError,
  MatFormField,
  MatFormFieldModule,
  MatInput,
  MatInputModule,
  MatLabel,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-QNBL54OW.js";
import {
  BehaviorSubject,
  CommonModule,
  Component,
  CurrencyPipe,
  DatePipe,
  EventEmitter,
  Injectable,
  Input,
  MatButton,
  MatButtonModule,
  MatCard,
  MatCardContent,
  MatCardHeader,
  MatCardModule,
  MatCardTitle,
  MatIconButton,
  NgClass,
  NgForOf,
  NgIf,
  Output,
  SlicePipe,
  ViewChild,
  computed,
  effect,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵpipeBind3,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵviewQuery
} from "./chunk-THPQGTPB.js";
import "./chunk-V72RMYHE.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-S35DAJRX.js";

// src/app/core/services/vehicle.service.ts
var VehicleService = class _VehicleService {
  authService;
  supabase;
  vehiclesSubject = new BehaviorSubject([]);
  vehicles$ = this.vehiclesSubject.asObservable();
  constructor(authService) {
    this.authService = authService;
    this.supabase = authService.supabase;
    this.initializeMockVehicles();
  }
  initializeMockVehicles() {
    const mockVehicles = [
      {
        id: "1",
        driver_id: "driver1",
        make: "Toyota",
        model: "Camry",
        year: 2020,
        color: "Silver",
        license_plate: "ABC123",
        capacity: 4,
        created_at: (/* @__PURE__ */ new Date()).toISOString(),
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      },
      {
        id: "2",
        driver_id: "driver2",
        make: "Honda",
        model: "Accord",
        year: 2019,
        color: "Black",
        license_plate: "XYZ789",
        capacity: 5,
        created_at: (/* @__PURE__ */ new Date()).toISOString(),
        updated_at: (/* @__PURE__ */ new Date()).toISOString()
      }
    ];
    this.vehiclesSubject.next(mockVehicles);
  }
  getDriverVehicles(driverId) {
    return __async(this, null, function* () {
      try {
        return this.vehiclesSubject.value.filter((vehicle) => vehicle.driver_id === driverId);
      } catch (error) {
        console.error("Error fetching driver vehicles:", error);
        return [];
      }
    });
  }
  addVehicle(vehicle) {
    return __async(this, null, function* () {
      try {
        const newVehicle = __spreadProps(__spreadValues({}, vehicle), {
          id: Math.random().toString(36).substring(2, 9),
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        });
        const currentVehicles = this.vehiclesSubject.value;
        this.vehiclesSubject.next([...currentVehicles, newVehicle]);
        return newVehicle;
      } catch (error) {
        console.error("Error adding vehicle:", error);
        return null;
      }
    });
  }
  updateVehicle(id, updates) {
    return __async(this, null, function* () {
      try {
        const currentVehicles = this.vehiclesSubject.value;
        const updatedVehicles = currentVehicles.map((vehicle) => vehicle.id === id ? __spreadProps(__spreadValues(__spreadValues({}, vehicle), updates), {
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }) : vehicle);
        this.vehiclesSubject.next(updatedVehicles);
        return true;
      } catch (error) {
        console.error("Error updating vehicle:", error);
        return false;
      }
    });
  }
  deleteVehicle(id) {
    return __async(this, null, function* () {
      try {
        const currentVehicles = this.vehiclesSubject.value;
        const updatedVehicles = currentVehicles.filter((vehicle) => vehicle.id !== id);
        this.vehiclesSubject.next(updatedVehicles);
        return true;
      } catch (error) {
        console.error("Error deleting vehicle:", error);
        return false;
      }
    });
  }
  static \u0275fac = function VehicleService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _VehicleService)(\u0275\u0275inject(AuthService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _VehicleService, factory: _VehicleService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VehicleService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }], null);
})();

// src/app/features/dashboard/driver/driver-profile/driver-profile.component.ts
function DriverProfileComponent_mat_error_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Full name is required ");
    \u0275\u0275elementEnd();
  }
}
function DriverProfileComponent_mat_error_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "mat-error");
    \u0275\u0275text(1, " Phone number is required ");
    \u0275\u0275elementEnd();
  }
}
var DriverProfileComponent = class _DriverProfileComponent {
  fb;
  authService;
  vehicleService;
  snackBar;
  profileForm;
  vehicleForm;
  vehicles = [];
  currentUser = null;
  editingVehicle = null;
  currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  constructor(fb, authService, vehicleService, snackBar) {
    this.fb = fb;
    this.authService = authService;
    this.vehicleService = vehicleService;
    this.snackBar = snackBar;
    this.profileForm = this.fb.group({
      full_name: ["", Validators.required],
      phone: ["", Validators.required]
    });
    this.vehicleForm = this.fb.group({
      make: ["", Validators.required],
      model: ["", Validators.required],
      year: ["", [Validators.required, Validators.min(1990), Validators.max(this.currentYear)]],
      color: ["", Validators.required],
      license_plate: ["", Validators.required],
      capacity: [4, [Validators.required, Validators.min(1), Validators.max(10)]]
    });
  }
  ngOnInit() {
    this.loadUserProfile();
    this.loadVehicles();
  }
  loadUserProfile() {
    return __async(this, null, function* () {
      try {
        this.currentUser = yield this.authService.getCurrentUser();
        if (this.currentUser) {
          this.profileForm.patchValue({
            full_name: this.currentUser.full_name || "",
            phone: this.currentUser.phone || ""
          });
        }
      } catch (error) {
        console.error("Error loading user profile:", error);
        this.snackBar.open("Failed to load profile", "Close", { duration: 3e3 });
      }
    });
  }
  loadVehicles() {
    return __async(this, null, function* () {
      try {
        if (this.currentUser) {
          this.vehicles = yield this.vehicleService.getDriverVehicles(this.currentUser.id);
        }
      } catch (error) {
        console.error("Error loading vehicles:", error);
        this.snackBar.open("Failed to load vehicles", "Close", { duration: 3e3 });
      }
    });
  }
  updateProfile() {
    return __async(this, null, function* () {
      if (this.profileForm.invalid)
        return;
      try {
        const success = yield this.authService.updateProfile(this.profileForm.value);
        if (success) {
          this.snackBar.open("Profile updated successfully", "Close", { duration: 3e3 });
        } else {
          throw new Error("Failed to update profile");
        }
      } catch (error) {
        console.error("Error updating profile:", error);
        this.snackBar.open("Failed to update profile", "Close", { duration: 3e3 });
      }
    });
  }
  editVehicle(vehicle) {
    this.editingVehicle = vehicle;
    this.vehicleForm.patchValue({
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      color: vehicle.color,
      license_plate: vehicle.license_plate,
      capacity: vehicle.capacity
    });
  }
  cancelEdit() {
    this.editingVehicle = null;
    this.vehicleForm.reset({
      capacity: 4
    });
  }
  saveVehicle() {
    return __async(this, null, function* () {
      if (this.vehicleForm.invalid || !this.currentUser)
        return;
      try {
        if (this.editingVehicle) {
          const success = yield this.vehicleService.updateVehicle(this.editingVehicle.id, this.vehicleForm.value);
          if (success) {
            this.snackBar.open("Vehicle updated successfully", "Close", { duration: 3e3 });
            this.cancelEdit();
            this.loadVehicles();
          } else {
            throw new Error("Failed to update vehicle");
          }
        } else {
          const newVehicle = __spreadProps(__spreadValues({}, this.vehicleForm.value), {
            driver_id: this.currentUser.id
          });
          const result = yield this.vehicleService.addVehicle(newVehicle);
          if (result) {
            this.snackBar.open("Vehicle added successfully", "Close", { duration: 3e3 });
            this.vehicleForm.reset({
              capacity: 4
            });
            this.loadVehicles();
          } else {
            throw new Error("Failed to add vehicle");
          }
        }
      } catch (error) {
        console.error("Error saving vehicle:", error);
        this.snackBar.open("Failed to save vehicle", "Close", { duration: 3e3 });
      }
    });
  }
  deleteVehicle(vehicleId) {
    return __async(this, null, function* () {
      try {
        const success = yield this.vehicleService.deleteVehicle(vehicleId);
        if (success) {
          this.snackBar.open("Vehicle deleted successfully", "Close", { duration: 3e3 });
          this.loadVehicles();
        } else {
          throw new Error("Failed to delete vehicle");
        }
      } catch (error) {
        console.error("Error deleting vehicle:", error);
        this.snackBar.open("Failed to delete vehicle", "Close", { duration: 3e3 });
      }
    });
  }
  static \u0275fac = function DriverProfileComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DriverProfileComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(VehicleService), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DriverProfileComponent, selectors: [["app-driver-profile"]], decls: 19, vars: 4, consts: [[1, "profile-container"], [3, "ngSubmit", "formGroup"], ["appearance", "outline", 1, "full-width"], ["matInput", "", "formControlName", "full_name", "placeholder", "Enter your full name"], [4, "ngIf"], ["matInput", "", "formControlName", "phone", "placeholder", "Enter your phone number"], ["mat-raised-button", "", "color", "primary", "type", "submit", 3, "disabled"]], template: function DriverProfileComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card")(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Driver Profile");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "form", 1);
      \u0275\u0275listener("ngSubmit", function DriverProfileComponent_Template_form_ngSubmit_6_listener() {
        return ctx.updateProfile();
      });
      \u0275\u0275elementStart(7, "mat-form-field", 2)(8, "mat-label");
      \u0275\u0275text(9, "Full Name");
      \u0275\u0275elementEnd();
      \u0275\u0275element(10, "input", 3);
      \u0275\u0275template(11, DriverProfileComponent_mat_error_11_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "mat-form-field", 2)(13, "mat-label");
      \u0275\u0275text(14, "Phone Number");
      \u0275\u0275elementEnd();
      \u0275\u0275element(15, "input", 5);
      \u0275\u0275template(16, DriverProfileComponent_mat_error_16_Template, 2, 0, "mat-error", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "button", 6);
      \u0275\u0275text(18, " Update Profile ");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      let tmp_1_0;
      let tmp_2_0;
      \u0275\u0275advance(6);
      \u0275\u0275property("formGroup", ctx.profileForm);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_1_0 = ctx.profileForm.get("full_name")) == null ? null : tmp_1_0.hasError("required"));
      \u0275\u0275advance(5);
      \u0275\u0275property("ngIf", (tmp_2_0 = ctx.profileForm.get("phone")) == null ? null : tmp_2_0.hasError("required"));
      \u0275\u0275advance();
      \u0275\u0275property("disabled", ctx.profileForm.invalid || ctx.profileForm.pristine);
    }
  }, dependencies: [
    CommonModule,
    NgIf,
    ReactiveFormsModule,
    \u0275NgNoValidate,
    DefaultValueAccessor,
    NgControlStatus,
    NgControlStatusGroup,
    FormGroupDirective,
    FormControlName,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatFormFieldModule,
    MatFormField,
    MatLabel,
    MatError,
    MatInputModule,
    MatInput,
    MatButtonModule,
    MatButton,
    MatSnackBarModule,
    MatDividerModule,
    MatIconModule
  ], styles: ["\n\n.profile-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n.full-width[_ngcontent-%COMP%] {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.vehicle-card[_ngcontent-%COMP%] {\n  margin-top: 20px;\n}\n.vehicle-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 0;\n  border-bottom: 1px solid #eee;\n}\n.vehicle-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.vehicle-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  font-weight: 500;\n}\n.vehicle-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n  color: #666;\n}\n.vehicle-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n}\n.divider[_ngcontent-%COMP%] {\n  margin: 16px 0;\n}\n.vehicle-form[_ngcontent-%COMP%] {\n  margin-top: 16px;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 8px;\n}\n.form-row[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n}\n.no-vehicles[_ngcontent-%COMP%] {\n  padding: 16px 0;\n  color: #666;\n  font-style: italic;\n}\n/*# sourceMappingURL=driver-profile.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DriverProfileComponent, [{
    type: Component,
    args: [{ selector: "app-driver-profile", standalone: true, imports: [
      CommonModule,
      ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatInputModule,
      MatButtonModule,
      MatSnackBarModule,
      MatDividerModule,
      MatIconModule
    ], template: `
    <div class="profile-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Driver Profile</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="full_name" placeholder="Enter your full name">
              <mat-error *ngIf="profileForm.get('full_name')?.hasError('required')">
                Full name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phone" placeholder="Enter your phone number">
              <mat-error *ngIf="profileForm.get('phone')?.hasError('required')">
                Phone number is required
              </mat-error>
            </mat-form-field>

            <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || profileForm.pristine">
              Update Profile
            </button>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- <mat-card class="vehicle-card">
        <mat-card-header>
          <mat-card-title>Vehicle Information</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="vehicles.length === 0" class="no-vehicles">
            <p>No vehicles added yet.</p>
          </div>

          <div *ngFor="let vehicle of vehicles" class="vehicle-item">
            <div class="vehicle-details">
              <h3>{{ vehicle.year }} {{ vehicle.make }} {{ vehicle.model }}</h3>
              <p>Color: {{ vehicle.color }}</p>
              <p>License Plate: {{ vehicle.license_plate }}</p>
              <p>Passenger Capacity: {{ vehicle.capacity }}</p>
            </div>
            <div class="vehicle-actions">
              <button mat-icon-button color="primary" (click)="editVehicle(vehicle)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteVehicle(vehicle.id)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>

          <mat-divider *ngIf="vehicles.length > 0" class="divider"></mat-divider>

          <form [formGroup]="vehicleForm" (ngSubmit)="saveVehicle()" class="vehicle-form">
            <h3>{{ editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle' }}</h3>
            
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Make</mat-label>
                <input matInput formControlName="make" placeholder="e.g. Toyota">
                <mat-error *ngIf="vehicleForm.get('make')?.hasError('required')">
                  Make is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Model</mat-label>
                <input matInput formControlName="model" placeholder="e.g. Camry">
                <mat-error *ngIf="vehicleForm.get('model')?.hasError('required')">
                  Model is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Year</mat-label>
                <input matInput formControlName="year" type="number" placeholder="e.g. 2020">
                <mat-error *ngIf="vehicleForm.get('year')?.hasError('required')">
                  Year is required
                </mat-error>
                <mat-error *ngIf="vehicleForm.get('year')?.hasError('min') || vehicleForm.get('year')?.hasError('max')">
                  Year must be between 1990 and {{ currentYear }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Color</mat-label>
                <input matInput formControlName="color" placeholder="e.g. Silver">
                <mat-error *ngIf="vehicleForm.get('color')?.hasError('required')">
                  Color is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>License Plate</mat-label>
                <input matInput formControlName="license_plate" placeholder="e.g. ABC123">
                <mat-error *ngIf="vehicleForm.get('license_plate')?.hasError('required')">
                  License plate is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Passenger Capacity</mat-label>
                <input matInput formControlName="capacity" type="number" placeholder="e.g. 4">
                <mat-error *ngIf="vehicleForm.get('capacity')?.hasError('required')">
                  Capacity is required
                </mat-error>
                <mat-error *ngIf="vehicleForm.get('capacity')?.hasError('min') || vehicleForm.get('capacity')?.hasError('max')">
                  Capacity must be between 1 and 10
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="vehicleForm.invalid">
                {{ editingVehicle ? 'Update Vehicle' : 'Add Vehicle' }}
              </button>
              <button mat-button type="button" *ngIf="editingVehicle" (click)="cancelEdit()">
                Cancel
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card> -->
    </div>
  `, styles: ["/* angular:styles/component:scss;cd08a256ff677efd795bd333e485bc012c1fa9536787b3a8ae7b76a0ac31d6bb;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/driver/driver-profile/driver-profile.component.ts */\n.profile-container {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n.full-width {\n  width: 100%;\n  margin-bottom: 16px;\n}\n.vehicle-card {\n  margin-top: 20px;\n}\n.vehicle-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 0;\n  border-bottom: 1px solid #eee;\n}\n.vehicle-item:last-child {\n  border-bottom: none;\n}\n.vehicle-details h3 {\n  margin: 0 0 8px 0;\n  font-weight: 500;\n}\n.vehicle-details p {\n  margin: 4px 0;\n  color: #666;\n}\n.vehicle-actions {\n  display: flex;\n  gap: 8px;\n}\n.divider {\n  margin: 16px 0;\n}\n.vehicle-form {\n  margin-top: 16px;\n}\n.form-row {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 8px;\n}\n.form-row mat-form-field {\n  flex: 1;\n}\n.form-actions {\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n}\n.no-vehicles {\n  padding: 16px 0;\n  color: #666;\n  font-style: italic;\n}\n/*# sourceMappingURL=driver-profile.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: VehicleService }, { type: MatSnackBar }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DriverProfileComponent, { className: "DriverProfileComponent", filePath: "src/app/features/dashboard/driver/driver-profile/driver-profile.component.ts", lineNumber: 239 });
})();

// src/app/features/dashboard/driver/driver-earnings/driver-earnings.component.ts
function DriverEarningsComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11);
    \u0275\u0275element(1, "mat-spinner", 12);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading payouts...");
    \u0275\u0275elementEnd()();
  }
}
function DriverEarningsComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 13)(1, "p");
    \u0275\u0275text(2, "No payouts found.");
    \u0275\u0275elementEnd()();
  }
}
function DriverEarningsComponent_table_32_th_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 23);
    \u0275\u0275text(1, "Date");
    \u0275\u0275elementEnd();
  }
}
function DriverEarningsComponent_table_32_td_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 24);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const payout_r1 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(2, 1, payout_r1.created_at, "medium"));
  }
}
function DriverEarningsComponent_table_32_th_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 23);
    \u0275\u0275text(1, "Ride ID");
    \u0275\u0275elementEnd();
  }
}
function DriverEarningsComponent_table_32_td_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 24);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "slice");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const payout_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", \u0275\u0275pipeBind3(2, 1, payout_r2.ride_id, 0, 8), "...");
  }
}
function DriverEarningsComponent_table_32_th_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 23);
    \u0275\u0275text(1, "Amount");
    \u0275\u0275elementEnd();
  }
}
function DriverEarningsComponent_table_32_td_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 24);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const payout_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(payout_r3.amount.toFixed(2));
  }
}
function DriverEarningsComponent_table_32_th_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 23);
    \u0275\u0275text(1, "Status");
    \u0275\u0275elementEnd();
  }
}
function DriverEarningsComponent_table_32_td_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 24)(1, "span", 25);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const payout_r4 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", "status-" + payout_r4.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", payout_r4.status, " ");
  }
}
function DriverEarningsComponent_table_32_tr_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 26);
  }
}
function DriverEarningsComponent_table_32_tr_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 27);
  }
}
function DriverEarningsComponent_table_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "table", 14);
    \u0275\u0275elementContainerStart(1, 15);
    \u0275\u0275template(2, DriverEarningsComponent_table_32_th_2_Template, 2, 0, "th", 16)(3, DriverEarningsComponent_table_32_td_3_Template, 3, 4, "td", 17);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(4, 18);
    \u0275\u0275template(5, DriverEarningsComponent_table_32_th_5_Template, 2, 0, "th", 16)(6, DriverEarningsComponent_table_32_td_6_Template, 3, 5, "td", 17);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(7, 19);
    \u0275\u0275template(8, DriverEarningsComponent_table_32_th_8_Template, 2, 0, "th", 16)(9, DriverEarningsComponent_table_32_td_9_Template, 2, 1, "td", 17);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(10, 20);
    \u0275\u0275template(11, DriverEarningsComponent_table_32_th_11_Template, 2, 0, "th", 16)(12, DriverEarningsComponent_table_32_td_12_Template, 3, 2, "td", 17);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(13, DriverEarningsComponent_table_32_tr_13_Template, 1, 0, "tr", 21)(14, DriverEarningsComponent_table_32_tr_14_Template, 1, 0, "tr", 22);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r4 = \u0275\u0275nextContext();
    \u0275\u0275property("dataSource", ctx_r4.payouts);
    \u0275\u0275advance(13);
    \u0275\u0275property("matHeaderRowDef", ctx_r4.displayedColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r4.displayedColumns);
  }
}
var DriverEarningsComponent = class _DriverEarningsComponent {
  paymentService;
  authService;
  payouts = [];
  displayedColumns = ["date", "ride_id", "amount", "status"];
  loading = false;
  totalEarnings = 0;
  pendingEarnings = 0;
  completedRides = 0;
  constructor(paymentService, authService) {
    this.paymentService = paymentService;
    this.authService = authService;
  }
  ngOnInit() {
    this.loadPayouts();
  }
  loadPayouts() {
    return __async(this, null, function* () {
      this.loading = true;
      try {
        const user = yield this.authService.getCurrentUser();
        if (!user)
          throw new Error("User not found");
        this.payouts = yield this.paymentService.getDriverPayouts(user.id);
        this.totalEarnings = yield this.paymentService.getDriverTotalEarnings(user.id);
        this.pendingEarnings = yield this.paymentService.getDriverPendingEarnings(user.id);
        this.completedRides = this.payouts.length;
      } catch (error) {
        console.error("Error loading payouts:", error);
      } finally {
        this.loading = false;
      }
    });
  }
  static \u0275fac = function DriverEarningsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DriverEarningsComponent)(\u0275\u0275directiveInject(PaymentService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DriverEarningsComponent, selectors: [["app-driver-earnings"]], decls: 33, vars: 6, consts: [[1, "earnings-container"], [1, "summary-card"], [1, "summary-grid"], [1, "summary-item"], [1, "summary-label"], [1, "summary-value"], [1, "payouts-card"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Refresh payouts", 3, "click"], ["class", "loading-container", 4, "ngIf"], ["class", "no-payouts", 4, "ngIf"], ["mat-table", "", "class", "payouts-table", 3, "dataSource", 4, "ngIf"], [1, "loading-container"], ["diameter", "40"], [1, "no-payouts"], ["mat-table", "", 1, "payouts-table", 3, "dataSource"], ["matColumnDef", "date"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "ride_id"], ["matColumnDef", "amount"], ["matColumnDef", "status"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["mat-header-cell", ""], ["mat-cell", ""], [1, "status-chip", 3, "ngClass"], ["mat-header-row", ""], ["mat-row", ""]], template: function DriverEarningsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Earnings Summary");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "div", 2)(7, "div", 3)(8, "div", 4);
      \u0275\u0275text(9, "Total Earnings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 5);
      \u0275\u0275text(11);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "div", 3)(13, "div", 4);
      \u0275\u0275text(14, "Pending Payouts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "div", 5);
      \u0275\u0275text(16);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "div", 3)(18, "div", 4);
      \u0275\u0275text(19, "Completed Rides");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "div", 5);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(22, "mat-card", 6)(23, "mat-card-header")(24, "mat-card-title");
      \u0275\u0275text(25, "Payout History");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "button", 7);
      \u0275\u0275listener("click", function DriverEarningsComponent_Template_button_click_26_listener() {
        return ctx.loadPayouts();
      });
      \u0275\u0275elementStart(27, "mat-icon");
      \u0275\u0275text(28, "refresh");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(29, "mat-card-content");
      \u0275\u0275template(30, DriverEarningsComponent_div_30_Template, 4, 0, "div", 8)(31, DriverEarningsComponent_div_31_Template, 3, 0, "div", 9)(32, DriverEarningsComponent_table_32_Template, 15, 3, "table", 10);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(11);
      \u0275\u0275textInterpolate(ctx.totalEarnings.toFixed(2));
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.pendingEarnings.toFixed(2));
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.completedRides);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.payouts.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.payouts.length > 0);
    }
  }, dependencies: [
    CommonModule,
    NgClass,
    NgIf,
    SlicePipe,
    DatePipe,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatChipsModule,
    MatIconModule,
    MatIcon,
    MatButtonModule,
    MatIconButton,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    MatTooltipModule,
    MatTooltip
  ], styles: ["\n\n.earnings-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.summary-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-top: 16px;\n}\n.summary-item[_ngcontent-%COMP%] {\n  background-color: #f5f5f5;\n  border-radius: 8px;\n  padding: 16px;\n  text-align: center;\n}\n.summary-label[_ngcontent-%COMP%] {\n  font-size: 0.9em;\n  color: rgba(0, 0, 0, 0.6);\n  margin-bottom: 8px;\n}\n.summary-value[_ngcontent-%COMP%] {\n  font-size: 1.8em;\n  font-weight: 500;\n  color: #3f51b5;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n.no-payouts[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 20px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.payouts-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.status-chip[_ngcontent-%COMP%] {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 16px;\n  font-size: 0.85em;\n  text-transform: capitalize;\n}\n.status-pending[_ngcontent-%COMP%] {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.status-paid[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n  color: white;\n}\n.status-failed[_ngcontent-%COMP%] {\n  background-color: #f44336;\n  color: white;\n}\n/*# sourceMappingURL=driver-earnings.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DriverEarningsComponent, [{
    type: Component,
    args: [{ selector: "app-driver-earnings", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatTableModule,
      MatChipsModule,
      MatIconModule,
      MatButtonModule,
      MatProgressSpinnerModule,
      MatTooltipModule
    ], template: `<div class="earnings-container">
      <mat-card class="summary-card">
        <mat-card-header>
          <mat-card-title>Earnings Summary</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-label">Total Earnings</div>
              <div class="summary-value">{{ totalEarnings.toFixed(2) }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">Pending Payouts</div>
              <div class="summary-value">{{ pendingEarnings.toFixed(2) }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">Completed Rides</div>
              <div class="summary-value">{{ completedRides }}</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="payouts-card">
        <mat-card-header>
          <mat-card-title>Payout History</mat-card-title>
          <button mat-icon-button color="primary" (click)="loadPayouts()" matTooltip="Refresh payouts">
            <mat-icon>refresh</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading payouts...</p>
          </div>

          <div *ngIf="!loading && payouts.length === 0" class="no-payouts">
            <p>No payouts found.</p>
          </div>

          <table mat-table [dataSource]="payouts" class="payouts-table" *ngIf="!loading && payouts.length > 0">
            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef>Date</th>
              <td mat-cell *matCellDef="let payout">{{ payout.created_at | date:'medium' }}</td>
            </ng-container>

            <ng-container matColumnDef="ride_id">
              <th mat-header-cell *matHeaderCellDef>Ride ID</th>
              <td mat-cell *matCellDef="let payout">{{ payout.ride_id | slice:0:8 }}...</td>
            </ng-container>

            <ng-container matColumnDef="amount">
              <th mat-header-cell *matHeaderCellDef>Amount</th>
              <td mat-cell *matCellDef="let payout">{{ payout.amount.toFixed(2) }}</td>
            </ng-container>

            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let payout">
                <span class="status-chip" [ngClass]="'status-' + payout.status">
                  {{ payout.status }}
                </span>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </mat-card-content>
      </mat-card>
    </div>`, styles: ["/* angular:styles/component:scss;4b682e020860ae1f8cddb5f90650ee0a6706c126bdd5a4a531414af4f91c081b;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/driver/driver-earnings/driver-earnings.component.ts */\n.earnings-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n.summary-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-top: 16px;\n}\n.summary-item {\n  background-color: #f5f5f5;\n  border-radius: 8px;\n  padding: 16px;\n  text-align: center;\n}\n.summary-label {\n  font-size: 0.9em;\n  color: rgba(0, 0, 0, 0.6);\n  margin-bottom: 8px;\n}\n.summary-value {\n  font-size: 1.8em;\n  font-weight: 500;\n  color: #3f51b5;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n.no-payouts {\n  text-align: center;\n  padding: 20px;\n  color: rgba(0, 0, 0, 0.6);\n}\n.payouts-table {\n  width: 100%;\n}\n.status-chip {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 16px;\n  font-size: 0.85em;\n  text-transform: capitalize;\n}\n.status-pending {\n  background-color: #ffeb3b;\n  color: #000;\n}\n.status-paid {\n  background-color: #4caf50;\n  color: white;\n}\n.status-failed {\n  background-color: #f44336;\n  color: white;\n}\n/*# sourceMappingURL=driver-earnings.component.css.map */\n"] }]
  }], () => [{ type: PaymentService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DriverEarningsComponent, { className: "DriverEarningsComponent", filePath: "src/app/features/dashboard/driver/driver-earnings/driver-earnings.component.ts", lineNumber: 106 });
})();

// src/app/features/dashboard/driver/ride-navigation/ride-navigation.component.ts
function RideNavigationComponent_div_0_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18)(1, "p")(2, "strong");
    \u0275\u0275text(3, "Distance:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p")(6, "strong");
    \u0275\u0275text(7, "Estimated Time:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r1.ride.distance_miles, " miles");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r1.ride.duration_minutes, " minutes");
  }
}
function RideNavigationComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 1)(1, "mat-card", 2)(2, "mat-card-header")(3, "mat-card-title");
    \u0275\u0275text(4, "Navigation");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 3);
    \u0275\u0275listener("click", function RideNavigationComponent_div_0_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeNavigation());
    });
    \u0275\u0275elementStart(6, "mat-icon");
    \u0275\u0275text(7, "close");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(8, "mat-card-content")(9, "div", 4)(10, "div", 5)(11, "div", 6)(12, "mat-icon", 7);
    \u0275\u0275text(13, "location_on");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "div", 8)(15, "span", 9);
    \u0275\u0275text(16, "Pickup Location:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "span", 10);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(19, "div", 6)(20, "mat-icon", 11);
    \u0275\u0275text(21, "flag");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(22, "div", 8)(23, "span", 9);
    \u0275\u0275text(24, "Dropoff Location:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "span", 10);
    \u0275\u0275text(26);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275element(27, "app-map-display", 12);
    \u0275\u0275template(28, RideNavigationComponent_div_0_div_28_Template, 9, 2, "div", 13);
    \u0275\u0275elementStart(29, "div", 14)(30, "a", 15)(31, "button", 16)(32, "mat-icon");
    \u0275\u0275text(33, "navigation");
    \u0275\u0275elementEnd();
    \u0275\u0275text(34, " Navigate to Pickup ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(35, "a", 15)(36, "button", 17)(37, "mat-icon");
    \u0275\u0275text(38, "navigation");
    \u0275\u0275elementEnd();
    \u0275\u0275text(39, " Navigate to Dropoff ");
    \u0275\u0275elementEnd()()()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(18);
    \u0275\u0275textInterpolate(ctx_r1.ride.pickup_location);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r1.ride.dropoff_location);
    \u0275\u0275advance();
    \u0275\u0275property("origin", ctx_r1.ride.pickup_location)("destination", ctx_r1.ride.dropoff_location);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.ride.distance_miles && ctx_r1.ride.duration_minutes);
    \u0275\u0275advance(2);
    \u0275\u0275property("href", ctx_r1.googleMapsPickupUrl, \u0275\u0275sanitizeUrl);
    \u0275\u0275advance(5);
    \u0275\u0275property("href", ctx_r1.googleMapsDropoffUrl, \u0275\u0275sanitizeUrl);
  }
}
var RideNavigationComponent = class _RideNavigationComponent {
  locationService;
  ride = null;
  close = new EventEmitter();
  googleMapsPickupUrl = "";
  googleMapsDropoffUrl = "";
  constructor(locationService) {
    this.locationService = locationService;
  }
  ngOnInit() {
    this.generateNavigationLinks();
  }
  generateNavigationLinks() {
    if (!this.ride)
      return;
    this.googleMapsPickupUrl = this.locationService.getGoogleMapsUrl(this.ride.pickup_location);
    this.googleMapsDropoffUrl = this.locationService.getGoogleMapsUrl(this.ride.dropoff_location);
  }
  closeNavigation() {
    this.close.emit();
  }
  static \u0275fac = function RideNavigationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RideNavigationComponent)(\u0275\u0275directiveInject(LocationService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RideNavigationComponent, selectors: [["app-ride-navigation"]], inputs: { ride: "ride" }, outputs: { close: "close" }, decls: 1, vars: 1, consts: [["class", "navigation-overlay", 4, "ngIf"], [1, "navigation-overlay"], [1, "navigation-card"], ["mat-icon-button", "", 1, "close-button", 3, "click"], [1, "navigation-details"], [1, "location-info"], [1, "location-item"], [1, "location-icon", "pickup"], [1, "location-text"], [1, "location-label"], [1, "location-value"], [1, "location-icon", "dropoff"], [3, "origin", "destination"], ["class", "route-info", 4, "ngIf"], [1, "navigation-links"], ["target", "_blank", 1, "nav-link", 3, "href"], ["mat-raised-button", "", "color", "primary"], ["mat-raised-button", "", "color", "accent"], [1, "route-info"]], template: function RideNavigationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275template(0, RideNavigationComponent_div_0_Template, 40, 7, "div", 0);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.ride);
    }
  }, dependencies: [CommonModule, NgIf, MatCardModule, MatCard, MatCardContent, MatCardHeader, MatCardTitle, MatButtonModule, MatButton, MatIconButton, MatIconModule, MatIcon, MapDisplayComponent], styles: ["\n\n.navigation-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.navigation-card[_ngcontent-%COMP%] {\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n.close-button[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 8px;\n  top: 8px;\n}\n.navigation-details[_ngcontent-%COMP%] {\n  padding: 16px 0;\n}\n.location-info[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\n.location-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n.location-icon[_ngcontent-%COMP%] {\n  margin-right: 16px;\n  color: #3f51b5;\n}\n.location-icon.pickup[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n.location-icon.dropoff[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n.location-text[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.location-label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n.location-value[_ngcontent-%COMP%] {\n  color: #666;\n}\n.route-info[_ngcontent-%COMP%] {\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  padding: 16px;\n  margin-bottom: 24px;\n}\n.route-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\n.navigation-links[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-around;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n.nav-link[_ngcontent-%COMP%] {\n  text-decoration: none;\n}\n/*# sourceMappingURL=ride-navigation.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RideNavigationComponent, [{
    type: Component,
    args: [{ selector: "app-ride-navigation", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MapDisplayComponent
    ], template: `
    <div class="navigation-overlay" *ngIf="ride">
      <mat-card class="navigation-card">
        <mat-card-header>
          <mat-card-title>Navigation</mat-card-title>
          <button mat-icon-button class="close-button" (click)="closeNavigation()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="navigation-details">
            <div class="location-info">
              <div class="location-item">
                <mat-icon class="location-icon pickup">location_on</mat-icon>
                <div class="location-text">
                  <span class="location-label">Pickup Location:</span>
                  <span class="location-value">{{ ride.pickup_location }}</span>
                </div>
              </div>
              <div class="location-item">
                <mat-icon class="location-icon dropoff">flag</mat-icon>
                <div class="location-text">
                  <span class="location-label">Dropoff Location:</span>
                  <span class="location-value">{{ ride.dropoff_location }}</span>
                </div>
              </div>
            </div>

            <app-map-display
              [origin]="ride.pickup_location"
              [destination]="ride.dropoff_location">
            </app-map-display>

            <div *ngIf="ride.distance_miles && ride.duration_minutes" class="route-info">
              <p><strong>Distance:</strong> {{ ride.distance_miles }} miles</p>
              <p><strong>Estimated Time:</strong> {{ ride.duration_minutes }} minutes</p>
            </div>

            <div class="navigation-links">
              <a [href]="googleMapsPickupUrl" target="_blank" class="nav-link">
                <button mat-raised-button color="primary">
                  <mat-icon>navigation</mat-icon>
                  Navigate to Pickup
                </button>
              </a>
              <a [href]="googleMapsDropoffUrl" target="_blank" class="nav-link">
                <button mat-raised-button color="accent">
                  <mat-icon>navigation</mat-icon>
                  Navigate to Dropoff
                </button>
              </a>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;18757e45353ad74705fbaddc2c2b51e52785d015d0b7ea3d97c17771e00418c5;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/driver/ride-navigation/ride-navigation.component.ts */\n.navigation-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.navigation-card {\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n.close-button {\n  position: absolute;\n  right: 8px;\n  top: 8px;\n}\n.navigation-details {\n  padding: 16px 0;\n}\n.location-info {\n  margin-bottom: 24px;\n}\n.location-item {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 16px;\n}\n.location-icon {\n  margin-right: 16px;\n  color: #3f51b5;\n}\n.location-icon.pickup {\n  color: #4caf50;\n}\n.location-icon.dropoff {\n  color: #f44336;\n}\n.location-text {\n  display: flex;\n  flex-direction: column;\n}\n.location-label {\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n.location-value {\n  color: #666;\n}\n.route-info {\n  background-color: #f5f5f5;\n  border-radius: 4px;\n  padding: 16px;\n  margin-bottom: 24px;\n}\n.route-info p {\n  margin: 8px 0;\n}\n.navigation-links {\n  display: flex;\n  justify-content: space-around;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n.nav-link {\n  text-decoration: none;\n}\n/*# sourceMappingURL=ride-navigation.component.css.map */\n"] }]
  }], () => [{ type: LocationService }], { ride: [{
    type: Input
  }], close: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RideNavigationComponent, { className: "RideNavigationComponent", filePath: "src/app/features/dashboard/driver/ride-navigation/ride-navigation.component.ts", lineNumber: 169 });
})();

// src/app/features/dashboard/driver/ride-assignments/ride-assignments.component.ts
var _c0 = ["availableSort"];
var _c1 = ["availablePaginator"];
var _c2 = ["myRidesSort"];
var _c3 = ["myRidesPaginator"];
var _c4 = () => [3, 5, 10, 25, 100];
var _c5 = () => [5, 10, 25, 100];
function RideAssignmentsComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275element(1, "mat-spinner", 18);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading rides...");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "p");
    \u0275\u0275text(2, "No available ride requests at this time.");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_14_th_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Pickup");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_14_td_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r1 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r1.pickup_location);
  }
}
function RideAssignmentsComponent_ng_container_14_th_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Destination");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_14_td_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r2.dropoff_location);
  }
}
function RideAssignmentsComponent_ng_container_14_th_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Time");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_14_td_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(2, 1, ride_r3.pickup_time, "short"));
  }
}
function RideAssignmentsComponent_ng_container_14_th_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Fare");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_14_td_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "currency");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r4 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r4.fare ? \u0275\u0275pipeBind2(2, 1, ride_r4.fare * 0.7, "USD") : "TBD");
  }
}
function RideAssignmentsComponent_ng_container_14_th_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 38);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_14_td_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 37)(1, "button", 39);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_14_td_18_Template_button_click_1_listener() {
      const ride_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.acceptRide(ride_r6.id));
    });
    \u0275\u0275text(2, " Accept ");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_14_tr_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 40);
  }
}
function RideAssignmentsComponent_ng_container_14_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 41);
  }
}
function RideAssignmentsComponent_ng_container_14_mat_expansion_panel_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-expansion-panel")(1, "mat-expansion-panel-header")(2, "mat-panel-title");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-panel-description");
    \u0275\u0275text(5);
    \u0275\u0275pipe(6, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 42)(8, "p")(9, "strong");
    \u0275\u0275text(10, "To:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "p")(13, "strong");
    \u0275\u0275text(14, "Fare:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(15);
    \u0275\u0275pipe(16, "currency");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "mat-action-row")(18, "button", 39);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_14_mat_expansion_panel_25_Template_button_click_18_listener() {
      const ride_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.acceptRide(ride_r9.id));
    });
    \u0275\u0275text(19, " Accept ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ride_r9 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ride_r9.pickup_location, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(6, 4, ride_r9.pickup_time, "shortTime"), " ");
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", ride_r9.dropoff_location, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ride_r9.fare ? \u0275\u0275pipeBind2(16, 7, ride_r9.fare * 0.7, "USD") : "TBD", "");
  }
}
function RideAssignmentsComponent_ng_container_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 20)(2, "table", 21, 0);
    \u0275\u0275elementContainerStart(4, 22);
    \u0275\u0275template(5, RideAssignmentsComponent_ng_container_14_th_5_Template, 2, 0, "th", 23)(6, RideAssignmentsComponent_ng_container_14_td_6_Template, 2, 1, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(7, 25);
    \u0275\u0275template(8, RideAssignmentsComponent_ng_container_14_th_8_Template, 2, 0, "th", 23)(9, RideAssignmentsComponent_ng_container_14_td_9_Template, 2, 1, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(10, 26);
    \u0275\u0275template(11, RideAssignmentsComponent_ng_container_14_th_11_Template, 2, 0, "th", 23)(12, RideAssignmentsComponent_ng_container_14_td_12_Template, 3, 4, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(13, 27);
    \u0275\u0275template(14, RideAssignmentsComponent_ng_container_14_th_14_Template, 2, 0, "th", 23)(15, RideAssignmentsComponent_ng_container_14_td_15_Template, 3, 4, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(16, 28);
    \u0275\u0275template(17, RideAssignmentsComponent_ng_container_14_th_17_Template, 2, 0, "th", 29)(18, RideAssignmentsComponent_ng_container_14_td_18_Template, 3, 0, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(19, RideAssignmentsComponent_ng_container_14_tr_19_Template, 1, 0, "tr", 30)(20, RideAssignmentsComponent_ng_container_14_tr_20_Template, 1, 0, "tr", 31);
    \u0275\u0275elementEnd();
    \u0275\u0275element(21, "mat-paginator", 32, 1);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "div", 33)(24, "mat-accordion", 34);
    \u0275\u0275template(25, RideAssignmentsComponent_ng_container_14_mat_expansion_panel_25_Template, 20, 10, "mat-expansion-panel", 35);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r6 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("dataSource", ctx_r6.availableDataSource);
    \u0275\u0275advance(17);
    \u0275\u0275property("matHeaderRowDef", ctx_r6.availableColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r6.availableColumns);
    \u0275\u0275advance();
    \u0275\u0275property("pageSizeOptions", \u0275\u0275pureFunction0(6, _c4))("pageSize", 3);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", ctx_r6.availableRides());
  }
}
function RideAssignmentsComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 43)(1, "div", 44)(2, "button", 45);
    \u0275\u0275listener("click", function RideAssignmentsComponent_div_25_Template_button_click_2_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.statusFilter.set("all"));
    });
    \u0275\u0275text(3, "All");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 45);
    \u0275\u0275listener("click", function RideAssignmentsComponent_div_25_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.statusFilter.set("assigned"));
    });
    \u0275\u0275text(5, "Assigned");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "button", 45);
    \u0275\u0275listener("click", function RideAssignmentsComponent_div_25_Template_button_click_6_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.statusFilter.set("in-progress"));
    });
    \u0275\u0275text(7, "In Progress");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "button", 45);
    \u0275\u0275listener("click", function RideAssignmentsComponent_div_25_Template_button_click_8_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.statusFilter.set("completed"));
    });
    \u0275\u0275text(9, "Completed");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r6 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r6.statusFilter() === "all" ? "primary" : "");
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r6.statusFilter() === "assigned" ? "primary" : "");
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r6.statusFilter() === "in-progress" ? "primary" : "");
    \u0275\u0275advance(2);
    \u0275\u0275property("color", ctx_r6.statusFilter() === "completed" ? "primary" : "");
  }
}
function RideAssignmentsComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275element(1, "mat-spinner", 18);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Loading rides...");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "p");
    \u0275\u0275text(2, "You don't have any assigned rides.");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "p");
    \u0275\u0275text(2, "No rides match the current filter.");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_29_th_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Pickup");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r11 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r11.pickup_location);
  }
}
function RideAssignmentsComponent_ng_container_29_th_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Destination");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r12 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r12.dropoff_location);
  }
}
function RideAssignmentsComponent_ng_container_29_th_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Time");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r13 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(2, 1, ride_r13.pickup_time, "short"));
  }
}
function RideAssignmentsComponent_ng_container_29_th_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Status");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37)(1, "span", 48);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ride_r14 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", "status-" + ride_r14.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ride_r14.status, " ");
  }
}
function RideAssignmentsComponent_ng_container_29_th_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 36);
    \u0275\u0275text(1, "Fare");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "currency");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ride_r15 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r15.fare ? \u0275\u0275pipeBind2(2, 1, ride_r15.fare * 0.7, "USD") : "TBD");
  }
}
function RideAssignmentsComponent_ng_container_29_th_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "th", 38);
    \u0275\u0275text(1, "Actions");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_21_button_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 39);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_td_21_button_1_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r17);
      const ride_r18 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.startRide(ride_r18.id));
    });
    \u0275\u0275text(1, " Start Ride ");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_21_button_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 53);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_td_21_button_2_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r19);
      const ride_r18 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.completeRide(ride_r18.id));
    });
    \u0275\u0275text(1, " Complete ");
    \u0275\u0275elementEnd();
  }
}
function RideAssignmentsComponent_ng_container_29_td_21_button_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 54);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_td_21_button_3_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r20);
      const ride_r18 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.showNavigation(ride_r18));
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "navigation");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_29_td_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "td", 37);
    \u0275\u0275template(1, RideAssignmentsComponent_ng_container_29_td_21_button_1_Template, 2, 0, "button", 49)(2, RideAssignmentsComponent_ng_container_29_td_21_button_2_Template, 2, 0, "button", 50)(3, RideAssignmentsComponent_ng_container_29_td_21_button_3_Template, 3, 0, "button", 51);
    \u0275\u0275elementStart(4, "button", 52);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_td_21_Template_button_click_4_listener() {
      const ride_r18 = \u0275\u0275restoreView(_r16).$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.viewRideDetails(ride_r18.id));
    });
    \u0275\u0275elementStart(5, "mat-icon");
    \u0275\u0275text(6, "visibility");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ride_r18 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r18.status === "assigned");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r18.status === "in-progress");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r18.status !== "completed");
  }
}
function RideAssignmentsComponent_ng_container_29_tr_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 40);
  }
}
function RideAssignmentsComponent_ng_container_29_tr_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "tr", 41);
  }
}
function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 59);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_8_Template_button_click_0_listener($event) {
      \u0275\u0275restoreView(_r22);
      const ride_r23 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      ctx_r6.startRide(ride_r23.id);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "play_arrow");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 60);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_9_Template_button_click_0_listener($event) {
      \u0275\u0275restoreView(_r24);
      const ride_r23 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      ctx_r6.completeRide(ride_r23.id);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "check_circle");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r25 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 61);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_26_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r25);
      const ride_r23 = \u0275\u0275nextContext().$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.showNavigation(ride_r23));
    });
    \u0275\u0275elementStart(1, "mat-icon");
    \u0275\u0275text(2, "navigation");
    \u0275\u0275elementEnd()();
  }
}
function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "mat-expansion-panel")(1, "mat-expansion-panel-header")(2, "mat-panel-title");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "mat-panel-description")(5, "div", 55)(6, "span", 48);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_8_Template, 3, 0, "button", 56)(9, RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_9_Template, 3, 0, "button", 57);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(10, "div", 42)(11, "p")(12, "strong");
    \u0275\u0275text(13, "To:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "p")(16, "strong");
    \u0275\u0275text(17, "Time:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(18);
    \u0275\u0275pipe(19, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "p")(21, "strong");
    \u0275\u0275text(22, "Fare:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(23);
    \u0275\u0275pipe(24, "currency");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(25, "mat-action-row");
    \u0275\u0275template(26, RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_button_26_Template, 3, 0, "button", 58);
    \u0275\u0275elementStart(27, "button", 52);
    \u0275\u0275listener("click", function RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_Template_button_click_27_listener() {
      const ride_r23 = \u0275\u0275restoreView(_r21).$implicit;
      const ctx_r6 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r6.viewRideDetails(ride_r23.id));
    });
    \u0275\u0275elementStart(28, "mat-icon");
    \u0275\u0275text(29, "visibility");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ride_r23 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ride_r23.pickup_location, " ");
    \u0275\u0275advance(3);
    \u0275\u0275property("ngClass", "status-" + ride_r23.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ride_r23.status);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r23.status === "assigned");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ride_r23.status === "in-progress");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ride_r23.dropoff_location, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", \u0275\u0275pipeBind2(19, 9, ride_r23.pickup_time, "short"), "");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ride_r23.fare ? \u0275\u0275pipeBind2(24, 12, ride_r23.fare * 0.7, "USD") : "TBD", "");
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ride_r23.status !== "completed");
  }
}
function RideAssignmentsComponent_ng_container_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "div", 20)(2, "table", 21, 2);
    \u0275\u0275elementContainerStart(4, 22);
    \u0275\u0275template(5, RideAssignmentsComponent_ng_container_29_th_5_Template, 2, 0, "th", 23)(6, RideAssignmentsComponent_ng_container_29_td_6_Template, 2, 1, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(7, 25);
    \u0275\u0275template(8, RideAssignmentsComponent_ng_container_29_th_8_Template, 2, 0, "th", 23)(9, RideAssignmentsComponent_ng_container_29_td_9_Template, 2, 1, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(10, 26);
    \u0275\u0275template(11, RideAssignmentsComponent_ng_container_29_th_11_Template, 2, 0, "th", 23)(12, RideAssignmentsComponent_ng_container_29_td_12_Template, 3, 4, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(13, 46);
    \u0275\u0275template(14, RideAssignmentsComponent_ng_container_29_th_14_Template, 2, 0, "th", 23)(15, RideAssignmentsComponent_ng_container_29_td_15_Template, 3, 2, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(16, 27);
    \u0275\u0275template(17, RideAssignmentsComponent_ng_container_29_th_17_Template, 2, 0, "th", 23)(18, RideAssignmentsComponent_ng_container_29_td_18_Template, 3, 4, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementContainerStart(19, 28);
    \u0275\u0275template(20, RideAssignmentsComponent_ng_container_29_th_20_Template, 2, 0, "th", 29)(21, RideAssignmentsComponent_ng_container_29_td_21_Template, 7, 3, "td", 24);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275template(22, RideAssignmentsComponent_ng_container_29_tr_22_Template, 1, 0, "tr", 30)(23, RideAssignmentsComponent_ng_container_29_tr_23_Template, 1, 0, "tr", 31);
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "mat-paginator", 47, 3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(26, "div", 33)(27, "mat-accordion", 34);
    \u0275\u0275template(28, RideAssignmentsComponent_ng_container_29_mat_expansion_panel_28_Template, 30, 15, "mat-expansion-panel", 35);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const ctx_r6 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("dataSource", ctx_r6.myRidesDataSource);
    \u0275\u0275advance(20);
    \u0275\u0275property("matHeaderRowDef", ctx_r6.myRidesColumns);
    \u0275\u0275advance();
    \u0275\u0275property("matRowDefColumns", ctx_r6.myRidesColumns);
    \u0275\u0275advance();
    \u0275\u0275property("pageSizeOptions", \u0275\u0275pureFunction0(6, _c5))("pageSize", 5);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", ctx_r6.filteredMyRides());
  }
}
function RideAssignmentsComponent_app_ride_navigation_30_Template(rf, ctx) {
  if (rf & 1) {
    const _r26 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "app-ride-navigation", 62);
    \u0275\u0275listener("close", function RideAssignmentsComponent_app_ride_navigation_30_Template_app_ride_navigation_close_0_listener() {
      \u0275\u0275restoreView(_r26);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.selectedRide = null);
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r6 = \u0275\u0275nextContext();
    \u0275\u0275property("ride", ctx_r6.selectedRide);
  }
}
function RideAssignmentsComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    const _r27 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 63)(1, "app-ride-detail", 64);
    \u0275\u0275listener("rideUpdated", function RideAssignmentsComponent_div_31_Template_app_ride_detail_rideUpdated_1_listener($event) {
      \u0275\u0275restoreView(_r27);
      const ctx_r6 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r6.onRideUpdated($event));
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r6 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("rideId", ctx_r6.selectedRideId)("onClose", ctx_r6.closeRideDetails.bind(ctx_r6));
  }
}
var RideAssignmentsComponent = class _RideAssignmentsComponent {
  rideService;
  authService;
  messageService;
  router;
  snackBar;
  currentUser = null;
  selectedRide = null;
  selectedRideId = null;
  loading = false;
  ridesSubscription = null;
  availableRides = signal([]);
  myRides = signal([]);
  statusFilter = signal("all");
  filteredMyRides = computed(() => {
    const rides = this.myRides();
    const filter = this.statusFilter();
    if (filter === "all") {
      return rides;
    }
    return rides.filter((ride) => ride.status === filter);
  });
  availableColumns = ["pickup_location", "dropoff_location", "pickup_time", "fare", "actions"];
  myRidesColumns = ["pickup_location", "dropoff_location", "pickup_time", "status", "fare", "actions"];
  availableDataSource = new MatTableDataSource([]);
  myRidesDataSource = new MatTableDataSource([]);
  availableSort;
  availablePaginator;
  myRidesSort;
  myRidesPaginator;
  constructor(rideService, authService, messageService, router, snackBar) {
    this.rideService = rideService;
    this.authService = authService;
    this.messageService = messageService;
    this.router = router;
    this.snackBar = snackBar;
    effect(() => {
      const filter = this.statusFilter();
      const allMyRides = this.myRides();
      if (filter === "all") {
        this.myRidesDataSource.data = allMyRides;
      } else {
        this.myRidesDataSource.data = allMyRides.filter((ride) => ride.status === filter);
      }
    });
  }
  ngOnInit() {
    return __async(this, null, function* () {
      try {
        yield this.loadCurrentUser();
        if (this.currentUser) {
          this.setupRealtimeSubscription();
          yield this.loadRides();
          console.log("\u2705 Driver dashboard initialized with realtime updates");
        }
      } catch (error) {
        console.error("\u274C Error loading current user:", error);
        this.snackBar.open("Failed to load user information", "Close", { duration: 3e3 });
      }
    });
  }
  ngAfterViewInit() {
    setTimeout(() => {
      this.setupDataSources();
    }, 0);
  }
  ngOnDestroy() {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }
  loadCurrentUser() {
    return __async(this, null, function* () {
      try {
        this.loading = true;
        this.currentUser = yield this.authService.getCurrentUser();
      } catch (error) {
        console.error("Error loading current user:", error);
        this.snackBar.open("Failed to load user information", "Close", { duration: 3e3 });
      } finally {
        this.loading = false;
      }
    });
  }
  setupRealtimeSubscription() {
    if (!this.currentUser)
      return;
    console.log("\u{1F504} Setting up realtime subscription for driver:", this.currentUser.id);
    this.ridesSubscription = this.rideService.rides$.subscribe((allRides) => {
      if (allRides && allRides.length >= 0) {
        const available = allRides.filter((ride) => ride.status === "requested");
        const myRides = allRides.filter((ride) => ride.driver_id === this.currentUser?.id && ["assigned", "in-progress", "completed"].includes(ride.status));
        this.availableRides.set(available);
        this.myRides.set(myRides);
        this.availableDataSource.data = available;
        const currentAvailableCount = this.availableDataSource.data.length;
        if (available.length > currentAvailableCount && currentAvailableCount > 0) {
          this.snackBar.open(`${available.length - currentAvailableCount} new ride(s) available!`, "View", {
            duration: 5e3,
            panelClass: ["success-snackbar"]
          });
        }
      }
    });
  }
  setupDataSources() {
    console.log("Setting up data sources...");
    if (this.availableSort) {
      this.availableDataSource.sort = this.availableSort;
    }
    if (this.availablePaginator) {
      this.availableDataSource.paginator = this.availablePaginator;
    }
    if (this.myRidesSort) {
      this.myRidesDataSource.sort = this.myRidesSort;
    }
    if (this.myRidesPaginator) {
      this.myRidesDataSource.paginator = this.myRidesPaginator;
    }
    this.availableDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case "pickup_time":
          return item[property] ? new Date(item[property]).getTime() : 0;
        case "fare":
          return item.fare || 0;
        case "pickup_location":
        case "dropoff_location":
          return item[property]?.toLowerCase() || "";
        default:
          return item[property] || "";
      }
    };
    this.myRidesDataSource.sortingDataAccessor = (item, property) => {
      switch (property) {
        case "pickup_time":
          return item[property] ? new Date(item[property]).getTime() : 0;
        case "fare":
          return item.fare || 0;
        case "pickup_location":
        case "dropoff_location":
        case "status":
          return item[property]?.toLowerCase() || "";
        default:
          return item[property] || "";
      }
    };
  }
  loadRides() {
    return __async(this, null, function* () {
      if (!this.currentUser)
        return;
      console.log("\u{1F504} Loading rides...");
      this.loading = true;
      try {
        yield this.rideService.getAllRides();
        const [available, assigned] = yield Promise.all([
          this.rideService.getAvailableRides(),
          this.rideService.getDriverRides(this.currentUser.id)
        ]);
        this.availableRides.set(available);
        this.myRides.set(assigned);
        this.availableDataSource.data = available;
        setTimeout(() => {
          this.setupDataSources();
        }, 0);
        this.snackBar.open("Rides refreshed", "Close", { duration: 2e3 });
      } catch (error) {
        console.error("\u274C Error loading rides:", error);
        this.snackBar.open(error.message || "Failed to load rides", "Close", { duration: 3e3 });
      } finally {
        this.loading = false;
      }
    });
  }
  acceptRide(rideId) {
    return __async(this, null, function* () {
      if (!this.currentUser)
        return;
      try {
        yield this.rideService.acceptRide(rideId, this.currentUser.id);
        this.snackBar.open("Ride accepted successfully", "Close", { duration: 3e3 });
      } catch (error) {
        console.error("Error accepting ride:", error);
        this.snackBar.open(error.message || "Failed to accept ride", "Close", { duration: 3e3 });
      }
    });
  }
  startRide(rideId) {
    return __async(this, null, function* () {
      try {
        yield this.rideService.startRide(rideId);
        this.snackBar.open("Ride started successfully", "Close", { duration: 3e3 });
      } catch (error) {
        console.error("Error starting ride:", error);
        this.snackBar.open(error.message || "Failed to start ride", "Close", { duration: 3e3 });
      }
    });
  }
  completeRide(rideId) {
    return __async(this, null, function* () {
      try {
        yield this.rideService.completeRide(rideId);
        this.snackBar.open("Ride completed successfully", "Close", { duration: 3e3 });
      } catch (error) {
        console.error("Error completing ride:", error);
        this.snackBar.open(error.message || "Failed to complete ride", "Close", { duration: 3e3 });
      }
    });
  }
  showNavigation(ride) {
    this.selectedRide = ride;
  }
  closeNavigation() {
    this.selectedRide = null;
  }
  openChat(rideId) {
    return __async(this, null, function* () {
      try {
        const thread = yield this.messageService.getOrCreateThreadForRide(rideId);
        yield this.router.navigate(["/dashboard", "driver", "messages", thread.id]);
      } catch (error) {
        console.error("Error opening chat:", error);
        this.snackBar.open(error.message || "Failed to open chat", "Close", { duration: 3e3 });
      }
    });
  }
  viewRideDetails(rideId) {
    this.selectedRideId = rideId;
  }
  closeRideDetails() {
    this.selectedRideId = null;
  }
  onRideUpdated(_ride) {
  }
  trackByRideId(_index, item) {
    return item.id;
  }
  static \u0275fac = function RideAssignmentsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RideAssignmentsComponent)(\u0275\u0275directiveInject(RideService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(MessageService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(MatSnackBar));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RideAssignmentsComponent, selectors: [["app-ride-assignments"]], viewQuery: function RideAssignmentsComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
      \u0275\u0275viewQuery(_c1, 5);
      \u0275\u0275viewQuery(_c2, 5);
      \u0275\u0275viewQuery(_c3, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.availableSort = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.availablePaginator = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.myRidesSort = _t.first);
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.myRidesPaginator = _t.first);
    }
  }, decls: 32, vars: 13, consts: [["availableSort", "matSort"], ["availablePaginator", ""], ["myRidesSort", "matSort"], ["myRidesPaginator", ""], [1, "assignments-container"], ["label", "Available Rides", 3, "tabIndex"], [1, "table-container"], [1, "header-with-actions"], ["title", "Realtime updates active", 1, "realtime-indicator"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Manual refresh", 3, "click"], ["class", "loading-container", 4, "ngIf"], ["class", "no-rides", 4, "ngIf"], [4, "ngIf"], ["label", "My Rides", 3, "tabIndex", "disabled"], ["class", "filter-container", 4, "ngIf"], [3, "ride", "close", 4, "ngIf"], ["class", "ride-detail-overlay", 4, "ngIf"], [1, "loading-container"], ["diameter", "40"], [1, "no-rides"], [1, "desktop-view"], ["mat-table", "", "matSort", "", 1, "ride-table", 3, "dataSource"], ["matColumnDef", "pickup_location"], ["mat-header-cell", "", "mat-sort-header", "", 4, "matHeaderCellDef"], ["mat-cell", "", 4, "matCellDef"], ["matColumnDef", "dropoff_location"], ["matColumnDef", "pickup_time"], ["matColumnDef", "fare"], ["matColumnDef", "actions"], ["mat-header-cell", "", 4, "matHeaderCellDef"], ["mat-header-row", "", 4, "matHeaderRowDef"], ["mat-row", "", 4, "matRowDef", "matRowDefColumns"], ["showFirstLastButtons", "", "aria-label", "Select page of available rides", 3, "pageSizeOptions", "pageSize"], [1, "mobile-view"], ["multi", ""], [4, "ngFor", "ngForOf"], ["mat-header-cell", "", "mat-sort-header", ""], ["mat-cell", ""], ["mat-header-cell", ""], ["mat-raised-button", "", "color", "primary", 3, "click"], ["mat-header-row", ""], ["mat-row", ""], [1, "ride-details"], [1, "filter-container"], ["aria-label", "Filter rides by status", 1, "filter-buttons"], ["mat-stroked-button", "", 1, "filter-button", 3, "click", "color"], ["matColumnDef", "status"], ["showFirstLastButtons", "", "aria-label", "Select page of my rides", 3, "pageSizeOptions", "pageSize"], [1, "status-chip", 3, "ngClass"], ["mat-raised-button", "", "color", "primary", 3, "click", 4, "ngIf"], ["mat-raised-button", "", "color", "accent", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "primary", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "primary", "matTooltip", "View Details", 3, "click"], ["mat-raised-button", "", "color", "accent", 3, "click"], ["mat-icon-button", "", "color", "primary", 3, "click"], [1, "ride-actions-header"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Start Ride", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "accent", "matTooltip", "Complete Ride", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Navigation", 3, "click", 4, "ngIf"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Start Ride", 3, "click"], ["mat-icon-button", "", "color", "accent", "matTooltip", "Complete Ride", 3, "click"], ["mat-icon-button", "", "color", "primary", "matTooltip", "Navigation", 3, "click"], [3, "close", "ride"], [1, "ride-detail-overlay"], [3, "rideUpdated", "rideId", "onClose"]], template: function RideAssignmentsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 4)(1, "mat-tab-group")(2, "mat-tab", 5)(3, "div", 6)(4, "div", 7)(5, "h3");
      \u0275\u0275text(6, "Available Ride Requests ");
      \u0275\u0275elementStart(7, "span", 8);
      \u0275\u0275text(8, "\u25CF");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "button", 9);
      \u0275\u0275listener("click", function RideAssignmentsComponent_Template_button_click_9_listener() {
        return ctx.loadRides();
      });
      \u0275\u0275elementStart(10, "mat-icon");
      \u0275\u0275text(11, "refresh");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(12, RideAssignmentsComponent_div_12_Template, 4, 0, "div", 10)(13, RideAssignmentsComponent_div_13_Template, 3, 0, "div", 11)(14, RideAssignmentsComponent_ng_container_14_Template, 26, 7, "ng-container", 12);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(15, "mat-tab", 13)(16, "div", 6)(17, "div", 7)(18, "h3");
      \u0275\u0275text(19, "My Assigned Rides ");
      \u0275\u0275elementStart(20, "span", 8);
      \u0275\u0275text(21, "\u25CF");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(22, "button", 9);
      \u0275\u0275listener("click", function RideAssignmentsComponent_Template_button_click_22_listener() {
        return ctx.loadRides();
      });
      \u0275\u0275elementStart(23, "mat-icon");
      \u0275\u0275text(24, "refresh");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(25, RideAssignmentsComponent_div_25_Template, 10, 4, "div", 14)(26, RideAssignmentsComponent_div_26_Template, 4, 0, "div", 10)(27, RideAssignmentsComponent_div_27_Template, 3, 0, "div", 11)(28, RideAssignmentsComponent_div_28_Template, 3, 0, "div", 11)(29, RideAssignmentsComponent_ng_container_29_Template, 29, 7, "ng-container", 12);
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(30, RideAssignmentsComponent_app_ride_navigation_30_Template, 1, 1, "app-ride-navigation", 15)(31, RideAssignmentsComponent_div_31_Template, 2, 2, "div", 16);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(2);
      \u0275\u0275property("tabIndex", 0);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.availableRides().length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.availableRides().length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("tabIndex", 1)("disabled", ctx.myRides().length === 0);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", ctx.myRides().length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.myRides().length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.filteredMyRides().length === 0 && ctx.myRides().length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.filteredMyRides().length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedRide);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedRideId);
    }
  }, dependencies: [
    CommonModule,
    NgClass,
    NgForOf,
    NgIf,
    CurrencyPipe,
    DatePipe,
    MatCardModule,
    MatTabsModule,
    MatTab,
    MatTabGroup,
    MatTableModule,
    MatTable,
    MatHeaderCellDef,
    MatHeaderRowDef,
    MatColumnDef,
    MatCellDef,
    MatRowDef,
    MatHeaderCell,
    MatCell,
    MatHeaderRow,
    MatRow,
    MatChipsModule,
    MatButtonModule,
    MatButton,
    MatIconButton,
    MatIconModule,
    MatIcon,
    MatSnackBarModule,
    MatBadgeModule,
    MatTooltipModule,
    MatTooltip,
    MatProgressSpinnerModule,
    MatProgressSpinner,
    RideNavigationComponent,
    RideDetailComponent,
    MatSortModule,
    MatSort,
    MatSortHeader,
    MatPaginatorModule,
    MatPaginator,
    MatExpansionModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionPanelActionRow,
    MatExpansionPanelHeader,
    MatExpansionPanelTitle,
    MatExpansionPanelDescription
  ], styles: ["\n\n.assignments-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.table-container[_ngcontent-%COMP%] {\n  margin: 20px;\n}\n.ride-table[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.no-rides[_ngcontent-%COMP%] {\n  padding: 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n}\n.status-chip[_ngcontent-%COMP%] {\n  border-radius: 16px;\n  padding: 4px 12px;\n  color: white;\n  font-weight: 500;\n}\n.status-requested[_ngcontent-%COMP%] {\n  background-color: #ff9800;\n}\n.status-assigned[_ngcontent-%COMP%] {\n  background-color: #2196f3;\n}\n.status-in-progress[_ngcontent-%COMP%] {\n  background-color: #673ab7;\n}\n.status-completed[_ngcontent-%COMP%] {\n  background-color: #4caf50;\n}\n.status-canceled[_ngcontent-%COMP%] {\n  background-color: #f44336;\n}\n.header-with-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #666;\n}\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 10px;\n}\n.ride-detail-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.realtime-indicator[_ngcontent-%COMP%] {\n  color: #4caf50;\n  font-size: 12px;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.desktop-view[_ngcontent-%COMP%] {\n  display: block;\n}\n.mobile-view[_ngcontent-%COMP%] {\n  display: none;\n}\n.filter-container[_ngcontent-%COMP%] {\n  margin: 0 20px 20px;\n}\n.ride-actions-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n@media (max-width: 600px) {\n  .desktop-view[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .mobile-view[_ngcontent-%COMP%] {\n    display: block;\n  }\n  .assignments-container[_ngcontent-%COMP%] {\n    padding: 0;\n  }\n  .table-container[_ngcontent-%COMP%] {\n    margin: 0;\n  }\n  .mat-tab-body-content[_ngcontent-%COMP%] {\n    overflow: hidden;\n  }\n  .filter-container[_ngcontent-%COMP%] {\n    margin: 0 0 16px;\n  }\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel[_ngcontent-%COMP%] {\n  margin: 8px 0;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\n  font-size: 14px;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-panel-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-panel-description[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n  align-items: center;\n}\n.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%] {\n  padding: 0 24px 16px;\n  font-size: 14px;\n}\n.mobile-view[_ngcontent-%COMP%]   .ride-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 4px 0;\n}\n.mobile-view[_ngcontent-%COMP%]   .mat-action-row[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n  padding: 8px 12px 8px 24px;\n}\n/*# sourceMappingURL=ride-assignments.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RideAssignmentsComponent, [{
    type: Component,
    args: [{ selector: "app-ride-assignments", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatTabsModule,
      MatTableModule,
      MatChipsModule,
      MatButtonModule,
      MatIconModule,
      MatSnackBarModule,
      MatBadgeModule,
      MatTooltipModule,
      MatProgressSpinnerModule,
      RideNavigationComponent,
      RideDetailComponent,
      MatSortModule,
      MatPaginatorModule,
      MatExpansionModule
    ], template: `
    <div class="assignments-container">
      <mat-tab-group>
        <mat-tab label="Available Rides" [tabIndex]="0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>Available Ride Requests <span class="realtime-indicator" title="Realtime updates active">\u25CF</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div> 
            
            <div *ngIf="!loading && availableRides().length === 0" class="no-rides">
              <p>No available ride requests at this time.</p>
            </div>

            <ng-container *ngIf="!loading && availableRides().length > 0">
              <div class="desktop-view">
                <table mat-table [dataSource]="availableDataSource"  #availableSort="matSort"  matSort class="ride-table">
                  <ng-container matColumnDef="pickup_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="dropoff_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="pickup_time">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="fare">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.fare ? ( ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let ride">
                      <button mat-raised-button color="primary" (click)="acceptRide(ride.id)">
                        Accept
                      </button>
                    </td>
                  </ng-container>
    
                  <tr mat-header-row *matHeaderRowDef="availableColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: availableColumns;"></tr>
                </table>
                <mat-paginator #availablePaginator [pageSizeOptions]="[3,5, 10, 25, 100]" [pageSize]="3" showFirstLastButtons aria-label="Select page of available rides"></mat-paginator>
              </div>

              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let ride of availableRides()">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        {{ ride.pickup_location }}
                      </mat-panel-title>
                      <mat-panel-description>
                        {{ ride.pickup_time | date:'shortTime' }}
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="ride-details">
                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>
                    </div>
                    <mat-action-row>
                      <button mat-raised-button color="primary" (click)="acceptRide(ride.id)">
                        Accept
                      </button>
                    </mat-action-row>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </ng-container>
          </div>
        </mat-tab>

        <mat-tab label="My Rides" [tabIndex]="1" [disabled]="myRides().length === 0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>My Assigned Rides <span class="realtime-indicator" title="Realtime updates active">\u25CF</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            
            <div class="filter-container" *ngIf="myRides().length > 0">
              <div class="filter-buttons" aria-label="Filter rides by status">
                <button mat-stroked-button
                        [color]="statusFilter() === 'all' ? 'primary' : ''"
                        (click)="statusFilter.set('all')"
                        class="filter-button">All</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'assigned' ? 'primary' : ''"
                        (click)="statusFilter.set('assigned')"
                        class="filter-button">Assigned</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'in-progress' ? 'primary' : ''"
                        (click)="statusFilter.set('in-progress')"
                        class="filter-button">In Progress</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'completed' ? 'primary' : ''"
                        (click)="statusFilter.set('completed')"
                        class="filter-button">Completed</button>
              </div>
            </div>

            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div>
            <div *ngIf="!loading && myRides().length === 0" class="no-rides">
              <p>You don't have any assigned rides.</p>
            </div>
            <div *ngIf="!loading && filteredMyRides().length === 0 && myRides().length > 0" class="no-rides">
              <p>No rides match the current filter.</p>
            </div>

            <ng-container *ngIf="!loading && filteredMyRides().length > 0">
              <div class="desktop-view">
                <table mat-table [dataSource]="myRidesDataSource" matSort #myRidesSort="matSort" class="ride-table">
                  <ng-container matColumnDef="pickup_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="dropoff_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="pickup_time">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                    <td mat-cell *matCellDef="let ride">
                      <span class="status-chip" [ngClass]="'status-' + ride.status">
                        {{ ride.status }}
                      </span>
                    </td>
                  </ng-container>
    
                  <ng-container matColumnDef="fare">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.fare ? (ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let ride">
                      <button mat-raised-button color="primary" *ngIf="ride.status === 'assigned'" (click)="startRide(ride.id)">
                        Start Ride
                      </button>
                      <button mat-raised-button color="accent" *ngIf="ride.status === 'in-progress'" (click)="completeRide(ride.id)">
                        Complete
                      </button>
                      <button mat-icon-button color="primary" *ngIf="ride.status !== 'completed'" (click)="showNavigation(ride)">
                        <mat-icon>navigation</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </td>
                  </ng-container>
    
                  <tr mat-header-row *matHeaderRowDef="myRidesColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: myRidesColumns;"></tr>
                </table>
                <mat-paginator #myRidesPaginator [pageSizeOptions]="[5, 10, 25, 100]" [pageSize]="5" showFirstLastButtons aria-label="Select page of my rides"></mat-paginator>
              </div>

              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let ride of filteredMyRides()">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        {{ ride.pickup_location }}
                      </mat-panel-title>
                      <mat-panel-description>
                        <div class="ride-actions-header">
                          <span class="status-chip" [ngClass]="'status-' + ride.status">{{ ride.status }}</span>
                          <button mat-icon-button color="primary" *ngIf="ride.status === 'assigned'" (click)="startRide(ride.id); $event.stopPropagation()" matTooltip="Start Ride">
                            <mat-icon>play_arrow</mat-icon>
                          </button>
                          <button mat-icon-button color="accent" *ngIf="ride.status === 'in-progress'" (click)="completeRide(ride.id); $event.stopPropagation()" matTooltip="Complete Ride">
                            <mat-icon>check_circle</mat-icon>
                          </button>
                        </div>
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="ride-details">
                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                      <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>
                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>
                    </div>
                    <mat-action-row>
                      <button mat-icon-button color="primary" *ngIf="ride.status !== 'completed'" (click)="showNavigation(ride)" matTooltip="Navigation">
                        <mat-icon>navigation</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </mat-action-row>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </ng-container>
          </div>
        </mat-tab>
      </mat-tab-group>

      <app-ride-navigation
        *ngIf="selectedRide"
        [ride]="selectedRide"
        (close)="selectedRide = null">
      </app-ride-navigation>

      <div *ngIf="selectedRideId" class="ride-detail-overlay">
        <app-ride-detail
          [rideId]="selectedRideId"
          [onClose]="closeRideDetails.bind(this)"
          (rideUpdated)="onRideUpdated($event)">
        </app-ride-detail>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;3d1be79a0d0bcc3ca06c6f37b991318cba1da2ec3742e43d340c1883cbf1c911;C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/driver/ride-assignments/ride-assignments.component.ts */\n.assignments-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.table-container {\n  margin: 20px;\n}\n.ride-table {\n  width: 100%;\n}\n.no-rides {\n  padding: 20px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n}\n.status-chip {\n  border-radius: 16px;\n  padding: 4px 12px;\n  color: white;\n  font-weight: 500;\n}\n.status-requested {\n  background-color: #ff9800;\n}\n.status-assigned {\n  background-color: #2196f3;\n}\n.status-in-progress {\n  background-color: #673ab7;\n}\n.status-completed {\n  background-color: #4caf50;\n}\n.status-canceled {\n  background-color: #f44336;\n}\n.header-with-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #666;\n}\n.loading-container p {\n  margin-top: 10px;\n}\n.ride-detail-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n.realtime-indicator {\n  color: #4caf50;\n  font-size: 12px;\n  animation: pulse 2s infinite;\n}\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.desktop-view {\n  display: block;\n}\n.mobile-view {\n  display: none;\n}\n.filter-container {\n  margin: 0 20px 20px;\n}\n.ride-actions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n@media (max-width: 600px) {\n  .desktop-view {\n    display: none;\n  }\n  .mobile-view {\n    display: block;\n  }\n  .assignments-container {\n    padding: 0;\n  }\n  .table-container {\n    margin: 0;\n  }\n  .mat-tab-body-content {\n    overflow: hidden;\n  }\n  .filter-container {\n    margin: 0 0 16px;\n  }\n}\n.mobile-view .mat-expansion-panel {\n  margin: 8px 0;\n}\n.mobile-view .mat-expansion-panel-header {\n  font-size: 14px;\n}\n.mobile-view .mat-panel-title {\n  font-weight: 500;\n}\n.mobile-view .mat-panel-description {\n  justify-content: flex-end;\n  align-items: center;\n}\n.mobile-view .ride-details {\n  padding: 0 24px 16px;\n  font-size: 14px;\n}\n.mobile-view .ride-details p {\n  margin: 4px 0;\n}\n.mobile-view .mat-action-row {\n  justify-content: flex-end;\n  padding: 8px 12px 8px 24px;\n}\n/*# sourceMappingURL=ride-assignments.component.css.map */\n"] }]
  }], () => [{ type: RideService }, { type: AuthService }, { type: MessageService }, { type: Router }, { type: MatSnackBar }], { availableSort: [{
    type: ViewChild,
    args: ["availableSort"]
  }], availablePaginator: [{
    type: ViewChild,
    args: ["availablePaginator"]
  }], myRidesSort: [{
    type: ViewChild,
    args: ["myRidesSort"]
  }], myRidesPaginator: [{
    type: ViewChild,
    args: ["myRidesPaginator"]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RideAssignmentsComponent, { className: "RideAssignmentsComponent", filePath: "src/app/features/dashboard/driver/ride-assignments/ride-assignments.component.ts", lineNumber: 444 });
})();

// src/app/features/dashboard/driver/driver.component.ts
var DriverComponent = class _DriverComponent {
  static \u0275fac = function DriverComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DriverComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DriverComponent, selectors: [["app-driver"]], decls: 13, vars: 0, consts: [[1, "dashboard-container"], [1, "welcome-card"], ["label", "Ride Assignments"], ["label", "Driver Profile"]], template: function DriverComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "mat-card", 1)(2, "mat-card-header")(3, "mat-card-title");
      \u0275\u0275text(4, "Driver Dashboard");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(5, "mat-card-content")(6, "p");
      \u0275\u0275text(7, "Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments.");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(8, "mat-tab-group")(9, "mat-tab", 2);
      \u0275\u0275element(10, "app-ride-assignments");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "mat-tab", 3);
      \u0275\u0275element(12, "app-driver-profile");
      \u0275\u0275elementEnd()()();
    }
  }, dependencies: [
    CommonModule,
    MatCardModule,
    MatCard,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    MatTabsModule,
    MatTab,
    MatTabGroup,
    DriverProfileComponent,
    RideAssignmentsComponent
  ], styles: ["\n\n.dashboard-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: #f5f5f5;\n}\n.welcome-card[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\nul[_ngcontent-%COMP%] {\n  list-style-type: none;\n  padding: 0;\n  margin: 16px 0;\n}\nli[_ngcontent-%COMP%] {\n  padding: 8px 0;\n}\n/*# sourceMappingURL=driver.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DriverComponent, [{
    type: Component,
    args: [{ selector: "app-driver", standalone: true, imports: [
      CommonModule,
      MatCardModule,
      MatTabsModule,
      DriverProfileComponent,
      RideAssignmentsComponent,
      DriverEarningsComponent
    ], template: '<div class="dashboard-container">\r\n  <mat-card class="welcome-card">\r\n    <mat-card-header>\r\n      <mat-card-title>Driver Dashboard</mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments.</p>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <mat-tab-group>\r\n    <mat-tab label="Ride Assignments">\r\n      <app-ride-assignments></app-ride-assignments>\r\n    </mat-tab>\r\n    <!-- <mat-tab label="Earnings">\r\n      <app-driver-earnings></app-driver-earnings>\r\n    </mat-tab> -->\r\n    <mat-tab label="Driver Profile">\r\n      <app-driver-profile></app-driver-profile>\r\n    </mat-tab>\r\n  </mat-tab-group>\r\n</div>\r\n<!-- driver_id: 5fa049a1-8e0f-41c1-9516-b8a9671e9ab8\r\nrider_id: e0b2b25c-017b-4d68-8231-aead2d273480 -->', styles: ["/* src/app/features/dashboard/driver/driver.component.scss */\n.dashboard-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: #f5f5f5;\n}\n.welcome-card {\n  margin-bottom: 20px;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n  margin: 16px 0;\n}\nli {\n  padding: 8px 0;\n}\n/*# sourceMappingURL=driver.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DriverComponent, { className: "DriverComponent", filePath: "src/app/features/dashboard/driver/driver.component.ts", lineNumber: 24 });
})();
export {
  DriverComponent
};
//# sourceMappingURL=chunk-L3UKJ3L2.js.map
