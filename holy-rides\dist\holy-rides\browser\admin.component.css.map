{"version": 3, "sources": ["src/app/features/dashboard/admin/admin.component.scss"], "sourcesContent": [".dashboard-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n    background-color: #f5f5f5;\n}\n\n.dashboard-title {\n  margin-bottom: 20px;\n  color: #3f51b5;\n  font-weight: 500;\n}\n\n.tab-content {\n  padding: 20px 0;\n  overflow-y: auto;\n\n}\n\n.filters-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 16px;\n  margin-bottom: 20px;\n  align-items: center;\n}\n\n.mat-form-field {\n  flex: 1;\n  min-width: 200px;\n}\n\n.table-container {\n  overflow-x: auto;\n  margin-top: 20px;\n}\n\ntable {\n  width: 100%;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n}\n\n.loading-container p {\n  margin-top: 16px;\n  color: rgba(0, 0, 0, 0.54);\n}\n\n.stats-container {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n\n.stats-card {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.full-width {\n  width: 100%;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 16px;\n  margin-top: 16px;\n  padding: 16px;\n}\n\n.scrollable-content {\n  overflow-y: auto;\n  max-height: calc(100% - 60px); /* Account for header height */\n  padding-right: 8px;\n}\n\nmat-card-content {\n  overflow-y: auto;\n  flex: 1;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 10px;\n  border-radius: 4px;\n  background-color: rgba(63, 81, 181, 0.1);\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 500;\n  color: #1976d2;\n}\n\n.stat-label {\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.54);\n  margin-top: 4px;\n}\n\n.ride-detail-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  overflow-y: auto;\n}\n\n/* Desktop/Mobile View Toggle */\n.desktop-view {\n  display: block;\n}\n\n.mobile-view {\n  display: none;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .filters-container {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .mat-form-field {\n    width: 100%;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* Mobile-specific styles for screens less than 600px */\n@media (max-width: 600px) {\n  .desktop-view {\n    display: none;\n  }\n\n  .mobile-view {\n    display: block;\n  }\n\n  .dashboard-container {\n    padding: 10px;\n  }\n\n  .tab-content {\n    padding: 10px 0;\n  }\n\n  .filters-container {\n    margin-bottom: 16px;\n  }\n\n  .mat-tab-body-content {\n    overflow: hidden;\n  }\n}\n\n.mat-grid-tile{\n  min-height: 260px;\n}\n\n.refresh-info {\n  display: flex;\n  align-items: center;\n  margin-left: auto;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.6);\n}\n\n.last-refresh {\n  margin-right: 8px;\n}\n\n.auto-refresh-note {\n  margin-left: 8px;\n  font-style: italic;\n  font-size: 12px;\n}\n\n.action-buttons {\n  margin-left: 20px;\n  display: flex;\n  align-items: center;\n}\n.close-overlay {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  z-index: 2000; /* Higher than overlay/dialog */\n  cursor: pointer;\n  background: white;\n  border-radius: 50%;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n  padding: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Mobile Accordion Styles */\n.mobile-view .mat-expansion-panel {\n  margin: 8px 0;\n  border-radius: 8px;\n}\n\n.mobile-view .mat-expansion-panel-header {\n  font-size: 14px;\n  padding: 12px 16px;\n}\n\n.mobile-view .mat-panel-title {\n  font-weight: 500;\n  font-size: 16px;\n  color: #333;\n}\n\n.mobile-view .mat-panel-description {\n  justify-content: flex-end;\n  align-items: center;\n  margin-left: 16px;\n}\n\n.mobile-view .user-actions-header,\n.mobile-view .ride-actions-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.mobile-view .role-chip,\n.mobile-view .status-chip {\n  font-size: 12px;\n  min-height: 24px;\n  line-height: 24px;\n}\n\n.mobile-view .user-details,\n.mobile-view .ride-details {\n  padding: 0 24px 16px;\n  font-size: 14px;\n}\n\n.mobile-view .user-details p,\n.mobile-view .ride-details p {\n  margin: 8px 0;\n  line-height: 1.4;\n}\n\n.mobile-view .mat-action-row {\n  justify-content: flex-end;\n  padding: 8px 12px 8px 24px;\n  border-top: 1px solid rgba(0, 0, 0, 0.12);\n}\n\n.mobile-view .mat-action-row button {\n  margin-left: 8px;\n}"], "mappings": ";AAAA,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;AACE,oBAAA;;AAGJ,CAAA;AACE,iBAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA,KAAA;AACA,cAAA;;AAIF,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAAA;AACE,QAAA;AACA,aAAA;;AAGF,CAAA;AACE,cAAA;AACA,cAAA;;AAGF;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;;AAGF,CARA,kBAQA;AACE,cAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA;AACA,kBAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,CAAA,EAAA;AACA,OAAA;AACA,cAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,cAAA,KAAA,KAAA,EAAA;AACA,iBAAA;;AAGF;AACE,cAAA;AACA,QAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,iBAAA;AACA,oBAAA,KAAA,EAAA,EAAA,EAAA,EAAA,GAAA,EAAA;;AAGF,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAAA;AACE,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA;;AAGF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,oBAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,cAAA;;AAIF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;;AAIF,OAAA,CAAA,SAAA,EAAA;AACE,GAlHF;AAmHI,oBAAA;AACA,iBAAA;;AAGF,GA/GF;AAgHI,WAAA;;AAGF,GAxEF;AAyEI,2BAAA;;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GA1BF;AA2BI,aAAA;;AAGF,GA1BF;AA2BI,aAAA;;AAGF,GA7JF;AA8JI,aAAA;;AAGF,GApJF;AAqJI,aAAA,KAAA;;AAGF,GAlJF;AAmJI,mBAAA;;AAGF,GAAA;AACE,cAAA;;;AAIJ,CAAA;AACE,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,eAAA;AACA,aAAA;AACA,SAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,gBAAA;;AAGF,CAAA;AACE,eAAA;AACA,cAAA;AACA,aAAA;;AAGF,CAAA;AACE,eAAA;AACA,WAAA;AACA,eAAA;;AAEF,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,WAAA;AACA,UAAA;AACA,cAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;;AAIF,CA1FA,YA0FA,CAAA;AACE,UAAA,IAAA;AACA,iBAAA;;AAGF,CA/FA,YA+FA,CAAA;AACE,aAAA;AACA,WAAA,KAAA;;AAGF,CApGA,YAoGA,CAAA;AACE,eAAA;AACA,aAAA;AACA,SAAA;;AAGF,CA1GA,YA0GA,CAAA;AACE,mBAAA;AACA,eAAA;AACA,eAAA;;AAGF,CAhHA,YAgHA,CAAA;AAAA,CAhHA,YAgHA,CAAA;AAEE,WAAA;AACA,eAAA;AACA,OAAA;;AAGF,CAvHA,YAuHA,CAAA;AAAA,CAvHA,YAuHA,CAAA;AAEE,aAAA;AACA,cAAA;AACA,eAAA;;AAGF,CA9HA,YA8HA,CAAA;AAAA,CA9HA,YA8HA,CAAA;AAEE,WAAA,EAAA,KAAA;AACA,aAAA;;AAGF,CApIA,YAoIA,CANA,aAMA;AAAA,CApIA,YAoIA,CANA,aAMA;AAEE,UAAA,IAAA;AACA,eAAA;;AAGF,CA1IA,YA0IA,CAAA;AACE,mBAAA;AACA,WAAA,IAAA,KAAA,IAAA;AACA,cAAA,IAAA,MAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAhJA,YAgJA,CANA,eAMA;AACE,eAAA;;", "names": []}