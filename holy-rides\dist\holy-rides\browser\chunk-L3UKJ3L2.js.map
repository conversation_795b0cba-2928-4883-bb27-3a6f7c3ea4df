{"version": 3, "sources": ["src/app/core/services/vehicle.service.ts", "src/app/features/dashboard/driver/driver-profile/driver-profile.component.ts", "src/app/features/dashboard/driver/driver-earnings/driver-earnings.component.ts", "src/app/features/dashboard/driver/ride-navigation/ride-navigation.component.ts", "src/app/features/dashboard/driver/ride-assignments/ride-assignments.component.ts", "src/app/features/dashboard/driver/driver.component.ts", "src/app/features/dashboard/driver/driver.component.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { Vehicle } from '../models/vehicle.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class VehicleService {\n  private supabase: SupabaseClient;\n  private vehiclesSubject = new BehaviorSubject<Vehicle[]>([]);\n  vehicles$ = this.vehiclesSubject.asObservable();\n\n  constructor(private authService: AuthService) {\n    this.supabase = authService.supabase;\n\n    // For demo purposes, we'll create some mock vehicles\n    // In a real app, you would fetch these from the database\n    this.initializeMockVehicles();\n  }\n\n  private initializeMockVehicles() {\n    const mockVehicles: Vehicle[] = [\n      {\n        id: '1',\n        driver_id: 'driver1',\n        make: 'Toyota',\n        model: 'Camry',\n        year: 2020,\n        color: 'Silver',\n        license_plate: 'ABC123',\n        capacity: 4,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      },\n      {\n        id: '2',\n        driver_id: 'driver2',\n        make: 'Honda',\n        model: 'Accord',\n        year: 2019,\n        color: 'Black',\n        license_plate: 'XYZ789',\n        capacity: 5,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }\n    ];\n\n    this.vehiclesSubject.next(mockVehicles);\n  }\n\n  async getDriverVehicles(driverId: string): Promise<Vehicle[]> {\n    try {\n      // In a real app, you would fetch from Supabase\n      // For now, we'll filter the mock vehicles\n      return this.vehiclesSubject.value.filter(vehicle => vehicle.driver_id === driverId);\n    } catch (error) {\n      console.error('Error fetching driver vehicles:', error);\n      return [];\n    }\n  }\n\n  async addVehicle(vehicle: Omit<Vehicle, 'id' | 'created_at' | 'updated_at'>): Promise<Vehicle | null> {\n    try {\n      // In a real app, you would insert into Supabase\n      // For now, we'll just add to the mock vehicles\n      const newVehicle: Vehicle = {\n        ...vehicle,\n        id: Math.random().toString(36).substring(2, 9),\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n\n      const currentVehicles = this.vehiclesSubject.value;\n      this.vehiclesSubject.next([...currentVehicles, newVehicle]);\n\n      return newVehicle;\n    } catch (error) {\n      console.error('Error adding vehicle:', error);\n      return null;\n    }\n  }\n\n  async updateVehicle(id: string, updates: Partial<Vehicle>): Promise<boolean> {\n    try {\n      // In a real app, you would update in Supabase\n      // For now, we'll update the mock vehicles\n      const currentVehicles = this.vehiclesSubject.value;\n      const updatedVehicles = currentVehicles.map(vehicle =>\n        vehicle.id === id ? {\n          ...vehicle,\n          ...updates,\n          updated_at: new Date().toISOString()\n        } : vehicle\n      );\n\n      this.vehiclesSubject.next(updatedVehicles);\n      return true;\n    } catch (error) {\n      console.error('Error updating vehicle:', error);\n      return false;\n    }\n  }\n\n  async deleteVehicle(id: string): Promise<boolean> {\n    try {\n      // In a real app, you would delete from Supabase\n      // For now, we'll remove from the mock vehicles\n      const currentVehicles = this.vehiclesSubject.value;\n      const updatedVehicles = currentVehicles.filter(vehicle => vehicle.id !== id);\n\n      this.vehiclesSubject.next(updatedVehicles);\n      return true;\n    } catch (error) {\n      console.error('Error deleting vehicle:', error);\n      return false;\n    }\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatIconModule } from '@angular/material/icon';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { VehicleService } from '../../../../core/services/vehicle.service';\nimport { Vehicle } from '../../../../core/models/vehicle.model';\nimport { User } from '../../../../core/models/user.model';\n\n@Component({\n  selector: 'app-driver-profile',\n  standalone: true,\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatSnackBarModule,\n    MatDividerModule,\n    MatIconModule\n  ],\n  template: `\n    <div class=\"profile-container\">\n      <mat-card>\n        <mat-card-header>\n          <mat-card-title>Driver Profile</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <form [formGroup]=\"profileForm\" (ngSubmit)=\"updateProfile()\">\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Full Name</mat-label>\n              <input matInput formControlName=\"full_name\" placeholder=\"Enter your full name\">\n              <mat-error *ngIf=\"profileForm.get('full_name')?.hasError('required')\">\n                Full name is required\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Phone Number</mat-label>\n              <input matInput formControlName=\"phone\" placeholder=\"Enter your phone number\">\n              <mat-error *ngIf=\"profileForm.get('phone')?.hasError('required')\">\n                Phone number is required\n              </mat-error>\n            </mat-form-field>\n\n            <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"profileForm.invalid || profileForm.pristine\">\n              Update Profile\n            </button>\n          </form>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- <mat-card class=\"vehicle-card\">\n        <mat-card-header>\n          <mat-card-title>Vehicle Information</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div *ngIf=\"vehicles.length === 0\" class=\"no-vehicles\">\n            <p>No vehicles added yet.</p>\n          </div>\n\n          <div *ngFor=\"let vehicle of vehicles\" class=\"vehicle-item\">\n            <div class=\"vehicle-details\">\n              <h3>{{ vehicle.year }} {{ vehicle.make }} {{ vehicle.model }}</h3>\n              <p>Color: {{ vehicle.color }}</p>\n              <p>License Plate: {{ vehicle.license_plate }}</p>\n              <p>Passenger Capacity: {{ vehicle.capacity }}</p>\n            </div>\n            <div class=\"vehicle-actions\">\n              <button mat-icon-button color=\"primary\" (click)=\"editVehicle(vehicle)\">\n                <mat-icon>edit</mat-icon>\n              </button>\n              <button mat-icon-button color=\"warn\" (click)=\"deleteVehicle(vehicle.id)\">\n                <mat-icon>delete</mat-icon>\n              </button>\n            </div>\n          </div>\n\n          <mat-divider *ngIf=\"vehicles.length > 0\" class=\"divider\"></mat-divider>\n\n          <form [formGroup]=\"vehicleForm\" (ngSubmit)=\"saveVehicle()\" class=\"vehicle-form\">\n            <h3>{{ editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle' }}</h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Make</mat-label>\n                <input matInput formControlName=\"make\" placeholder=\"e.g. Toyota\">\n                <mat-error *ngIf=\"vehicleForm.get('make')?.hasError('required')\">\n                  Make is required\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Model</mat-label>\n                <input matInput formControlName=\"model\" placeholder=\"e.g. Camry\">\n                <mat-error *ngIf=\"vehicleForm.get('model')?.hasError('required')\">\n                  Model is required\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Year</mat-label>\n                <input matInput formControlName=\"year\" type=\"number\" placeholder=\"e.g. 2020\">\n                <mat-error *ngIf=\"vehicleForm.get('year')?.hasError('required')\">\n                  Year is required\n                </mat-error>\n                <mat-error *ngIf=\"vehicleForm.get('year')?.hasError('min') || vehicleForm.get('year')?.hasError('max')\">\n                  Year must be between 1990 and {{ currentYear }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Color</mat-label>\n                <input matInput formControlName=\"color\" placeholder=\"e.g. Silver\">\n                <mat-error *ngIf=\"vehicleForm.get('color')?.hasError('required')\">\n                  Color is required\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>License Plate</mat-label>\n                <input matInput formControlName=\"license_plate\" placeholder=\"e.g. ABC123\">\n                <mat-error *ngIf=\"vehicleForm.get('license_plate')?.hasError('required')\">\n                  License plate is required\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Passenger Capacity</mat-label>\n                <input matInput formControlName=\"capacity\" type=\"number\" placeholder=\"e.g. 4\">\n                <mat-error *ngIf=\"vehicleForm.get('capacity')?.hasError('required')\">\n                  Capacity is required\n                </mat-error>\n                <mat-error *ngIf=\"vehicleForm.get('capacity')?.hasError('min') || vehicleForm.get('capacity')?.hasError('max')\">\n                  Capacity must be between 1 and 10\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <div class=\"form-actions\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"vehicleForm.invalid\">\n                {{ editingVehicle ? 'Update Vehicle' : 'Add Vehicle' }}\n              </button>\n              <button mat-button type=\"button\" *ngIf=\"editingVehicle\" (click)=\"cancelEdit()\">\n                Cancel\n              </button>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card> -->\n    </div>\n  `,\n  styles: [`\n    .profile-container {\n      padding: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n\n    .full-width {\n      width: 100%;\n      margin-bottom: 16px;\n    }\n\n    .vehicle-card {\n      margin-top: 20px;\n    }\n\n    .vehicle-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px 0;\n      border-bottom: 1px solid #eee;\n    }\n\n    .vehicle-item:last-child {\n      border-bottom: none;\n    }\n\n    .vehicle-details h3 {\n      margin: 0 0 8px 0;\n      font-weight: 500;\n    }\n\n    .vehicle-details p {\n      margin: 4px 0;\n      color: #666;\n    }\n\n    .vehicle-actions {\n      display: flex;\n      gap: 8px;\n    }\n\n    .divider {\n      margin: 16px 0;\n    }\n\n    .vehicle-form {\n      margin-top: 16px;\n    }\n\n    .form-row {\n      display: flex;\n      gap: 16px;\n      margin-bottom: 8px;\n    }\n\n    .form-row mat-form-field {\n      flex: 1;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 8px;\n      margin-top: 16px;\n    }\n\n    .no-vehicles {\n      padding: 16px 0;\n      color: #666;\n      font-style: italic;\n    }\n  `]\n})\nexport class DriverProfileComponent implements OnInit {\n  profileForm: FormGroup;\n  vehicleForm: FormGroup;\n  vehicles: Vehicle[] = [];\n  currentUser: User | null = null;\n  editingVehicle: Vehicle | null = null;\n  currentYear = new Date().getFullYear();\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private vehicleService: VehicleService,\n    private snackBar: MatSnackBar\n  ) {\n    this.profileForm = this.fb.group({\n      full_name: ['', Validators.required],\n      phone: ['', Validators.required]\n    });\n\n    this.vehicleForm = this.fb.group({\n      make: ['', Validators.required],\n      model: ['', Validators.required],\n      year: ['', [Validators.required, Validators.min(1990), Validators.max(this.currentYear)]],\n      color: ['', Validators.required],\n      license_plate: ['', Validators.required],\n      capacity: [4, [Validators.required, Validators.min(1), Validators.max(10)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadUserProfile();\n    this.loadVehicles();\n  }\n\n  async loadUserProfile(): Promise<void> {\n    try {\n      this.currentUser = await this.authService.getCurrentUser();\n      if (this.currentUser) {\n        this.profileForm.patchValue({\n          full_name: this.currentUser.full_name || '',\n          phone: this.currentUser.phone || ''\n        });\n      }\n    } catch (error) {\n      console.error('Error loading user profile:', error);\n      this.snackBar.open('Failed to load profile', 'Close', { duration: 3000 });\n    }\n  }\n\n  async loadVehicles(): Promise<void> {\n    try {\n      if (this.currentUser) {\n        this.vehicles = await this.vehicleService.getDriverVehicles(this.currentUser.id);\n      }\n    } catch (error) {\n      console.error('Error loading vehicles:', error);\n      this.snackBar.open('Failed to load vehicles', 'Close', { duration: 3000 });\n    }\n  }\n\n  async updateProfile(): Promise<void> {\n    if (this.profileForm.invalid) return;\n\n    try {\n      const success = await this.authService.updateProfile(this.profileForm.value);\n      if (success) {\n        this.snackBar.open('Profile updated successfully', 'Close', { duration: 3000 });\n      } else {\n        throw new Error('Failed to update profile');\n      }\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      this.snackBar.open('Failed to update profile', 'Close', { duration: 3000 });\n    }\n  }\n\n  editVehicle(vehicle: Vehicle): void {\n    this.editingVehicle = vehicle;\n    this.vehicleForm.patchValue({\n      make: vehicle.make,\n      model: vehicle.model,\n      year: vehicle.year,\n      color: vehicle.color,\n      license_plate: vehicle.license_plate,\n      capacity: vehicle.capacity\n    });\n  }\n\n  cancelEdit(): void {\n    this.editingVehicle = null;\n    this.vehicleForm.reset({\n      capacity: 4\n    });\n  }\n\n  async saveVehicle(): Promise<void> {\n    if (this.vehicleForm.invalid || !this.currentUser) return;\n\n    try {\n      if (this.editingVehicle) {\n        // Update existing vehicle\n        const success = await this.vehicleService.updateVehicle(\n          this.editingVehicle.id,\n          this.vehicleForm.value\n        );\n        \n        if (success) {\n          this.snackBar.open('Vehicle updated successfully', 'Close', { duration: 3000 });\n          this.cancelEdit();\n          this.loadVehicles();\n        } else {\n          throw new Error('Failed to update vehicle');\n        }\n      } else {\n        // Add new vehicle\n        const newVehicle = {\n          ...this.vehicleForm.value,\n          driver_id: this.currentUser.id\n        };\n        \n        const result = await this.vehicleService.addVehicle(newVehicle);\n        if (result) {\n          this.snackBar.open('Vehicle added successfully', 'Close', { duration: 3000 });\n          this.vehicleForm.reset({\n            capacity: 4\n          });\n          this.loadVehicles();\n        } else {\n          throw new Error('Failed to add vehicle');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving vehicle:', error);\n      this.snackBar.open('Failed to save vehicle', 'Close', { duration: 3000 });\n    }\n  }\n\n  async deleteVehicle(vehicleId: string): Promise<void> {\n    try {\n      const success = await this.vehicleService.deleteVehicle(vehicleId);\n      if (success) {\n        this.snackBar.open('Vehicle deleted successfully', 'Close', { duration: 3000 });\n        this.loadVehicles();\n      } else {\n        throw new Error('Failed to delete vehicle');\n      }\n    } catch (error) {\n      console.error('Error deleting vehicle:', error);\n      this.snackBar.open('Failed to delete vehicle', 'Close', { duration: 3000 });\n    }\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DriverPayout } from '../../../../core/models/payout.model';\n\n@Component({\n  selector: 'app-driver-earnings',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatTableModule,\n    MatChipsModule,\n    MatIconModule,\n    MatButtonModule,\n    MatProgressSpinnerModule,\n    MatTooltipModule\n  ],\n  template: '<div class=\"earnings-container\">\\n      <mat-card class=\"summary-card\">\\n        <mat-card-header>\\n          <mat-card-title>Earnings Summary</mat-card-title>\\n        </mat-card-header>\\n        <mat-card-content>\\n          <div class=\"summary-grid\">\\n            <div class=\"summary-item\">\\n              <div class=\"summary-label\">Total Earnings</div>\\n              <div class=\"summary-value\">{{ totalEarnings.toFixed(2) }}</div>\\n            </div>\\n            <div class=\"summary-item\">\\n              <div class=\"summary-label\">Pending Payouts</div>\\n              <div class=\"summary-value\">{{ pendingEarnings.toFixed(2) }}</div>\\n            </div>\\n            <div class=\"summary-item\">\\n              <div class=\"summary-label\">Completed Rides</div>\\n              <div class=\"summary-value\">{{ completedRides }}</div>\\n            </div>\\n          </div>\\n        </mat-card-content>\\n      </mat-card>\\n\\n      <mat-card class=\"payouts-card\">\\n        <mat-card-header>\\n          <mat-card-title>Payout History</mat-card-title>\\n          <button mat-icon-button color=\"primary\" (click)=\"loadPayouts()\" matTooltip=\"Refresh payouts\">\\n            <mat-icon>refresh</mat-icon>\\n          </button>\\n        </mat-card-header>\\n        <mat-card-content>\\n          <div *ngIf=\"loading\" class=\"loading-container\">\\n            <mat-spinner diameter=\"40\"></mat-spinner>\\n            <p>Loading payouts...</p>\\n          </div>\\n\\n          <div *ngIf=\"!loading && payouts.length === 0\" class=\"no-payouts\">\\n            <p>No payouts found.</p>\\n          </div>\\n\\n          <table mat-table [dataSource]=\"payouts\" class=\"payouts-table\" *ngIf=\"!loading && payouts.length > 0\">\\n            <ng-container matColumnDef=\"date\">\\n              <th mat-header-cell *matHeaderCellDef>Date</th>\\n              <td mat-cell *matCellDef=\"let payout\">{{ payout.created_at | date:\\'medium\\' }}</td>\\n            </ng-container>\\n\\n            <ng-container matColumnDef=\"ride_id\">\\n              <th mat-header-cell *matHeaderCellDef>Ride ID</th>\\n              <td mat-cell *matCellDef=\"let payout\">{{ payout.ride_id | slice:0:8 }}...</td>\\n            </ng-container>\\n\\n            <ng-container matColumnDef=\"amount\">\\n              <th mat-header-cell *matHeaderCellDef>Amount</th>\\n              <td mat-cell *matCellDef=\"let payout\">{{ payout.amount.toFixed(2) }}</td>\\n            </ng-container>\\n\\n            <ng-container matColumnDef=\"status\">\\n              <th mat-header-cell *matHeaderCellDef>Status</th>\\n              <td mat-cell *matCellDef=\"let payout\">\\n                <span class=\"status-chip\" [ngClass]=\"\\'status-\\' + payout.status\">\\n                  {{ payout.status }}\\n                </span>\\n              </td>\\n            </ng-container>\\n\\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\"></tr>\\n          </table>\\n        </mat-card-content>\\n      </mat-card>\\n    </div>',\n  styles: [`\n    .earnings-container {\n      display: flex;\n      flex-direction: column;\n      gap: 20px;\n      padding: 20px;\n      max-width: 1000px;\n      margin: 0 auto;\n    }\n\n    .summary-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 20px;\n      margin-top: 16px;\n    }\n\n    .summary-item {\n      background-color: #f5f5f5;\n      border-radius: 8px;\n      padding: 16px;\n      text-align: center;\n    }\n\n    .summary-label {\n      font-size: 0.9em;\n      color: rgba(0, 0, 0, 0.6);\n      margin-bottom: 8px;\n    }\n\n    .summary-value {\n      font-size: 1.8em;\n      font-weight: 500;\n      color: #3f51b5;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n    }\n\n    .no-payouts {\n      text-align: center;\n      padding: 20px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .payouts-table {\n      width: 100%;\n    }\n\n    .status-chip {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 16px;\n      font-size: 0.85em;\n      text-transform: capitalize;\n    }\n\n    .status-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .status-paid {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-failed {\n      background-color: #f44336;\n      color: white;\n    }\n  `]\n})\nexport class DriverEarningsComponent implements OnInit {\n  payouts: DriverPayout[] = [];\n  displayedColumns: string[] = ['date', 'ride_id', 'amount', 'status'];\n  loading = false;\n  totalEarnings = 0;\n  pendingEarnings = 0;\n  completedRides = 0;\n\n  constructor(\n    private paymentService: PaymentService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadPayouts();\n  }\n\n  async loadPayouts(): Promise<void> {\n    this.loading = true;\n\n    try {\n      const user = await this.authService.getCurrentUser();\n      if (!user) throw new Error('User not found');\n\n      // Load payouts\n      this.payouts = await this.paymentService.getDriverPayouts(user.id);\n\n      // Calculate earnings\n      this.totalEarnings = await this.paymentService.getDriverTotalEarnings(user.id);\n      this.pendingEarnings = await this.paymentService.getDriverPendingEarnings(user.id);\n\n      // Count completed rides (based on payouts)\n      this.completedRides = this.payouts.length;\n    } catch (error) {\n      console.error('Error loading payouts:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n}\n", "import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { Ride } from '../../../../core/models/ride.model';\n\nimport { MapDisplayComponent } from '../../../../shared/components/map-display/map-display.component';\nimport { LocationService } from '../../../../core/services/location.service';\n\n@Component({\n  selector: 'app-ride-navigation',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MapDisplayComponent\n  ],\n  template: `\n    <div class=\"navigation-overlay\" *ngIf=\"ride\">\n      <mat-card class=\"navigation-card\">\n        <mat-card-header>\n          <mat-card-title>Navigation</mat-card-title>\n          <button mat-icon-button class=\"close-button\" (click)=\"closeNavigation()\">\n            <mat-icon>close</mat-icon>\n          </button>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"navigation-details\">\n            <div class=\"location-info\">\n              <div class=\"location-item\">\n                <mat-icon class=\"location-icon pickup\">location_on</mat-icon>\n                <div class=\"location-text\">\n                  <span class=\"location-label\">Pickup Location:</span>\n                  <span class=\"location-value\">{{ ride.pickup_location }}</span>\n                </div>\n              </div>\n              <div class=\"location-item\">\n                <mat-icon class=\"location-icon dropoff\">flag</mat-icon>\n                <div class=\"location-text\">\n                  <span class=\"location-label\">Dropoff Location:</span>\n                  <span class=\"location-value\">{{ ride.dropoff_location }}</span>\n                </div>\n              </div>\n            </div>\n\n            <app-map-display\n              [origin]=\"ride.pickup_location\"\n              [destination]=\"ride.dropoff_location\">\n            </app-map-display>\n\n            <div *ngIf=\"ride.distance_miles && ride.duration_minutes\" class=\"route-info\">\n              <p><strong>Distance:</strong> {{ ride.distance_miles }} miles</p>\n              <p><strong>Estimated Time:</strong> {{ ride.duration_minutes }} minutes</p>\n            </div>\n\n            <div class=\"navigation-links\">\n              <a [href]=\"googleMapsPickupUrl\" target=\"_blank\" class=\"nav-link\">\n                <button mat-raised-button color=\"primary\">\n                  <mat-icon>navigation</mat-icon>\n                  Navigate to Pickup\n                </button>\n              </a>\n              <a [href]=\"googleMapsDropoffUrl\" target=\"_blank\" class=\"nav-link\">\n                <button mat-raised-button color=\"accent\">\n                  <mat-icon>navigation</mat-icon>\n                  Navigate to Dropoff\n                </button>\n              </a>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .navigation-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .navigation-card {\n      width: 90%;\n      max-width: 600px;\n      max-height: 80vh;\n      overflow-y: auto;\n    }\n\n    .close-button {\n      position: absolute;\n      right: 8px;\n      top: 8px;\n    }\n\n    .navigation-details {\n      padding: 16px 0;\n    }\n\n    .location-info {\n      margin-bottom: 24px;\n    }\n\n    .location-item {\n      display: flex;\n      align-items: flex-start;\n      margin-bottom: 16px;\n    }\n\n    .location-icon {\n      margin-right: 16px;\n      color: #3f51b5;\n    }\n\n    .location-icon.pickup {\n      color: #4caf50;\n    }\n\n    .location-icon.dropoff {\n      color: #f44336;\n    }\n\n    .location-text {\n      display: flex;\n      flex-direction: column;\n    }\n\n    .location-label {\n      font-weight: 500;\n      margin-bottom: 4px;\n    }\n\n    .location-value {\n      color: #666;\n    }\n\n    .route-info {\n      background-color: #f5f5f5;\n      border-radius: 4px;\n      padding: 16px;\n      margin-bottom: 24px;\n    }\n\n    .route-info p {\n      margin: 8px 0;\n    }\n\n    .navigation-links {\n      display: flex;\n      justify-content: space-around;\n      flex-wrap: wrap;\n      gap: 16px;\n    }\n\n    .nav-link {\n      text-decoration: none;\n    }\n  `]\n})\nexport class RideNavigationComponent implements OnInit {\n  @Input() ride: Ride | null = null;\n  @Output() close = new EventEmitter<void>();\n\n  googleMapsPickupUrl: string = '';\n  googleMapsDropoffUrl: string = '';\n\n  constructor(private locationService: LocationService) {}\n\n  ngOnInit(): void {\n    this.generateNavigationLinks();\n  }\n\n  generateNavigationLinks(): void {\n    if (!this.ride) return;\n\n    // Create Google Maps navigation links using the LocationService\n    this.googleMapsPickupUrl = this.locationService.getGoogleMapsUrl(this.ride.pickup_location);\n    this.googleMapsDropoffUrl = this.locationService.getGoogleMapsUrl(this.ride.dropoff_location);\n  }\n\n  closeNavigation(): void {\n    this.close.emit();\n  }\n}\n", "import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, signal, ViewChild, AfterViewInit, computed, effect } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Ride, RideStatus } from '../../../../core/models/ride.model';\nimport { User } from '../../../../core/models/user.model';\nimport { RideNavigationComponent } from '../ride-navigation/ride-navigation.component';\nimport { MessageService } from '../../../../core/services/message.service';\nimport { RideDetailComponent } from '../../../../shared/components/ride-detail/ride-detail.component';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatExpansionModule } from '@angular/material/expansion';\n\n@Component({\n  selector: 'app-ride-assignments',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatTabsModule,\n    MatTableModule,\n    MatChipsModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSnackBarModule,\n    MatBadgeModule,\n    MatTooltipModule,\n    MatProgressSpinnerModule,\n    RideNavigationComponent,\n    RideDetailComponent,\n    MatSortModule,\n    MatPaginatorModule,\n    MatExpansionModule\n  ],\n  template: `\n    <div class=\"assignments-container\">\n      <mat-tab-group>\n        <mat-tab label=\"Available Rides\" [tabIndex]=\"0\">\n          <div class=\"table-container\">\n            <div class=\"header-with-actions\">\n              <h3>Available Ride Requests <span class=\"realtime-indicator\" title=\"Realtime updates active\">●</span></h3>\n              <button mat-icon-button color=\"primary\" (click)=\"loadRides()\" matTooltip=\"Manual refresh\">\n                <mat-icon>refresh</mat-icon>\n              </button>\n            </div>\n            <div *ngIf=\"loading\" class=\"loading-container\">\n              <mat-spinner diameter=\"40\"></mat-spinner>\n              <p>Loading rides...</p>\n            </div> \n            \n            <div *ngIf=\"!loading && availableRides().length === 0\" class=\"no-rides\">\n              <p>No available ride requests at this time.</p>\n            </div>\n\n            <ng-container *ngIf=\"!loading && availableRides().length > 0\">\n              <div class=\"desktop-view\">\n                <table mat-table [dataSource]=\"availableDataSource\"  #availableSort=\"matSort\"  matSort class=\"ride-table\">\n                  <ng-container matColumnDef=\"pickup_location\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.pickup_location }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"dropoff_location\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.dropoff_location }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"pickup_time\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.pickup_time | date:'short' }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"fare\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.fare ? ( ride.fare * .7 | currency:'USD') : 'TBD' }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"actions\">\n                    <th mat-header-cell *matHeaderCellDef>Actions</th>\n                    <td mat-cell *matCellDef=\"let ride\">\n                      <button mat-raised-button color=\"primary\" (click)=\"acceptRide(ride.id)\">\n                        Accept\n                      </button>\n                    </td>\n                  </ng-container>\n    \n                  <tr mat-header-row *matHeaderRowDef=\"availableColumns\"></tr>\n                  <tr mat-row *matRowDef=\"let row; columns: availableColumns;\"></tr>\n                </table>\n                <mat-paginator #availablePaginator [pageSizeOptions]=\"[3,5, 10, 25, 100]\" [pageSize]=\"3\" showFirstLastButtons aria-label=\"Select page of available rides\"></mat-paginator>\n              </div>\n\n              <div class=\"mobile-view\">\n                <mat-accordion multi>\n                  <mat-expansion-panel *ngFor=\"let ride of availableRides()\">\n                    <mat-expansion-panel-header>\n                      <mat-panel-title>\n                        {{ ride.pickup_location }}\n                      </mat-panel-title>\n                      <mat-panel-description>\n                        {{ ride.pickup_time | date:'shortTime' }}\n                      </mat-panel-description>\n                    </mat-expansion-panel-header>\n                    <div class=\"ride-details\">\n                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>\n                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>\n                    </div>\n                    <mat-action-row>\n                      <button mat-raised-button color=\"primary\" (click)=\"acceptRide(ride.id)\">\n                        Accept\n                      </button>\n                    </mat-action-row>\n                  </mat-expansion-panel>\n                </mat-accordion>\n              </div>\n            </ng-container>\n          </div>\n        </mat-tab>\n\n        <mat-tab label=\"My Rides\" [tabIndex]=\"1\" [disabled]=\"myRides().length === 0\">\n          <div class=\"table-container\">\n            <div class=\"header-with-actions\">\n              <h3>My Assigned Rides <span class=\"realtime-indicator\" title=\"Realtime updates active\">●</span></h3>\n              <button mat-icon-button color=\"primary\" (click)=\"loadRides()\" matTooltip=\"Manual refresh\">\n                <mat-icon>refresh</mat-icon>\n              </button>\n            </div>\n            \n            <div class=\"filter-container\" *ngIf=\"myRides().length > 0\">\n              <div class=\"filter-buttons\" aria-label=\"Filter rides by status\">\n                <button mat-stroked-button\n                        [color]=\"statusFilter() === 'all' ? 'primary' : ''\"\n                        (click)=\"statusFilter.set('all')\"\n                        class=\"filter-button\">All</button>\n                <button mat-stroked-button\n                        [color]=\"statusFilter() === 'assigned' ? 'primary' : ''\"\n                        (click)=\"statusFilter.set('assigned')\"\n                        class=\"filter-button\">Assigned</button>\n                <button mat-stroked-button\n                        [color]=\"statusFilter() === 'in-progress' ? 'primary' : ''\"\n                        (click)=\"statusFilter.set('in-progress')\"\n                        class=\"filter-button\">In Progress</button>\n                <button mat-stroked-button\n                        [color]=\"statusFilter() === 'completed' ? 'primary' : ''\"\n                        (click)=\"statusFilter.set('completed')\"\n                        class=\"filter-button\">Completed</button>\n              </div>\n            </div>\n\n            <div *ngIf=\"loading\" class=\"loading-container\">\n              <mat-spinner diameter=\"40\"></mat-spinner>\n              <p>Loading rides...</p>\n            </div>\n            <div *ngIf=\"!loading && myRides().length === 0\" class=\"no-rides\">\n              <p>You don't have any assigned rides.</p>\n            </div>\n            <div *ngIf=\"!loading && filteredMyRides().length === 0 && myRides().length > 0\" class=\"no-rides\">\n              <p>No rides match the current filter.</p>\n            </div>\n\n            <ng-container *ngIf=\"!loading && filteredMyRides().length > 0\">\n              <div class=\"desktop-view\">\n                <table mat-table [dataSource]=\"myRidesDataSource\" matSort #myRidesSort=\"matSort\" class=\"ride-table\">\n                  <ng-container matColumnDef=\"pickup_location\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.pickup_location }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"dropoff_location\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.dropoff_location }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"pickup_time\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.pickup_time | date:'short' }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"status\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>\n                    <td mat-cell *matCellDef=\"let ride\">\n                      <span class=\"status-chip\" [ngClass]=\"'status-' + ride.status\">\n                        {{ ride.status }}\n                      </span>\n                    </td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"fare\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>\n                    <td mat-cell *matCellDef=\"let ride\">{{ ride.fare ? (ride.fare * .7 | currency:'USD') : 'TBD' }}</td>\n                  </ng-container>\n    \n                  <ng-container matColumnDef=\"actions\">\n                    <th mat-header-cell *matHeaderCellDef>Actions</th>\n                    <td mat-cell *matCellDef=\"let ride\">\n                      <button mat-raised-button color=\"primary\" *ngIf=\"ride.status === 'assigned'\" (click)=\"startRide(ride.id)\">\n                        Start Ride\n                      </button>\n                      <button mat-raised-button color=\"accent\" *ngIf=\"ride.status === 'in-progress'\" (click)=\"completeRide(ride.id)\">\n                        Complete\n                      </button>\n                      <button mat-icon-button color=\"primary\" *ngIf=\"ride.status !== 'completed'\" (click)=\"showNavigation(ride)\">\n                        <mat-icon>navigation</mat-icon>\n                      </button>\n                      <button mat-icon-button color=\"primary\" (click)=\"viewRideDetails(ride.id)\" matTooltip=\"View Details\">\n                        <mat-icon>visibility</mat-icon>\n                      </button>\n                    </td>\n                  </ng-container>\n    \n                  <tr mat-header-row *matHeaderRowDef=\"myRidesColumns\"></tr>\n                  <tr mat-row *matRowDef=\"let row; columns: myRidesColumns;\"></tr>\n                </table>\n                <mat-paginator #myRidesPaginator [pageSizeOptions]=\"[5, 10, 25, 100]\" [pageSize]=\"5\" showFirstLastButtons aria-label=\"Select page of my rides\"></mat-paginator>\n              </div>\n\n              <div class=\"mobile-view\">\n                <mat-accordion multi>\n                  <mat-expansion-panel *ngFor=\"let ride of filteredMyRides()\">\n                    <mat-expansion-panel-header>\n                      <mat-panel-title>\n                        {{ ride.pickup_location }}\n                      </mat-panel-title>\n                      <mat-panel-description>\n                        <div class=\"ride-actions-header\">\n                          <span class=\"status-chip\" [ngClass]=\"'status-' + ride.status\">{{ ride.status }}</span>\n                          <button mat-icon-button color=\"primary\" *ngIf=\"ride.status === 'assigned'\" (click)=\"startRide(ride.id); $event.stopPropagation()\" matTooltip=\"Start Ride\">\n                            <mat-icon>play_arrow</mat-icon>\n                          </button>\n                          <button mat-icon-button color=\"accent\" *ngIf=\"ride.status === 'in-progress'\" (click)=\"completeRide(ride.id); $event.stopPropagation()\" matTooltip=\"Complete Ride\">\n                            <mat-icon>check_circle</mat-icon>\n                          </button>\n                        </div>\n                      </mat-panel-description>\n                    </mat-expansion-panel-header>\n                    <div class=\"ride-details\">\n                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>\n                      <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>\n                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>\n                    </div>\n                    <mat-action-row>\n                      <button mat-icon-button color=\"primary\" *ngIf=\"ride.status !== 'completed'\" (click)=\"showNavigation(ride)\" matTooltip=\"Navigation\">\n                        <mat-icon>navigation</mat-icon>\n                      </button>\n                      <button mat-icon-button color=\"primary\" (click)=\"viewRideDetails(ride.id)\" matTooltip=\"View Details\">\n                        <mat-icon>visibility</mat-icon>\n                      </button>\n                    </mat-action-row>\n                  </mat-expansion-panel>\n                </mat-accordion>\n              </div>\n            </ng-container>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n\n      <app-ride-navigation\n        *ngIf=\"selectedRide\"\n        [ride]=\"selectedRide\"\n        (close)=\"selectedRide = null\">\n      </app-ride-navigation>\n\n      <div *ngIf=\"selectedRideId\" class=\"ride-detail-overlay\">\n        <app-ride-detail\n          [rideId]=\"selectedRideId\"\n          [onClose]=\"closeRideDetails.bind(this)\"\n          (rideUpdated)=\"onRideUpdated($event)\">\n        </app-ride-detail>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .assignments-container {\n      padding: 20px;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .table-container {\n      margin: 20px;\n    }\n\n    .ride-table {\n      width: 100%;\n    }\n\n    \n    .no-rides {\n      padding: 20px;\n      text-align: center;\n      color: #666;\n      font-style: italic;\n    }\n\n    .status-chip {\n      border-radius: 16px;\n      padding: 4px 12px;\n      color: white;\n      font-weight: 500;\n    }\n\n    .status-requested {\n      background-color: #ff9800;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n    }\n\n    .status-in-progress {\n      background-color: #673ab7;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n    }\n\n    .header-with-actions {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n    }\n\n    .loading-container {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: 20px;\n      color: #666;\n    }\n\n    .loading-container p {\n      margin-top: 10px;\n    }\n\n    .ride-detail-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background-color: rgba(0, 0, 0, 0.5);\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      z-index: 1000;\n    }\n\n    .realtime-indicator {\n      color: #4caf50;\n      font-size: 12px;\n      animation: pulse 2s infinite;\n    }\n\n    @keyframes pulse {\n      0% { opacity: 1; }\n      50% { opacity: 0.5; }\n      100% { opacity: 1; }\n    }\n\n    .desktop-view {\n      display: block;\n    }\n    .mobile-view {\n      display: none;\n    }\n\n    .filter-container {\n      margin: 0 20px 20px;\n    }\n    \n    .ride-actions-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    @media (max-width: 600px) {\n      .desktop-view {\n        display: none;\n      }\n      .mobile-view {\n        display: block;\n      }\n      .assignments-container {\n        padding: 0;\n      }\n      .table-container {\n        margin: 0;\n      }\n      .mat-tab-body-content {\n        overflow: hidden;\n      }\n      .filter-container {\n        margin: 0 0 16px;\n      }\n    }\n\n    .mobile-view .mat-expansion-panel {\n      margin: 8px 0;\n    }\n    .mobile-view .mat-expansion-panel-header {\n      font-size: 14px;\n    }\n    .mobile-view .mat-panel-title {\n      font-weight: 500;\n    }\n    .mobile-view .mat-panel-description {\n      justify-content: flex-end;\n      align-items: center;\n    }\n    .mobile-view .ride-details {\n      padding: 0 24px 16px;\n      font-size: 14px;\n    }\n    .mobile-view .ride-details p {\n      margin: 4px 0;\n    }\n    .mobile-view .mat-action-row {\n      justify-content: flex-end;\n      padding: 8px 12px 8px 24px;\n    }\n  `]\n})\nexport class RideAssignmentsComponent implements OnInit, OnDestroy, AfterViewInit {\n\n  currentUser: User | null = null;\n  selectedRide: Ride | null = null;\n  selectedRideId: string | null = null;\n  loading: boolean = false;\n  private ridesSubscription: Subscription | null = null;\n\n  availableRides = signal<Ride[]>([]);\n  myRides = signal<Ride[]>([]);\n  \n  statusFilter = signal<RideStatus | 'all'>('all');\n  filteredMyRides = computed(() => {\n    const rides = this.myRides();\n    const filter = this.statusFilter();\n    if (filter === 'all') {\n      return rides;\n    }\n    return rides.filter(ride => ride.status === filter);\n  });\n\n  availableColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'fare', 'actions'];\n  myRidesColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'status', 'fare', 'actions'];\n\n  availableDataSource = new MatTableDataSource<Ride>([]);\n  myRidesDataSource = new MatTableDataSource<Ride>([]);\n\n  @ViewChild('availableSort') availableSort!: MatSort;\n  @ViewChild('availablePaginator') availablePaginator!: MatPaginator;\n  @ViewChild('myRidesSort') myRidesSort!: MatSort;\n  @ViewChild('myRidesPaginator') myRidesPaginator!: MatPaginator;\n\n  constructor(\n    private rideService: RideService,\n    private authService: AuthService,\n    private messageService: MessageService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) { \n    effect(() => {\n      const filter = this.statusFilter();\n      const allMyRides = this.myRides();\n      if (filter === 'all') {\n          this.myRidesDataSource.data = allMyRides;\n      } else {\n          this.myRidesDataSource.data = allMyRides.filter(ride => ride.status === filter);\n      }\n    });\n  }\n\n  async ngOnInit() {\n    try {\n      await this.loadCurrentUser();\n      if (this.currentUser) {\n        // Set up realtime subscription to rides\n        this.setupRealtimeSubscription();\n        // Initial load of rides\n        await this.loadRides();\n        console.log('✅ Driver dashboard initialized with realtime updates');\n      }\n    } catch (error) {\n      console.error('❌ Error loading current user:', error);\n      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });\n    }\n  }\n\n  ngAfterViewInit() {\n    // Setup data sources after view is initialized\n    setTimeout(() => {\n      this.setupDataSources();\n    }, 0);\n  }\n\n  ngOnDestroy(): void {\n    if (this.ridesSubscription) {\n      this.ridesSubscription.unsubscribe();\n    }\n  }\n\n\n\n\n  private async loadCurrentUser(): Promise<void> {\n    try {\n      this.loading = true;\n      this.currentUser = await this.authService.getCurrentUser();\n    } catch (error) {\n      console.error('Error loading current user:', error);\n      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  private setupRealtimeSubscription(): void {\n    if (!this.currentUser) return;\n\n    console.log('🔄 Setting up realtime subscription for driver:', this.currentUser.id);\n\n    this.ridesSubscription = this.rideService.rides$.subscribe((allRides: Ride[]) => {\n      if (allRides && allRides.length >= 0) {\n        const available = allRides.filter(ride => ride.status === 'requested');\n        const myRides = allRides.filter(ride =>\n          ride.driver_id === this.currentUser?.id &&\n          (['assigned', 'in-progress', 'completed'] as RideStatus[]).includes(ride.status)\n        );\n\n        this.availableRides.set(available);\n        this.myRides.set(myRides);\n\n        this.availableDataSource.data = available;\n        \n        const currentAvailableCount = this.availableDataSource.data.length;\n        if (available.length > currentAvailableCount && currentAvailableCount > 0) {\n          this.snackBar.open(`${available.length - currentAvailableCount} new ride(s) available!`, 'View', {\n            duration: 5000,\n            panelClass: ['success-snackbar']\n          });\n        }\n      }\n    });\n  }\n  private setupDataSources() {\n    console.log('Setting up data sources...');\n    if (this.availableSort) {\n      this.availableDataSource.sort = this.availableSort;\n    }\n    if (this.availablePaginator) {\n      this.availableDataSource.paginator = this.availablePaginator;\n    }\n\n    if (this.myRidesSort) {\n      this.myRidesDataSource.sort = this.myRidesSort;\n    }\n    if (this.myRidesPaginator) {\n      this.myRidesDataSource.paginator = this.myRidesPaginator;\n    }\n\n    this.availableDataSource.sortingDataAccessor = (item: Ride, property: string) => {\n      switch (property) {\n        case 'pickup_time':\n          return item[property] ? new Date(item[property]).getTime() : 0;\n        case 'fare':\n          return item.fare || 0;\n        case 'pickup_location':\n        case 'dropoff_location':\n          return (item as any)[property]?.toLowerCase() || '';\n        default:\n          return (item as any)[property] || '';\n      }\n    };\n\n    this.myRidesDataSource.sortingDataAccessor = (item: Ride, property: string) => {\n      switch (property) {\n        case 'pickup_time':\n          return item[property] ? new Date(item[property]).getTime() : 0;\n        case 'fare':\n          return item.fare || 0;\n        case 'pickup_location':\n        case 'dropoff_location':\n        case 'status':\n          return (item as any)[property]?.toLowerCase() || '';\n        default:\n          return (item as any)[property] || '';\n      }\n    };\n  }\n\n\n  async loadRides(): Promise<void> {\n    if (!this.currentUser) return;\n    console.log('🔄 Loading rides...');\n    this.loading = true;\n    try {\n      await this.rideService.getAllRides();\n\n      const [available, assigned] = await Promise.all([\n        this.rideService.getAvailableRides(),\n        this.rideService.getDriverRides(this.currentUser.id)\n      ]);\n\n      this.availableRides.set(available);\n      this.myRides.set(assigned);\n\n      this.availableDataSource.data = available;\n\n      setTimeout(() => {\n        this.setupDataSources();\n      }, 0);\n\n      this.snackBar.open('Rides refreshed', 'Close', { duration: 2000 });\n    } catch (error: any) {\n      console.error('❌ Error loading rides:', error);\n      this.snackBar.open(error.message || 'Failed to load rides', 'Close', { duration: 3000 });\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  async acceptRide(rideId: string): Promise<void> {\n    if (!this.currentUser) return;\n\n    try {\n      await this.rideService.acceptRide(rideId, this.currentUser.id);\n      this.snackBar.open('Ride accepted successfully', 'Close', { duration: 3000 });\n    } catch (error: any) {\n      console.error('Error accepting ride:', error);\n      this.snackBar.open(error.message || 'Failed to accept ride', 'Close', { duration: 3000 });\n    }\n  }\n\n  async startRide(rideId: string): Promise<void> {\n    try {\n      await this.rideService.startRide(rideId);\n      this.snackBar.open('Ride started successfully', 'Close', { duration: 3000 });\n    } catch (error: any) {\n      console.error('Error starting ride:', error);\n      this.snackBar.open(error.message || 'Failed to start ride', 'Close', { duration: 3000 });\n    }\n  }\n\n  async completeRide(rideId: string): Promise<void> {\n    try {\n      await this.rideService.completeRide(rideId);\n      this.snackBar.open('Ride completed successfully', 'Close', { duration: 3000 });\n    } catch (error: any) {\n      console.error('Error completing ride:', error);\n      this.snackBar.open(error.message || 'Failed to complete ride', 'Close', { duration: 3000 });\n    }\n  }\n\n  showNavigation(ride: Ride): void {\n    this.selectedRide = ride;\n  }\n\n  closeNavigation(): void {\n    this.selectedRide = null;\n  }\n\n  async openChat(rideId: string) {\n    try {\n      const thread = await this.messageService.getOrCreateThreadForRide(rideId);\n      await this.router.navigate(['/dashboard', 'driver', 'messages', thread.id]);\n    } catch (error: any) {\n      console.error('Error opening chat:', error);\n      this.snackBar.open(error.message || 'Failed to open chat', 'Close', { duration: 3000 });\n    }\n  }\n\n  viewRideDetails(rideId: string) {\n    this.selectedRideId = rideId;\n  }\n\n  closeRideDetails() {\n    this.selectedRideId = null;\n  }\n\n  onRideUpdated(_ride: Ride) {\n  }\n\n  trackByRideId(_index: number, item: Ride): string {\n    return item.id;\n  }\n}\n", "import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { DriverProfileComponent } from './driver-profile/driver-profile.component';\r\nimport { DriverEarningsComponent } from './driver-earnings/driver-earnings.component';\r\nimport { RideAssignmentsComponent } from './ride-assignments/ride-assignments.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-driver',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    MatCardModule,\r\n    MatTabsModule,\r\n    DriverProfileComponent,\r\n    RideAssignmentsComponent,\r\n    DriverEarningsComponent\r\n  ],\r\n  templateUrl: './driver.component.html',\r\n  styleUrl: './driver.component.scss'\r\n})\r\nexport class DriverComponent {}\r\n", "<div class=\"dashboard-container\">\r\n  <mat-card class=\"welcome-card\">\r\n    <mat-card-header>\r\n      <mat-card-title>Driver Dashboard</mat-card-title>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>Welcome to your driver dashboard! Here you can manage your profile, vehicle information, and ride assignments.</p>\r\n    </mat-card-content>\r\n  </mat-card>\r\n\r\n  <mat-tab-group>\r\n    <mat-tab label=\"Ride Assignments\">\r\n      <app-ride-assignments></app-ride-assignments>\r\n    </mat-tab>\r\n    <!-- <mat-tab label=\"Earnings\">\r\n      <app-driver-earnings></app-driver-earnings>\r\n    </mat-tab> -->\r\n    <mat-tab label=\"Driver Profile\">\r\n      <app-driver-profile></app-driver-profile>\r\n    </mat-tab>\r\n  </mat-tab-group>\r\n</div>\r\n<!-- driver_id: 5fa049a1-8e0f-41c1-9516-b8a9671e9ab8\r\nrider_id: e0b2b25c-017b-4d68-8231-aead2d273480 -->"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASM,IAAO,iBAAP,MAAO,gBAAc;EAKL;EAJZ;EACA,kBAAkB,IAAI,gBAA2B,CAAA,CAAE;EAC3D,YAAY,KAAK,gBAAgB,aAAY;EAE7C,YAAoB,aAAwB;AAAxB,SAAA,cAAA;AAClB,SAAK,WAAW,YAAY;AAI5B,SAAK,uBAAsB;EAC7B;EAEQ,yBAAsB;AAC5B,UAAM,eAA0B;MAC9B;QACE,IAAI;QACJ,WAAW;QACX,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,eAAe;QACf,UAAU;QACV,aAAY,oBAAI,KAAI,GAAG,YAAW;QAClC,aAAY,oBAAI,KAAI,GAAG,YAAW;;MAEpC;QACE,IAAI;QACJ,WAAW;QACX,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,eAAe;QACf,UAAU;QACV,aAAY,oBAAI,KAAI,GAAG,YAAW;QAClC,aAAY,oBAAI,KAAI,GAAG,YAAW;;;AAItC,SAAK,gBAAgB,KAAK,YAAY;EACxC;EAEM,kBAAkB,UAAgB;;AACtC,UAAI;AAGF,eAAO,KAAK,gBAAgB,MAAM,OAAO,aAAW,QAAQ,cAAc,QAAQ;MACpF,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,eAAO,CAAA;MACT;IACF;;EAEM,WAAW,SAA0D;;AACzE,UAAI;AAGF,cAAM,aAAsB,iCACvB,UADuB;UAE1B,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;UAC7C,aAAY,oBAAI,KAAI,GAAG,YAAW;UAClC,aAAY,oBAAI,KAAI,GAAG,YAAW;;AAGpC,cAAM,kBAAkB,KAAK,gBAAgB;AAC7C,aAAK,gBAAgB,KAAK,CAAC,GAAG,iBAAiB,UAAU,CAAC;AAE1D,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,eAAO;MACT;IACF;;EAEM,cAAc,IAAY,SAAyB;;AACvD,UAAI;AAGF,cAAM,kBAAkB,KAAK,gBAAgB;AAC7C,cAAM,kBAAkB,gBAAgB,IAAI,aAC1C,QAAQ,OAAO,KAAK,gDACf,UACA,UAFe;UAGlB,aAAY,oBAAI,KAAI,GAAG,YAAW;aAChC,OAAO;AAGb,aAAK,gBAAgB,KAAK,eAAe;AACzC,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,eAAO;MACT;IACF;;EAEM,cAAc,IAAU;;AAC5B,UAAI;AAGF,cAAM,kBAAkB,KAAK,gBAAgB;AAC7C,cAAM,kBAAkB,gBAAgB,OAAO,aAAW,QAAQ,OAAO,EAAE;AAE3E,aAAK,gBAAgB,KAAK,eAAe;AACzC,eAAO;MACT,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,eAAO;MACT;IACF;;;qCA9GW,iBAAc,mBAAA,WAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;;;;;ACgCa,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;AAMA,IAAA,yBAAA,GAAA,WAAA;AACE,IAAA,iBAAA,GAAA,4BAAA;AACF,IAAA,uBAAA;;;AA4LR,IAAO,yBAAP,MAAO,wBAAsB;EASvB;EACA;EACA;EACA;EAXV;EACA;EACA,WAAsB,CAAA;EACtB,cAA2B;EAC3B,iBAAiC;EACjC,eAAc,oBAAI,KAAI,GAAG,YAAW;EAEpC,YACU,IACA,aACA,gBACA,UAAqB;AAHrB,SAAA,KAAA;AACA,SAAA,cAAA;AACA,SAAA,iBAAA;AACA,SAAA,WAAA;AAER,SAAK,cAAc,KAAK,GAAG,MAAM;MAC/B,WAAW,CAAC,IAAI,WAAW,QAAQ;MACnC,OAAO,CAAC,IAAI,WAAW,QAAQ;KAChC;AAED,SAAK,cAAc,KAAK,GAAG,MAAM;MAC/B,MAAM,CAAC,IAAI,WAAW,QAAQ;MAC9B,OAAO,CAAC,IAAI,WAAW,QAAQ;MAC/B,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,IAAI,IAAI,GAAG,WAAW,IAAI,KAAK,WAAW,CAAC,CAAC;MACxF,OAAO,CAAC,IAAI,WAAW,QAAQ;MAC/B,eAAe,CAAC,IAAI,WAAW,QAAQ;MACvC,UAAU,CAAC,GAAG,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC;KAC3E;EACH;EAEA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,aAAY;EACnB;EAEM,kBAAe;;AACnB,UAAI;AACF,aAAK,cAAc,MAAM,KAAK,YAAY,eAAc;AACxD,YAAI,KAAK,aAAa;AACpB,eAAK,YAAY,WAAW;YAC1B,WAAW,KAAK,YAAY,aAAa;YACzC,OAAO,KAAK,YAAY,SAAS;WAClC;QACH;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,+BAA+B,KAAK;AAClD,aAAK,SAAS,KAAK,0BAA0B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC1E;IACF;;EAEM,eAAY;;AAChB,UAAI;AACF,YAAI,KAAK,aAAa;AACpB,eAAK,WAAW,MAAM,KAAK,eAAe,kBAAkB,KAAK,YAAY,EAAE;QACjF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAK,SAAS,KAAK,2BAA2B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC3E;IACF;;EAEM,gBAAa;;AACjB,UAAI,KAAK,YAAY;AAAS;AAE9B,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,YAAY,cAAc,KAAK,YAAY,KAAK;AAC3E,YAAI,SAAS;AACX,eAAK,SAAS,KAAK,gCAAgC,SAAS,EAAE,UAAU,IAAI,CAAE;QAChF,OAAO;AACL,gBAAM,IAAI,MAAM,0BAA0B;QAC5C;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAK,SAAS,KAAK,4BAA4B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC5E;IACF;;EAEA,YAAY,SAAgB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,YAAY,WAAW;MAC1B,MAAM,QAAQ;MACd,OAAO,QAAQ;MACf,MAAM,QAAQ;MACd,OAAO,QAAQ;MACf,eAAe,QAAQ;MACvB,UAAU,QAAQ;KACnB;EACH;EAEA,aAAU;AACR,SAAK,iBAAiB;AACtB,SAAK,YAAY,MAAM;MACrB,UAAU;KACX;EACH;EAEM,cAAW;;AACf,UAAI,KAAK,YAAY,WAAW,CAAC,KAAK;AAAa;AAEnD,UAAI;AACF,YAAI,KAAK,gBAAgB;AAEvB,gBAAM,UAAU,MAAM,KAAK,eAAe,cACxC,KAAK,eAAe,IACpB,KAAK,YAAY,KAAK;AAGxB,cAAI,SAAS;AACX,iBAAK,SAAS,KAAK,gCAAgC,SAAS,EAAE,UAAU,IAAI,CAAE;AAC9E,iBAAK,WAAU;AACf,iBAAK,aAAY;UACnB,OAAO;AACL,kBAAM,IAAI,MAAM,0BAA0B;UAC5C;QACF,OAAO;AAEL,gBAAM,aAAa,iCACd,KAAK,YAAY,QADH;YAEjB,WAAW,KAAK,YAAY;;AAG9B,gBAAM,SAAS,MAAM,KAAK,eAAe,WAAW,UAAU;AAC9D,cAAI,QAAQ;AACV,iBAAK,SAAS,KAAK,8BAA8B,SAAS,EAAE,UAAU,IAAI,CAAE;AAC5E,iBAAK,YAAY,MAAM;cACrB,UAAU;aACX;AACD,iBAAK,aAAY;UACnB,OAAO;AACL,kBAAM,IAAI,MAAM,uBAAuB;UACzC;QACF;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAK,SAAS,KAAK,0BAA0B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC1E;IACF;;EAEM,cAAc,WAAiB;;AACnC,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,eAAe,cAAc,SAAS;AACjE,YAAI,SAAS;AACX,eAAK,SAAS,KAAK,gCAAgC,SAAS,EAAE,UAAU,IAAI,CAAE;AAC9E,eAAK,aAAY;QACnB,OAAO;AACL,gBAAM,IAAI,MAAM,0BAA0B;QAC5C;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAC9C,aAAK,SAAS,KAAK,4BAA4B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC5E;IACF;;;qCAtJW,yBAAsB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,oBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,WAAA,GAAA,CAAA,cAAA,WAAA,GAAA,YAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,aAAA,eAAA,sBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,YAAA,IAAA,mBAAA,SAAA,eAAA,yBAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,WAAA,QAAA,UAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAhN/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,UAAA,EACnB,GAAA,iBAAA,EACS,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA,EAAiB;AAEjD,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,QAAA,CAAA;AACgB,MAAA,qBAAA,YAAA,SAAA,2DAAA;AAAA,eAAY,IAAA,cAAA;MAAe,CAAA;AACzD,MAAA,yBAAA,GAAA,kBAAA,CAAA,EAAwD,GAAA,WAAA;AAC3C,MAAA,iBAAA,GAAA,WAAA;AAAS,MAAA,uBAAA;AACpB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,kBAAA,CAAA,EAAwD,IAAA,WAAA;AAC3C,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACvB,MAAA,oBAAA,IAAA,SAAA,CAAA;AACA,MAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,aAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,UAAA,CAAA;AACE,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAS,EACJ,EACU,EACV;;;;;AAtBD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,WAAA;AAIU,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,YAAA,IAAA,WAAA,MAAA,OAAA,OAAA,QAAA,SAAA,UAAA,CAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,UAAA,IAAA,YAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,SAAA,UAAA,CAAA;AAK0C,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,YAAA,WAAA,IAAA,YAAA,QAAA;;;IAlChE;IAAY;IACZ;IAAmB;IAAA;IAAA;IAAA;IAAA;IAAA;IACnB;IAAa;IAAA;IAAA;IAAA;IACb;IAAkB;IAAA;IAAA;IAClB;IAAc;IACd;IAAe;IACf;IACA;IACA;EAAa,GAAA,QAAA,CAAA,oxCAAA,EAAA,CAAA;;;sEAmNJ,wBAAsB,CAAA;UA/NlC;uBACW,sBAAoB,YAClB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsIT,QAAA,CAAA,sqCAAA,EAAA,CAAA;;;;6EA2EU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,gFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;ACpN4uC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA6D,IAAA,oBAAA,GAAA,eAAA,EAAA;AAAuD,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA,EAAI;;;;;AAAgC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,GAAA;AAAiB,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA,EAAI;;;;;AAAqM,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAAqB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,CAAA;;AAAyC,IAAA,uBAAA;;;;AAAzC,IAAA,oBAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,UAAA,YAAA,QAAA,CAAA;;;;;AAAgJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;AAAqB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,CAAA;;AAAmC,IAAA,uBAAA;;;;AAAnC,IAAA,oBAAA;AAAA,IAAA,6BAAA,IAAA,sBAAA,GAAA,GAAA,UAAA,SAAA,GAAA,CAAA,GAAA,KAAA;;;;;AAAyI,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAAqB,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,CAAA;AAA8B,IAAA,uBAAA;;;;AAA9B,IAAA,oBAAA;AAAA,IAAA,4BAAA,UAAA,OAAA,QAAA,CAAA,CAAA;;;;;AAAoI,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAAqB,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,QAAA,EAAA;AAAwG,IAAA,iBAAA,CAAA;AAAqC,IAAA,uBAAA,EAAO;;;;AAAxG,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,UAAA,MAAA;AAA4D,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,QAAA,GAAA;;;;;AAA8G,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AAA0E,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AAA7vC,IAAA,yBAAA,GAAA,SAAA,EAAA;AAAmH,IAAA,kCAAA,GAAA,EAAA;AAAkD,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;;AAA0J,IAAA,kCAAA,GAAA,EAAA;AAAqD,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;;AAAuJ,IAAA,kCAAA,GAAA,EAAA;AAAoD,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;;AAAiJ,IAAA,kCAAA,IAAA,EAAA;AAAoD,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA;;AAAuR,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA,EAAuD,IAAA,iDAAA,GAAA,GAAA,MAAA,EAAA;AAAiG,IAAA,uBAAA;;;;AAA1zC,IAAA,qBAAA,cAAA,OAAA,OAAA;AAAsrC,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,gBAAA;AAAuF,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,gBAAA;;;AA+E31F,IAAO,0BAAP,MAAO,yBAAuB;EASxB;EACA;EATV,UAA0B,CAAA;EAC1B,mBAA6B,CAAC,QAAQ,WAAW,UAAU,QAAQ;EACnE,UAAU;EACV,gBAAgB;EAChB,kBAAkB;EAClB,iBAAiB;EAEjB,YACU,gBACA,aAAwB;AADxB,SAAA,iBAAA;AACA,SAAA,cAAA;EACP;EAEH,WAAQ;AACN,SAAK,YAAW;EAClB;EAEM,cAAW;;AACf,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,YAAY,eAAc;AAClD,YAAI,CAAC;AAAM,gBAAM,IAAI,MAAM,gBAAgB;AAG3C,aAAK,UAAU,MAAM,KAAK,eAAe,iBAAiB,KAAK,EAAE;AAGjE,aAAK,gBAAgB,MAAM,KAAK,eAAe,uBAAuB,KAAK,EAAE;AAC7E,aAAK,kBAAkB,MAAM,KAAK,eAAe,yBAAyB,KAAK,EAAE;AAGjF,aAAK,iBAAiB,KAAK,QAAQ;MACrC,SAAS,OAAO;AACd,gBAAQ,MAAM,0BAA0B,KAAK;MAC/C;AACE,aAAK,UAAU;MACjB;IACF;;;qCAtCW,0BAAuB,4BAAA,cAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,mBAAA,IAAA,SAAA,WAAA,cAAA,mBAAA,GAAA,OAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,aAAA,IAAA,SAAA,iBAAA,GAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,aAAA,IAAA,GAAA,iBAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,MAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,kBAAA,GAAA,CAAA,YAAA,IAAA,GAAA,YAAA,GAAA,CAAA,gBAAA,SAAA,GAAA,CAAA,gBAAA,QAAA,GAAA,CAAA,gBAAA,QAAA,GAAA,CAAA,kBAAA,IAAA,GAAA,iBAAA,GAAA,CAAA,WAAA,IAAA,GAAA,aAAA,kBAAA,GAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,YAAA,EAAA,GAAA,CAAA,GAAA,eAAA,GAAA,SAAA,GAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,WAAA,EAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA/EvB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,YAAA,CAAA,EAAuC,GAAA,iBAAA,EAA2B,GAAA,gBAAA;AAA4B,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAiB;AAAsC,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EAAsC,GAAA,OAAA,CAAA,EAAwC,GAAA,OAAA,CAAA;AAA2C,MAAA,iBAAA,GAAA,gBAAA;AAAc,MAAA,uBAAA;AAAsB,MAAA,yBAAA,IAAA,OAAA,CAAA;AAA2B,MAAA,iBAAA,EAAA;AAA8B,MAAA,uBAAA,EAAM;AAAkC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,OAAA,CAAA;AAA2C,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAAsB,MAAA,yBAAA,IAAA,OAAA,CAAA;AAA2B,MAAA,iBAAA,EAAA;AAAgC,MAAA,uBAAA,EAAM;AAAkC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,OAAA,CAAA;AAA2C,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAAsB,MAAA,yBAAA,IAAA,OAAA,CAAA;AAA2B,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA,EAAM,EAAoB,EAAkB,EAA6B;AAA6B,MAAA,yBAAA,IAAA,YAAA,CAAA,EAA+B,IAAA,iBAAA,EAA2B,IAAA,gBAAA;AAA4B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAA6B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAwC,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,YAAA;MAAa,CAAA;AAA6C,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAW,EAAqB;AAAsC,MAAA,yBAAA,IAAA,kBAAA;AAA8B,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,IAAA,yCAAA,GAAA,GAAA,OAAA,CAAA,EAA+L,IAAA,2CAAA,IAAA,GAAA,SAAA,EAAA;AAAm6C,MAAA,uBAAA,EAAmB,EAAmB;;;AAA1iF,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,cAAA,QAAA,CAAA,CAAA;AAA2M,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,gBAAA,QAAA,CAAA,CAAA;AAA6M,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,cAAA;AAAie,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAA6K,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,WAAA,CAAA;AAAgM,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,SAAA,CAAA;;;IAT9nD;IAAY;IAAA;IAAA;IAAA;IACZ;IAAa;IAAA;IAAA;IAAA;IACb;IAAc;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACd;IACA;IAAa;IACb;IAAe;IACf;IAAwB;IACxB;IAAgB;EAAA,GAAA,QAAA,CAAA,k8CAAA,EAAA,CAAA;;;sEAiFP,yBAAuB,CAAA;UA5FnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aAAu8F,QAAA,CAAA,27CAAA,EAAA,CAAA;;;;6EA+Et8F,yBAAuB,EAAA,WAAA,2BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;ACpDxB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6E,GAAA,GAAA,EACxE,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA;AAC7D,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,iBAAA;AAAe,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAAmC,IAAA,uBAAA,EAAI;;;;AAD7C,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,KAAA,gBAAA,QAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,KAAA,kBAAA,UAAA;;;;;;AAlC9C,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA6C,GAAA,YAAA,CAAA,EACT,GAAA,iBAAA,EACf,GAAA,gBAAA;AACC,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,UAAA,CAAA;AAA6C,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAiB;IAAA,CAAA;AACrE,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA,EAAW,EACnB;AAEX,IAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,OAAA,CAAA,EACgB,IAAA,OAAA,CAAA,EACH,IAAA,OAAA,CAAA,EACE,IAAA,YAAA,CAAA;AACc,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAClD,IAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,QAAA,CAAA;AACI,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAC7C,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAA0B,IAAA,uBAAA,EAAO,EAC1D;AAER,IAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,YAAA,EAAA;AACe,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AAC5C,IAAA,yBAAA,IAAA,OAAA,CAAA,EAA2B,IAAA,QAAA,CAAA;AACI,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA,EAAO,EAC3D,EACF;AAGR,IAAA,oBAAA,IAAA,mBAAA,EAAA;AAKA,IAAA,qBAAA,IAAA,+CAAA,GAAA,GAAA,OAAA,EAAA;AAKA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA8B,IAAA,KAAA,EAAA,EACqC,IAAA,UAAA,EAAA,EACrB,IAAA,UAAA;AAC9B,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AACpB,IAAA,iBAAA,IAAA,sBAAA;AACF,IAAA,uBAAA,EAAS;AAEX,IAAA,yBAAA,IAAA,KAAA,EAAA,EAAkE,IAAA,UAAA,EAAA,EACvB,IAAA,UAAA;AAC7B,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AACpB,IAAA,iBAAA,IAAA,uBAAA;AACF,IAAA,uBAAA,EAAS,EACP,EACA,EACF,EACW,EACV;;;;AAtC8B,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,eAAA;AAOA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA,gBAAA;AAMjC,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,OAAA,KAAA,eAAA,EAA+B,eAAA,OAAA,KAAA,gBAAA;AAI3B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,KAAA,kBAAA,OAAA,KAAA,gBAAA;AAMD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,qBAAA,uBAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,sBAAA,uBAAA;;;AAuGX,IAAO,0BAAP,MAAO,yBAAuB;EAOd;EANX,OAAoB;EACnB,QAAQ,IAAI,aAAY;EAElC,sBAA8B;EAC9B,uBAA+B;EAE/B,YAAoB,iBAAgC;AAAhC,SAAA,kBAAA;EAAmC;EAEvD,WAAQ;AACN,SAAK,wBAAuB;EAC9B;EAEA,0BAAuB;AACrB,QAAI,CAAC,KAAK;AAAM;AAGhB,SAAK,sBAAsB,KAAK,gBAAgB,iBAAiB,KAAK,KAAK,eAAe;AAC1F,SAAK,uBAAuB,KAAK,gBAAgB,iBAAiB,KAAK,KAAK,gBAAgB;EAC9F;EAEA,kBAAe;AACb,SAAK,MAAM,KAAI;EACjB;;qCAvBW,0BAAuB,4BAAA,eAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,QAAA,EAAA,MAAA,OAAA,GAAA,SAAA,EAAA,OAAA,QAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,mBAAA,IAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,QAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,UAAA,aAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,UAAA,UAAA,GAAA,YAAA,GAAA,MAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,SAAA,GAAA,CAAA,qBAAA,IAAA,SAAA,QAAA,GAAA,CAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAnJhC,MAAA,qBAAA,GAAA,wCAAA,IAAA,GAAA,OAAA,CAAA;;;AAAiC,MAAA,qBAAA,QAAA,IAAA,IAAA;;oBAPjC,cAAY,MACZ,eAAa,SAAA,gBAAA,eAAA,cACb,iBAAe,WAAA,eACf,eAAa,SACb,mBAAmB,GAAA,QAAA,CAAA,8jDAAA,EAAA,CAAA;;;sEAsJV,yBAAuB,CAAA;UA9JnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAwDT,QAAA,CAAA,s9CAAA,EAAA,CAAA;2CA6FQ,MAAI,CAAA;UAAZ;MACS,OAAK,CAAA;UAAd;;;;6EAFU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,kFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;;;;;;;AC9GxB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,eAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA,EAAI;;;;;AAGzB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwE,GAAA,GAAA;AACnE,IAAA,iBAAA,GAAA,0CAAA;AAAwC,IAAA,uBAAA,EAAI;;;;;AAOzC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC5D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;;;;AAA1B,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,eAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;;;;;AACjE,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA;;;;AAA3B,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,gBAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;;AAAqC,IAAA,uBAAA;;;;AAArC,IAAA,oBAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,QAAA,aAAA,OAAA,CAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;;AAA4D,IAAA,uBAAA;;;;AAA5D,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,OAAA,sBAAA,GAAA,GAAA,QAAA,OAAA,KAAA,KAAA,IAAA,KAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAC7C,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,UAAA,EAAA;AACQ,IAAA,qBAAA,SAAA,SAAA,kFAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,QAAA,EAAA,CAAmB;IAAA,CAAA;AACpE,IAAA,iBAAA,GAAA,UAAA;AACF,IAAA,uBAAA,EAAS;;;;;AAIb,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;;AAOA,IAAA,yBAAA,GAAA,qBAAA,EAA2D,GAAA,4BAAA,EAC7B,GAAA,iBAAA;AAExB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,uBAAA;AACE,IAAA,iBAAA,CAAA;;AACF,IAAA,uBAAA,EAAwB;AAE1B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,GAAA,EACrB,GAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAA4D,IAAA,uBAAA,EAAI;AAE5F,IAAA,yBAAA,IAAA,gBAAA,EAAgB,IAAA,UAAA,EAAA;AAC4B,IAAA,qBAAA,SAAA,SAAA,oGAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,QAAA,EAAA,CAAmB;IAAA,CAAA;AACpE,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACM;;;;AAdb,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,iBAAA,GAAA;AAGA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,GAAA,GAAA,QAAA,aAAA,WAAA,GAAA,GAAA;AAIsB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,kBAAA,EAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,OAAA,sBAAA,IAAA,GAAA,QAAA,OAAA,KAAA,KAAA,IAAA,OAAA,EAAA;;;;;AAnDpC,IAAA,kCAAA,CAAA;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,SAAA,IAAA,CAAA;AAEtB,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAQxC,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAuD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;AAEzD,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,iBAAA,IAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,iBAAA,EAAA;AAErB,IAAA,qBAAA,IAAA,0EAAA,IAAA,IAAA,uBAAA,EAAA;AAmBF,IAAA,uBAAA,EAAgB;;;;;AAzDC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,OAAA,mBAAA;AA8BK,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,gBAAA;AACa,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,gBAAA;AAEA,IAAA,oBAAA;AAAA,IAAA,qBAAA,mBAAA,0BAAA,GAAA,GAAA,CAAA,EAAsC,YAAA,CAAA;AAKjC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;;;;;;AAkC5C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA,EACO,GAAA,UAAA,EAAA;AAGtD,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,IAAiB,KAAK,CAAC;IAAA,CAAA;AACV,IAAA,iBAAA,GAAA,KAAA;AAAG,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEQ,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,IAAiB,UAAU,CAAC;IAAA,CAAA;AACf,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;AACtC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEQ,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,IAAiB,aAAa,CAAC;IAAA,CAAA;AAClB,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEQ,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,IAAiB,WAAW,CAAC;IAAA,CAAA;AAChB,IAAA,iBAAA,GAAA,WAAA;AAAS,IAAA,uBAAA,EAAS,EAC5C;;;;AAfI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,aAAA,MAAA,QAAA,YAAA,EAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,aAAA,MAAA,aAAA,YAAA,EAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,aAAA,MAAA,gBAAA,YAAA,EAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,aAAA,MAAA,cAAA,YAAA,EAAA;;;;;AAMZ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,eAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA,EAAI;;;;;AAEzB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,GAAA;AAC5D,IAAA,iBAAA,GAAA,oCAAA;AAAkC,IAAA,uBAAA,EAAI;;;;;AAE3C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiG,GAAA,GAAA;AAC5F,IAAA,iBAAA,GAAA,oCAAA;AAAkC,IAAA,uBAAA,EAAI;;;;;AAOnC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC5D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;;;;AAA1B,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,eAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;;;;;AACjE,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA;;;;AAA3B,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,gBAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;;AAAqC,IAAA,uBAAA;;;;AAArC,IAAA,oBAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,SAAA,aAAA,OAAA,CAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC5D,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAoC,GAAA,QAAA,EAAA;AAEhC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;;;;AAFmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,SAAA,MAAA;AACxB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,QAAA,GAAA;;;;;AAMJ,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsD,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAC1D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAoC,IAAA,iBAAA,CAAA;;AAA2D,IAAA,uBAAA;;;;AAA3D,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,OAAA,sBAAA,GAAA,GAAA,SAAA,OAAA,KAAA,KAAA,IAAA,KAAA;;;;;AAIpC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;;;;;;AAE3C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA6E,IAAA,qBAAA,SAAA,SAAA,2FAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,SAAA,EAAA,CAAkB;IAAA,CAAA;AACtG,IAAA,iBAAA,GAAA,cAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA+E,IAAA,qBAAA,SAAA,SAAA,2FAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,SAAA,EAAA,CAAqB;IAAA,CAAA;AAC3G,IAAA,iBAAA,GAAA,YAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA4E,IAAA,qBAAA,SAAA,SAAA,2FAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,QAAA,CAAoB;IAAA,CAAA;AACvG,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;;;;;;AARnC,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,kEAAA,GAAA,GAAA,UAAA,EAAA,EAA0G,GAAA,kEAAA,GAAA,GAAA,UAAA,EAAA,EAGK,GAAA,kEAAA,GAAA,GAAA,UAAA,EAAA;AAM/G,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAwC,IAAA,qBAAA,SAAA,SAAA,kFAAA;AAAA,YAAA,WAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,SAAA,EAAA,CAAwB;IAAA,CAAA;AACvE,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW,EACxB;;;;AAXkC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,UAAA;AAGD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,aAAA;AAGD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;;;;;AAS7C,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;AACA,IAAA,oBAAA,GAAA,MAAA,EAAA;;;;;;AAeQ,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA2E,IAAA,qBAAA,SAAA,SAAA,0GAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAS,aAAA,UAAA,SAAA,EAAA;AAAkB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC9H,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;;;;;;AAEjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA6E,IAAA,qBAAA,SAAA,SAAA,0GAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAS,aAAA,aAAA,SAAA,EAAA;AAAqB,aAAA,sBAAE,OAAA,gBAAA,CAAwB;IAAA,CAAA;AACnI,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA,EAAW;;;;;;AAWvC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA4E,IAAA,qBAAA,SAAA,SAAA,6GAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,QAAA,CAAoB;IAAA,CAAA;AACvG,IAAA,yBAAA,GAAA,UAAA;AAAU,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAW;;;;;;AAxBrC,IAAA,yBAAA,GAAA,qBAAA,EAA4D,GAAA,4BAAA,EAC9B,GAAA,iBAAA;AAExB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,uBAAA,EAAuB,GAAA,OAAA,EAAA,EACY,GAAA,QAAA,EAAA;AAC+B,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AAC/E,IAAA,qBAAA,GAAA,mFAAA,GAAA,GAAA,UAAA,EAAA,EAA0J,GAAA,mFAAA,GAAA,GAAA,UAAA,EAAA;AAM5J,IAAA,uBAAA,EAAM,EACgB;AAE1B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,GAAA,EACrB,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAAqC,IAAA,uBAAA;AAC/D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;;AAA4D,IAAA,uBAAA,EAAI;AAE5F,IAAA,yBAAA,IAAA,gBAAA;AACE,IAAA,qBAAA,IAAA,oFAAA,GAAA,GAAA,UAAA,EAAA;AAGA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAAwC,IAAA,qBAAA,SAAA,SAAA,oGAAA;AAAA,YAAA,WAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,SAAA,EAAA,CAAwB;IAAA,CAAA;AACvE,IAAA,yBAAA,IAAA,UAAA;AAAU,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA,EAAW,EACxB,EACM;;;;AA1Bb,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,iBAAA,GAAA;AAI4B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,SAAA,MAAA;AAAoC,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,MAAA;AACrB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,UAAA;AAGD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,aAAA;AAOpB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,kBAAA,EAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,sBAAA,IAAA,GAAA,SAAA,aAAA,OAAA,GAAA,EAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,OAAA,sBAAA,IAAA,IAAA,SAAA,OAAA,KAAA,KAAA,IAAA,OAAA,EAAA;AAGe,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA,WAAA;;;;;AAjFnD,IAAA,kCAAA,CAAA;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,SAAA,IAAA,CAAA;AAEtB,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,GAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,GAAA,wDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAQxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAIxD,IAAA,kCAAA,IAAA,EAAA;AACE,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;;AAiBxC,IAAA,qBAAA,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA,EAAqD,IAAA,yDAAA,GAAA,GAAA,MAAA,EAAA;AAEvD,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,iBAAA,IAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,iBAAA,EAAA;AAErB,IAAA,qBAAA,IAAA,0EAAA,IAAA,IAAA,uBAAA,EAAA;AA+BF,IAAA,uBAAA,EAAgB;;;;;AAvFC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,cAAA,OAAA,iBAAA;AAgDK,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,mBAAA,OAAA,cAAA;AACa,IAAA,oBAAA;AAAA,IAAA,qBAAA,oBAAA,OAAA,cAAA;AAEF,IAAA,oBAAA;AAAA,IAAA,qBAAA,mBAAA,0BAAA,GAAA,GAAA,CAAA,EAAoC,YAAA,CAAA;AAK7B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,gBAAA,CAAA;;;;;;AAsClD,IAAA,yBAAA,GAAA,uBAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,gGAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAA,OAAA,eAAwB,IAAI;IAAA,CAAA;AAC9B,IAAA,uBAAA;;;;AAFE,IAAA,qBAAA,QAAA,OAAA,YAAA;;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,mBAAA,EAAA;AAIpD,IAAA,qBAAA,eAAA,SAAA,gFAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAe,OAAA,cAAA,MAAA,CAAqB;IAAA,CAAA;AACtC,IAAA,uBAAA,EAAkB;;;;AAHhB,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,OAAA,cAAA,EAAyB,WAAA,OAAA,iBAAA,KAAA,MAAA,CAAA;;;AAsK7B,IAAO,2BAAP,MAAO,0BAAwB;EAiCzB;EACA;EACA;EACA;EACA;EAnCV,cAA2B;EAC3B,eAA4B;EAC5B,iBAAgC;EAChC,UAAmB;EACX,oBAAyC;EAEjD,iBAAiB,OAAe,CAAA,CAAE;EAClC,UAAU,OAAe,CAAA,CAAE;EAE3B,eAAe,OAA2B,KAAK;EAC/C,kBAAkB,SAAS,MAAK;AAC9B,UAAM,QAAQ,KAAK,QAAO;AAC1B,UAAM,SAAS,KAAK,aAAY;AAChC,QAAI,WAAW,OAAO;AACpB,aAAO;IACT;AACA,WAAO,MAAM,OAAO,UAAQ,KAAK,WAAW,MAAM;EACpD,CAAC;EAED,mBAA6B,CAAC,mBAAmB,oBAAoB,eAAe,QAAQ,SAAS;EACrG,iBAA2B,CAAC,mBAAmB,oBAAoB,eAAe,UAAU,QAAQ,SAAS;EAE7G,sBAAsB,IAAI,mBAAyB,CAAA,CAAE;EACrD,oBAAoB,IAAI,mBAAyB,CAAA,CAAE;EAEvB;EACK;EACP;EACK;EAE/B,YACU,aACA,aACA,gBACA,QACA,UAAqB;AAJrB,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,iBAAA;AACA,SAAA,SAAA;AACA,SAAA,WAAA;AAER,WAAO,MAAK;AACV,YAAM,SAAS,KAAK,aAAY;AAChC,YAAM,aAAa,KAAK,QAAO;AAC/B,UAAI,WAAW,OAAO;AAClB,aAAK,kBAAkB,OAAO;MAClC,OAAO;AACH,aAAK,kBAAkB,OAAO,WAAW,OAAO,UAAQ,KAAK,WAAW,MAAM;MAClF;IACF,CAAC;EACH;EAEM,WAAQ;;AACZ,UAAI;AACF,cAAM,KAAK,gBAAe;AAC1B,YAAI,KAAK,aAAa;AAEpB,eAAK,0BAAyB;AAE9B,gBAAM,KAAK,UAAS;AACpB,kBAAQ,IAAI,2DAAsD;QACpE;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,sCAAiC,KAAK;AACpD,aAAK,SAAS,KAAK,mCAAmC,SAAS,EAAE,UAAU,IAAI,CAAE;MACnF;IACF;;EAEA,kBAAe;AAEb,eAAW,MAAK;AACd,WAAK,iBAAgB;IACvB,GAAG,CAAC;EACN;EAEA,cAAW;AACT,QAAI,KAAK,mBAAmB;AAC1B,WAAK,kBAAkB,YAAW;IACpC;EACF;EAKc,kBAAe;;AAC3B,UAAI;AACF,aAAK,UAAU;AACf,aAAK,cAAc,MAAM,KAAK,YAAY,eAAc;MAC1D,SAAS,OAAO;AACd,gBAAQ,MAAM,+BAA+B,KAAK;AAClD,aAAK,SAAS,KAAK,mCAAmC,SAAS,EAAE,UAAU,IAAI,CAAE;MACnF;AACE,aAAK,UAAU;MACjB;IACF;;EAEQ,4BAAyB;AAC/B,QAAI,CAAC,KAAK;AAAa;AAEvB,YAAQ,IAAI,0DAAmD,KAAK,YAAY,EAAE;AAElF,SAAK,oBAAoB,KAAK,YAAY,OAAO,UAAU,CAAC,aAAoB;AAC9E,UAAI,YAAY,SAAS,UAAU,GAAG;AACpC,cAAM,YAAY,SAAS,OAAO,UAAQ,KAAK,WAAW,WAAW;AACrE,cAAM,UAAU,SAAS,OAAO,UAC9B,KAAK,cAAc,KAAK,aAAa,MACpC,CAAC,YAAY,eAAe,WAAW,EAAmB,SAAS,KAAK,MAAM,CAAC;AAGlF,aAAK,eAAe,IAAI,SAAS;AACjC,aAAK,QAAQ,IAAI,OAAO;AAExB,aAAK,oBAAoB,OAAO;AAEhC,cAAM,wBAAwB,KAAK,oBAAoB,KAAK;AAC5D,YAAI,UAAU,SAAS,yBAAyB,wBAAwB,GAAG;AACzE,eAAK,SAAS,KAAK,GAAG,UAAU,SAAS,qBAAqB,2BAA2B,QAAQ;YAC/F,UAAU;YACV,YAAY,CAAC,kBAAkB;WAChC;QACH;MACF;IACF,CAAC;EACH;EACQ,mBAAgB;AACtB,YAAQ,IAAI,4BAA4B;AACxC,QAAI,KAAK,eAAe;AACtB,WAAK,oBAAoB,OAAO,KAAK;IACvC;AACA,QAAI,KAAK,oBAAoB;AAC3B,WAAK,oBAAoB,YAAY,KAAK;IAC5C;AAEA,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB,OAAO,KAAK;IACrC;AACA,QAAI,KAAK,kBAAkB;AACzB,WAAK,kBAAkB,YAAY,KAAK;IAC1C;AAEA,SAAK,oBAAoB,sBAAsB,CAAC,MAAY,aAAoB;AAC9E,cAAQ,UAAU;QAChB,KAAK;AACH,iBAAO,KAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,QAAO,IAAK;QAC/D,KAAK;AACH,iBAAO,KAAK,QAAQ;QACtB,KAAK;QACL,KAAK;AACH,iBAAQ,KAAa,QAAQ,GAAG,YAAW,KAAM;QACnD;AACE,iBAAQ,KAAa,QAAQ,KAAK;MACtC;IACF;AAEA,SAAK,kBAAkB,sBAAsB,CAAC,MAAY,aAAoB;AAC5E,cAAQ,UAAU;QAChB,KAAK;AACH,iBAAO,KAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE,QAAO,IAAK;QAC/D,KAAK;AACH,iBAAO,KAAK,QAAQ;QACtB,KAAK;QACL,KAAK;QACL,KAAK;AACH,iBAAQ,KAAa,QAAQ,GAAG,YAAW,KAAM;QACnD;AACE,iBAAQ,KAAa,QAAQ,KAAK;MACtC;IACF;EACF;EAGM,YAAS;;AACb,UAAI,CAAC,KAAK;AAAa;AACvB,cAAQ,IAAI,4BAAqB;AACjC,WAAK,UAAU;AACf,UAAI;AACF,cAAM,KAAK,YAAY,YAAW;AAElC,cAAM,CAAC,WAAW,QAAQ,IAAI,MAAM,QAAQ,IAAI;UAC9C,KAAK,YAAY,kBAAiB;UAClC,KAAK,YAAY,eAAe,KAAK,YAAY,EAAE;SACpD;AAED,aAAK,eAAe,IAAI,SAAS;AACjC,aAAK,QAAQ,IAAI,QAAQ;AAEzB,aAAK,oBAAoB,OAAO;AAEhC,mBAAW,MAAK;AACd,eAAK,iBAAgB;QACvB,GAAG,CAAC;AAEJ,aAAK,SAAS,KAAK,mBAAmB,SAAS,EAAE,UAAU,IAAI,CAAE;MACnE,SAAS,OAAY;AACnB,gBAAQ,MAAM,+BAA0B,KAAK;AAC7C,aAAK,SAAS,KAAK,MAAM,WAAW,wBAAwB,SAAS,EAAE,UAAU,IAAI,CAAE;MACzF;AACE,aAAK,UAAU;MACjB;IACF;;EAEM,WAAW,QAAc;;AAC7B,UAAI,CAAC,KAAK;AAAa;AAEvB,UAAI;AACF,cAAM,KAAK,YAAY,WAAW,QAAQ,KAAK,YAAY,EAAE;AAC7D,aAAK,SAAS,KAAK,8BAA8B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC9E,SAAS,OAAY;AACnB,gBAAQ,MAAM,yBAAyB,KAAK;AAC5C,aAAK,SAAS,KAAK,MAAM,WAAW,yBAAyB,SAAS,EAAE,UAAU,IAAI,CAAE;MAC1F;IACF;;EAEM,UAAU,QAAc;;AAC5B,UAAI;AACF,cAAM,KAAK,YAAY,UAAU,MAAM;AACvC,aAAK,SAAS,KAAK,6BAA6B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC7E,SAAS,OAAY;AACnB,gBAAQ,MAAM,wBAAwB,KAAK;AAC3C,aAAK,SAAS,KAAK,MAAM,WAAW,wBAAwB,SAAS,EAAE,UAAU,IAAI,CAAE;MACzF;IACF;;EAEM,aAAa,QAAc;;AAC/B,UAAI;AACF,cAAM,KAAK,YAAY,aAAa,MAAM;AAC1C,aAAK,SAAS,KAAK,+BAA+B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC/E,SAAS,OAAY;AACnB,gBAAQ,MAAM,0BAA0B,KAAK;AAC7C,aAAK,SAAS,KAAK,MAAM,WAAW,2BAA2B,SAAS,EAAE,UAAU,IAAI,CAAE;MAC5F;IACF;;EAEA,eAAe,MAAU;AACvB,SAAK,eAAe;EACtB;EAEA,kBAAe;AACb,SAAK,eAAe;EACtB;EAEM,SAAS,QAAc;;AAC3B,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,eAAe,yBAAyB,MAAM;AACxE,cAAM,KAAK,OAAO,SAAS,CAAC,cAAc,UAAU,YAAY,OAAO,EAAE,CAAC;MAC5E,SAAS,OAAY;AACnB,gBAAQ,MAAM,uBAAuB,KAAK;AAC1C,aAAK,SAAS,KAAK,MAAM,WAAW,uBAAuB,SAAS,EAAE,UAAU,IAAI,CAAE;MACxF;IACF;;EAEA,gBAAgB,QAAc;AAC5B,SAAK,iBAAiB;EACxB;EAEA,mBAAgB;AACd,SAAK,iBAAiB;EACxB;EAEA,cAAc,OAAW;EACzB;EAEA,cAAc,QAAgB,MAAU;AACtC,WAAO,KAAK;EACd;;qCAtQW,2BAAwB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,WAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;;;;;;;;;;;;;;AA3YjC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,eAAA,EAClB,GAAA,WAAA,CAAA,EACmC,GAAA,OAAA,CAAA,EACjB,GAAA,OAAA,CAAA,EACM,GAAA,IAAA;AAC3B,MAAA,iBAAA,GAAA,0BAAA;AAAwB,MAAA,yBAAA,GAAA,QAAA,CAAA;AAAiE,MAAA,iBAAA,GAAA,QAAA;AAAC,MAAA,uBAAA,EAAO;AACrG,MAAA,yBAAA,GAAA,UAAA,CAAA;AAAwC,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,UAAA;MAAW,CAAA;AAC1D,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAW,EACrB;AAEX,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAA+C,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAKyB,IAAA,mDAAA,IAAA,GAAA,gBAAA,EAAA;AAkE1E,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAA6E,IAAA,OAAA,CAAA,EAC9C,IAAA,OAAA,CAAA,EACM,IAAA,IAAA;AAC3B,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,yBAAA,IAAA,QAAA,CAAA;AAAiE,MAAA,iBAAA,IAAA,QAAA;AAAC,MAAA,uBAAA,EAAO;AAC/F,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAwC,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,UAAA;MAAW,CAAA;AAC1D,MAAA,yBAAA,IAAA,UAAA;AAAU,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAW,EACrB;AAGX,MAAA,qBAAA,IAAA,0CAAA,IAAA,GAAA,OAAA,EAAA,EAA2D,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAqBZ,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAIkB,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAGgC,IAAA,mDAAA,IAAA,GAAA,gBAAA,EAAA;AAgGnG,MAAA,uBAAA,EAAM,EACE;AAGZ,MAAA,qBAAA,IAAA,0DAAA,GAAA,GAAA,uBAAA,EAAA,EAGgC,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAUlC,MAAA,uBAAA;;;AAxOqC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,CAAA;AAQvB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,eAAA,EAAA,WAAA,CAAA;AAIS,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,eAAA,EAAA,SAAA,CAAA;AAiEO,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,CAAA,EAAc,YAAA,IAAA,QAAA,EAAA,WAAA,CAAA;AASL,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA,EAAA,SAAA,CAAA;AAqBzB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,QAAA,EAAA,WAAA,CAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,gBAAA,EAAA,WAAA,KAAA,IAAA,QAAA,EAAA,SAAA,CAAA;AAIS,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,gBAAA,EAAA,SAAA,CAAA;AAiGlB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA;AAKG,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;;;IArPR;IAAY;IAAA;IAAA;IAAA;IAAA;IACZ;IACA;IAAa;IAAA;IACb;IAAc;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACd;IACA;IAAe;IAAA;IACf;IAAa;IACb;IACA;IACA;IAAgB;IAChB;IAAwB;IACxB;IACA;IACA;IAAa;IAAA;IACb;IAAkB;IAClB;IAAkB;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA,GAAA,QAAA,CAAA,owGAAA,EAAA,CAAA;;;sEA8YT,0BAAwB,CAAA;UAjapC;uBACW,wBAAsB,YACpB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4OT,QAAA,CAAA,kqFAAA,EAAA,CAAA;iIA2L2B,eAAa,CAAA;UAAxC;WAAU,eAAe;MACO,oBAAkB,CAAA;UAAlD;WAAU,oBAAoB;MACL,aAAW,CAAA;UAApC;WAAU,aAAa;MACO,kBAAgB,CAAA;UAA9C;WAAU,kBAAkB;;;;6EA9BlB,0BAAwB,EAAA,WAAA,4BAAA,UAAA,oFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;ACpa/B,IAAO,kBAAP,MAAO,iBAAe;;qCAAf,kBAAe;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,CAAA,SAAA,gBAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACvB5B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,YAAA,CAAA,EACA,GAAA,iBAAA,EACZ,GAAA,gBAAA;AACC,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAiB;AAEnD,MAAA,yBAAA,GAAA,kBAAA,EAAkB,GAAA,GAAA;AACb,MAAA,iBAAA,GAAA,gHAAA;AAA8G,MAAA,uBAAA,EAAI,EACpG;AAGrB,MAAA,yBAAA,GAAA,eAAA,EAAe,GAAA,WAAA,CAAA;AAEX,MAAA,oBAAA,IAAA,sBAAA;AACF,MAAA,uBAAA;AAIA,MAAA,yBAAA,IAAA,WAAA,CAAA;AACE,MAAA,oBAAA,IAAA,oBAAA;AACF,MAAA,uBAAA,EAAU,EACI;;;IDPd;IACA;IAAa;IAAA;IAAA;IAAA;IACb;IAAa;IAAA;IACb;IACA;EAAwB,GAAA,QAAA,CAAA,mYAAA,EAAA,CAAA;;;sEAMf,iBAAe,CAAA;UAd3B;uBACW,cAAY,YACV,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;OACD,UAAA,s3BAAA,QAAA,CAAA,oXAAA,EAAA,CAAA;;;;6EAIU,iBAAe,EAAA,WAAA,mBAAA,UAAA,yDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}