import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, AfterViewInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatBadgeModule } from '@angular/material/badge';
import { MatListModule } from '@angular/material/list';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { DriverSelectionDialogComponent } from './driver-selection-dialog/driver-selection-dialog.component';
import { AdminReportsComponent } from './admin-reports/admin-reports.component';
import { UserDetailsDialogComponent } from './user-details-dialog/user-details-dialog.component';
import { RideDetailComponent } from '../../../shared/components/ride-detail/ride-detail.component';
import { SquareSandboxComponent } from './square-sandbox/square-sandbox.component';
import { StripePaymentComponent } from './stripe-payment/stripe-payment.component';
import { RidePricingComponent } from './ride-pricing/ride-pricing.component';
import { AdminRideCreateDialogComponent } from './admin-ride-create-dialog/admin-ride-create-dialog.component';

import { User, UserRole } from '../../../core/models/user.model';
import { Ride, RideStatus } from '../../../core/models/ride.model';
import { SystemStatistics } from '../../../core/models/statistics.model';
import { RideService } from '../../../core/services/ride.service';
import { StatisticsService } from '../../../core/services/statistics.service';
import { UserService } from '../../../core/services/user.service';
import { ThemePalette } from '@angular/material/core';
import { DriverPaymentComponent } from './driver-payment/driver-payment.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-admin',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatSortModule,
    MatPaginatorModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatBadgeModule,
    MatListModule,
    MatGridListModule,
    MatTooltipModule,
    MatDialogModule,
    MatExpansionModule,
    AdminReportsComponent,
    DriverSelectionDialogComponent,
    UserDetailsDialogComponent,
    RideDetailComponent,
    SquareSandboxComponent,
    DriverPaymentComponent,
    StripePaymentComponent,
    RidePricingComponent,
    AdminRideCreateDialogComponent
  ],
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss']
})
export class AdminComponent implements OnInit, AfterViewInit, OnDestroy {
  // User Management
  usersSignal = signal<User[]>([]);
  users: User[] = []; // Keep for backward compatibility with existing code
  userDataSource = new MatTableDataSource<User>([]);
  userDisplayedColumns: string[] = ['email', 'full_name', 'role', 'created_at', 'status', 'actions'];
  userRoleFilter: UserRole | '' = '';
  userSearchTerm: string = '';
  loadingUsers: boolean = false;

  // Signal-based filtering for mobile
  userRoleFilterSignal = signal<UserRole | 'all'>('all');
  userSearchTermSignal = signal<string>('');
  filteredUsers = computed(() => {
    const allUsers = this.usersSignal();
    const roleFilter = this.userRoleFilterSignal();
    const searchTerm = this.userSearchTermSignal().toLowerCase().trim();

    let filtered = allUsers;

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.full_name && user.full_name.toLowerCase().includes(searchTerm)) ||
        (user.email && user.email.toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  });

  // Ride Management
  ridesSignal = signal<Ride[]>([]);
  rides: Ride[] = []; // Keep for backward compatibility with existing code
  rideDataSource = new MatTableDataSource<Ride>([]);
  rideDisplayedColumns: string[] = [
    'rider_id',
    'driver_id',
    'pickup_location',
    'dropoff_location',
    //'fare',
    'status',
    'created_at',
    'actions'
  ];
  rideStatusFilter: RideStatus | '' = '';
  rideSearchTerm: string = '';
  loadingRides: boolean = false;
  selectedRideId: string | null = null;
  lastRideRefreshTime: Date = new Date();

  // Signal-based filtering for mobile
  rideStatusFilterSignal = signal<RideStatus | 'all'>('all');
  rideSearchTermSignal = signal<string>('');
  filteredRides = computed(() => {
    const allRides = this.ridesSignal();
    const statusFilter = this.rideStatusFilterSignal();
    const searchTerm = this.rideSearchTermSignal().toLowerCase().trim();

    let filtered = allRides;

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ride => ride.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(ride =>
        (ride.pickup_location && ride.pickup_location.toLowerCase().includes(searchTerm)) ||
        (ride.dropoff_location && ride.dropoff_location.toLowerCase().includes(searchTerm)) ||
        (this.getUserName(ride.rider_id) && this.getUserName(ride.rider_id).toLowerCase().includes(searchTerm)) ||
        (ride.driver_id && this.getUserName(ride.driver_id) && this.getUserName(ride.driver_id).toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  });

  // Statistics
  statistics: SystemStatistics | null = null;
  loadingStatistics: boolean = false;

  // Cache for user names to avoid repeated lookups
  private userNameCache: { [key: string]: string } = {};
  private userNameLoadingCache: { [key: string]: Promise<void> } = {};

  private ridesSubscription: Subscription | null = null;
  private usersSubscription: Subscription | null = null;

  @ViewChild('userSort') userSort!: MatSort;
  @ViewChild('userPaginator') userPaginator!: MatPaginator;
  @ViewChild('rideSort') rideSort!: MatSort;
  @ViewChild('ridePaginator') ridePaginator!: MatPaginator;

  constructor(
    private userService: UserService,
    private rideService: RideService,
    private statisticsService: StatisticsService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  // Flag to track when data loading is complete
  private dataLoadingComplete = false;

  async ngOnInit() {
    console.log('Admin Component ngOnInit');
    try {
      this.setupUserSubscription();
      await Promise.all([
        this.initialLoadRides(),
        this.loadStatistics()
      ]);
      this.dataLoadingComplete = true;

      // If AfterViewInit has already run, set up the paginators now
      if (this.userPaginator && this.ridePaginator) {
        this.setupPaginators();
      }

      // Subscribe to ride updates instead of using interval
      this.setupRideSubscription();
    } catch (error) {
      console.error('Error loading data:', error);
      this.snackBar.open('Failed to load data', 'Close', { duration: 3000 });
    }
  }

  // New method for initial load
  private async initialLoadRides(): Promise<void> {
    this.loadingRides = true;
    try {
      this.rides = await this.rideService.getAllRides();
      this.rideDataSource.data = this.rides;
      this.rideDataSource.sort = this.rideSort;
      this.rideDataSource.paginator = this.ridePaginator;
      // Apply any existing filters
      if (this.rideStatusFilter || this.rideSearchTerm) {
        this.applyRideFilters();
      }

      this.lastRideRefreshTime = new Date();
    } catch (error) {
      console.error('Error loading rides:', error);
      throw error;
    } finally {
      this.loadingRides = false;
    }

      this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;
  }

  private setupRideSubscription(): void {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }

    this.ridesSubscription = this.rideService.rides$.subscribe(rides => {
      this.rides = rides;
      this.ridesSignal.set(rides); // Update the signal
      this.rideDataSource.data = rides;

      if (this.dataLoadingComplete) {
        this.rideDataSource.sort = this.rideSort;
        this.rideDataSource.paginator = this.ridePaginator;
      }

      if (this.rideStatusFilter || this.rideSearchTerm) {
        this.applyRideFilters();
      }

      this.lastRideRefreshTime = new Date();
    });
  }

  ngAfterViewInit() {
    // If data is already loaded, set up paginators immediately
    if (this.dataLoadingComplete) {
      this.setupPaginators();
    }
    // Otherwise, the setupPaginators will be called when data loading completes in ngOnInit
  }

  // Separate method to set up paginators to avoid code duplication
  private setupPaginators() {
    // Connect user data source to sort and paginator
    this.userDataSource.sort = this.userSort;
    this.userDataSource.paginator = this.userPaginator;

    // Connect ride data source to sort and paginator
    this.rideDataSource.sort = this.rideSort;
    this.rideDataSource.paginator = this.ridePaginator;

    // Custom filter predicate for user filtering
    this.userDataSource.filterPredicate = (data: User, filter: string) => {
      const searchTerms = JSON.parse(filter);
      let roleMatch = true;
      let searchMatch = true;

      if (searchTerms.role) {
        roleMatch = data.role === searchTerms.role;
      }

      if (searchTerms.searchTerm) {
        const term = searchTerms.searchTerm.toLowerCase();
        const emailMatch = data.email.toLowerCase().includes(term);
        const nameMatch = data.full_name ? data.full_name.toLowerCase().includes(term) : false;
        searchMatch = emailMatch || nameMatch;
      }

      return roleMatch && searchMatch;
    };


    // Custom sort function for users
    this.userDataSource.sortingDataAccessor = (item: User, property: string) => {
      switch (property) {
        case 'created_at':
          return new Date(item.created_at).getTime();
        case 'full_name':
          return item.full_name?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };

    // Custom filter predicate for ride filtering
    this.rideDataSource.filterPredicate = (data: Ride, filter: string) => {
      const searchTerms = JSON.parse(filter);
      let statusMatch = true;
      let searchMatch = true;

      if (searchTerms.status) {
        statusMatch = data.status === searchTerms.status;
      }

      if (searchTerms.searchTerm) {
        const term = searchTerms.searchTerm.toLowerCase();
        const pickupMatch = data.pickup_location.toLowerCase().includes(term);
        const dropoffMatch = data.dropoff_location.toLowerCase().includes(term);
        const riderName = (this.getUserName(data.rider_id) || '').toLowerCase().includes(term);
        //const driverName = data.driver_id ? (this.getUserName(data.driver_id) || '').toLowerCase() : '';
     
        searchMatch = pickupMatch || dropoffMatch || riderName;
      }

      return statusMatch && searchMatch;
    };

    // Custom sort function for rides
    this.rideDataSource.sortingDataAccessor = (item: Ride, property: string) => {
       console.log("SORTING ASSESOR ",property);
      switch (property) {
        case 'rider_id':
          return this.getUserName(item.rider_id).toLowerCase();
        case 'driver_id':
          return item.driver_id ? this.getUserName(item.driver_id).toLowerCase() : 'zzz'; // Sort empty values last
        case 'created_at':
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        default:
          return (item as any)[property] || '';
      }
    };

  }

  // User Management Methods
  private setupUserSubscription(): void {
    this.loadingUsers = true;
    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
    // Assuming userService has a users$ observable that provides realtime updates
    this.usersSubscription = this.userService.users$.subscribe(users => {
      this.users = users;
      this.usersSignal.set(users); // Update the signal
      this.userDataSource.data = users;
      if (this.dataLoadingComplete) { // to avoid errors on init
          this.userDataSource.sort = this.userSort;
          this.userDataSource.paginator = this.userPaginator;
      }
      if (this.userRoleFilter || this.userSearchTerm) {
        this.applyUserFilters();
      }
      this.loadingUsers = false;
    }, (error: any) => {
      console.error('Error loading users:', error);
      this.snackBar.open('Failed to load users', 'Close', { duration: 3000 });
      this.loadingUsers = false;
    });
  }

  applyUserFilters() {
    const filterValue = JSON.stringify({
      role: this.userRoleFilter,
      searchTerm: this.userSearchTerm
    });

    this.userDataSource.filter = filterValue;

    // Make sure paginator exists and is properly connected before using it
    if (this.userDataSource.paginator && this.dataLoadingComplete) {
      this.userDataSource.paginator.firstPage();
    }
  }

  getRoleDisplayName(role: UserRole): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  async approveDriver(userId: string) {
    try {
      const success = await this.userService.approveDriver(userId);
      if (success) {
        this.snackBar.open('Driver approved successfully', 'Close', { duration: 3000 });
      } else {
        throw new Error('Failed to approve driver');
      }
    } catch (error) {
      console.error('Error approving driver:', error);
      this.snackBar.open('Failed to approve driver', 'Close', { duration: 3000 });
    }
  }

  openUserDetails(user: User): void {
    this.dialog.open(UserDetailsDialogComponent, {
      width: '500px',
      data: user
    });
  }

  getStatusDisplay(user: any): string {

    if (user.role === 'driver') {
      return user.is_approved ? 'Approved' : 'Pending Approval';
    }
    if( user.role=== 'admin' ){
      return user.is_approved ? 'Active' : 'Inactive';
    }
    return 'Active'; // For riders and admins
  }

  // Ride Management Methods
  async loadRides() {
    this.loadingRides = true;
    try {
      // Force a refresh from the service
      await this.rideService.getAllRides();
      this.lastRideRefreshTime = new Date();
    } catch (error) {
      console.error('Error loading rides:', error);
      this.snackBar.open('Failed to load rides', 'Close', { duration: 3000 });
    } finally {
      this.loadingRides = false;
    }
  }

  applyRideFilters() {
    const filterValue = JSON.stringify({
      status: this.rideStatusFilter,
      searchTerm: this.rideSearchTerm
    });

    this.rideDataSource.filter = filterValue;

    // Make sure paginator exists and is properly connected before using it
    if (this.rideDataSource.paginator && this.dataLoadingComplete) {
      this.rideDataSource.paginator.firstPage();
    }
  }

  async updateRideStatus(rideId: string, status: RideStatus) {
    try {
      const success = await this.rideService.updateRideStatus(rideId, status);
      if (success) {
        this.snackBar.open(`Ride status updated to ${this.getStatusDisplayName(status)}`, 'Close', { duration: 3000 });
     //   await this.loadRides();
      } else {
        throw new Error('Failed to update ride status');
      }
    } catch (error) {
      console.error('Error updating ride status:', error);
      this.snackBar.open('Failed to update ride status', 'Close', { duration: 3000 });
    }
  }

  openDriverSelectionDialog(rideId: string): void {
    const dialogRef = this.dialog.open(DriverSelectionDialogComponent, {
      width: '500px',
      data: { drivers: this.users.filter(user => user.role === 'driver' && user.is_approved) }
    });

    dialogRef.afterClosed().subscribe(async (driverId: string) => {
      if (driverId) {
        try {
          const success = await this.rideService.assignRideToDriver(rideId, driverId);
          if (success) {
            this.snackBar.open('Driver assigned successfully', 'Close', { duration: 3000 });
          //  await this.loadRides();
          } else {
            throw new Error('Failed to assign driver');
          }
        } catch (error) {
          console.error('Error assigning driver:', error);
          this.snackBar.open('Failed to assign driver', 'Close', { duration: 3000 });
        }
      }
    });
  }

  viewRideDetails(rideId: string): void {
    this.selectedRideId = rideId;
  }

  closeRideDetails(): void {
    this.selectedRideId = null;
  }

  onRideUpdated(updatedRide: Ride): void {
    // The realtime subscription will handle the update automatically.
  }

  openCreateRideDialog(): void {
    const dialogRef = this.dialog.open(AdminRideCreateDialogComponent, {
      width: '600px'
    });

    dialogRef.afterClosed().subscribe((createdRide: Ride) => {
      if (createdRide) {
        this.snackBar.open('Ride created successfully', 'Close', { duration: 3000 });
        // The ride will be added automatically through the realtime subscription
      }
    });
  }

  // Statistics Methods
  async loadStatistics() {
    this.loadingStatistics = true;
    try {
      this.statistics = await this.statisticsService.generateSystemStatistics();
    } catch (error) {
      console.error('Error loading statistics:', error);
      this.snackBar.open('Failed to load statistics', 'Close', { duration: 3000 });
    } finally {
      this.loadingStatistics = false;
    }
  }

  // Helper Methods
  getStatusDisplayName(status: RideStatus): string {
    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  getStatusColor(status: RideStatus): ThemePalette {
    const statusColors: { [key in RideStatus]: ThemePalette } = {
      'requested': 'warn',
      'assigned': 'primary',
      'in-progress': 'accent',
      'completed': 'primary',
      'canceled': 'warn'
    };
    return statusColors[status] || 'primary';
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  getUserName(userId: string | undefined): string {
    if (!userId) return 'N/A';

    const user = this.users.find(x => x.id === userId);
    return user?.full_name || userId || 'N/A';
  }

  trackByUserId(index: number, item: User): string {
    return item.id;
  }

  trackByRideId(index: number, item: Ride): string {
    return item.id;
  }

  /**
   * Clean up resources when component is destroyed
   */
  ngOnDestroy(): void {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
    if (this.usersSubscription) {
      this.usersSubscription.unsubscribe();
    }
  }
}


// rider=51873bf3-4188-4d74-a54e-5d2b7ccf2e5c

// driver=b5efc0aa-e6f5-4301-9f03-b3092e0dc973


